/* Modern Security Interface Styles */
/* تصميم واجهة الأمان الحديثة */

@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

:root {
    --primary-gradient: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
    --secondary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --danger-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    --success-gradient: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.7);
    --text-muted: rgba(255, 255, 255, 0.5);
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    min-height: 100vh;
    overflow-x: hidden;
    color: var(--text-primary);
}

/* Glass Morphism Effects */
.glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.glass-button {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
}

.glass-button:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(0, 212, 255, 0.5);
    transform: translateY(-2px);
}

/* Advanced Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
    }
    50% {
        box-shadow: 0 0 30px rgba(0, 212, 255, 0.6);
    }
}

/* Particle Background */
.particles-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    opacity: 0.1;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--primary-gradient);
    border-radius: 50%;
    animation: particleFloat 8s linear infinite;
}

@keyframes particleFloat {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

/* Advanced Card Styles */
.modern-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 30px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
}

.modern-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.6s;
}

.modern-card:hover::before {
    left: 100%;
}

.modern-card:hover {
    transform: translateY(-10px) scale(1.02);
    border-color: rgba(0, 212, 255, 0.5);
    box-shadow: 0 20px 40px rgba(0, 212, 255, 0.2);
}

/* Gradient Text */
.gradient-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Advanced Buttons */
.modern-btn {
    background: var(--primary-gradient);
    border: none;
    border-radius: 15px;
    padding: 15px 30px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
}

.modern-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.modern-btn:hover::before {
    left: 100%;
}

.modern-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(0, 212, 255, 0.4);
}

.modern-btn:active {
    transform: translateY(-1px);
}

/* Status Indicators */
.status-online {
    color: #00ff88;
    text-shadow: 0 0 10px #00ff88;
}

.status-offline {
    color: #ff6b6b;
    text-shadow: 0 0 10px #ff6b6b;
}

.status-warning {
    color: #ffa726;
    text-shadow: 0 0 10px #ffa726;
}

/* Progress Bars */
.modern-progress {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: progressShine 2s infinite;
}

@keyframes progressShine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Notification Styles */
.modern-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    padding: 20px;
    color: var(--text-primary);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    animation: slideInRight 0.3s ease;
}

.notification-success {
    border-left: 4px solid #00ff88;
}

.notification-error {
    border-left: 4px solid #ff6b6b;
}

.notification-warning {
    border-left: 4px solid #ffa726;
}

.notification-info {
    border-left: 4px solid #00d4ff;
}

/* Loading Animations */
.modern-loader {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(0, 212, 255, 0.3);
    border-top: 3px solid #00d4ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .modern-card {
        padding: 25px;
    }
}

@media (max-width: 768px) {
    .modern-card {
        padding: 20px;
        border-radius: 15px;
    }
    
    .modern-btn {
        padding: 12px 24px;
        border-radius: 12px;
    }
}

@media (max-width: 480px) {
    .modern-card {
        padding: 15px;
        border-radius: 12px;
    }
    
    .modern-btn {
        padding: 10px 20px;
        font-size: 14px;
    }
}

/* Dark Mode Enhancements */
@media (prefers-color-scheme: dark) {
    :root {
        --glass-bg: rgba(255, 255, 255, 0.03);
        --glass-border: rgba(255, 255, 255, 0.08);
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .modern-card {
        border-width: 2px;
    }
    
    .modern-btn {
        border: 2px solid rgba(255, 255, 255, 0.3);
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Print Styles */
@media print {
    .modern-card {
        background: white;
        color: black;
        border: 1px solid #ccc;
    }
    
    .gradient-text {
        color: black;
        -webkit-text-fill-color: black;
    }
}
