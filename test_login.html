<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - اختبار تسجيل الدخول</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .test-container {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 40px;
            max-width: 500px;
            width: 100%;
            backdrop-filter: blur(20px);
            text-align: center;
        }
        
        .test-title {
            font-size: 28px;
            font-weight: 900;
            margin-bottom: 30px;
            color: #3182ce;
        }
        
        .test-info {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: right;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .info-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .info-label {
            color: rgba(255, 255, 255, 0.8);
        }
        
        .info-value {
            color: #3182ce;
            font-weight: 600;
            font-family: monospace;
        }
        
        .test-btn {
            background: linear-gradient(135deg, #1a365d, #2c5282);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(26, 54, 93, 0.4);
        }
        
        .success-btn {
            background: linear-gradient(135deg, #48bb78, #38a169);
        }
        
        .error-btn {
            background: linear-gradient(135deg, #f56565, #e53e3e);
        }
        
        .result-area {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            min-height: 100px;
            text-align: right;
        }
        
        .result-success {
            color: #48bb78;
            border: 1px solid #48bb78;
        }
        
        .result-error {
            color: #f56565;
            border: 1px solid #f56565;
        }
        
        .password-display {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 18px;
            letter-spacing: 2px;
            color: #3182ce;
            border: 1px solid rgba(49, 130, 206, 0.3);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🔐 اختبار كلمة المرور الجديدة</h1>
        
        <div class="test-info">
            <div class="info-item">
                <span class="info-label">اسم المستخدم:</span>
                <span class="info-value">admin</span>
            </div>
            <div class="info-item">
                <span class="info-label">كلمة المرور الجديدة:</span>
                <span class="info-value password-display">SHeh#1981</span>
            </div>
            <div class="info-item">
                <span class="info-label">حالة النظام:</span>
                <span class="info-value" id="systemStatus">جاري التحقق...</span>
            </div>
        </div>
        
        <div>
            <button class="test-btn" onclick="testPassword()">🧪 اختبار كلمة المرور</button>
            <button class="test-btn" onclick="resetSystem()">🔄 إعادة تهيئة النظام</button>
            <button class="test-btn success-btn" onclick="goToLogin()">🚪 الذهاب لتسجيل الدخول</button>
        </div>
        
        <div class="result-area" id="resultArea">
            <p>اضغط على "اختبار كلمة المرور" للتحقق من عمل النظام</p>
        </div>
    </div>
    
    <script src="app_core.js"></script>
    <script>
        // Wait for system to be ready
        document.addEventListener('egbankReady', function() {
            console.log('EG BANK system ready for testing');
            updateSystemStatus();
        });
        
        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded');
            
            // If system is already ready
            if (window.EGBankApp && window.EGBankApp.isInitialized) {
                updateSystemStatus();
            }
        });
        
        function updateSystemStatus() {
            try {
                const statusElement = document.getElementById('systemStatus');
                if (window.EGBankApp && window.EGBankApp.isInitialized) {
                    statusElement.textContent = 'جاهز ✅';
                    statusElement.style.color = '#48bb78';
                } else {
                    statusElement.textContent = 'غير جاهز ❌';
                    statusElement.style.color = '#f56565';
                }
            } catch (error) {
                console.error('Error updating system status:', error);
            }
        }
        
        async function testPassword() {
            const resultArea = document.getElementById('resultArea');
            resultArea.innerHTML = '<p>🔄 جاري اختبار كلمة المرور...</p>';
            
            try {
                // Test login with new password
                const result = await window.egbank.login('admin', 'SHeh#1981');
                
                if (result.success) {
                    resultArea.className = 'result-area result-success';
                    resultArea.innerHTML = `
                        <h3>✅ نجح الاختبار!</h3>
                        <p><strong>المستخدم:</strong> ${result.user.fullName}</p>
                        <p><strong>الدور:</strong> ${result.user.role}</p>
                        <p><strong>الصلاحيات:</strong> ${result.user.permissions.length} صلاحية</p>
                        <p><strong>الحالة:</strong> ${result.user.status}</p>
                        <p style="margin-top: 15px; color: #48bb78;">✨ كلمة المرور تعمل بشكل صحيح!</p>
                    `;
                } else {
                    resultArea.className = 'result-area result-error';
                    resultArea.innerHTML = `
                        <h3>❌ فشل الاختبار!</h3>
                        <p><strong>السبب:</strong> ${result.message}</p>
                        <p style="margin-top: 15px;">جرب إعادة تهيئة النظام</p>
                    `;
                }
            } catch (error) {
                console.error('Test error:', error);
                resultArea.className = 'result-area result-error';
                resultArea.innerHTML = `
                    <h3>❌ خطأ في الاختبار!</h3>
                    <p><strong>التفاصيل:</strong> ${error.message}</p>
                    <p style="margin-top: 15px;">تأكد من تحميل النظام بشكل صحيح</p>
                `;
            }
        }
        
        function resetSystem() {
            const resultArea = document.getElementById('resultArea');
            resultArea.innerHTML = '<p>🔄 جاري إعادة تهيئة النظام...</p>';
            
            try {
                // Clear all localStorage data
                localStorage.clear();
                
                // Reinitialize the system
                if (window.EGBankApp) {
                    window.EGBankApp.setupDefaultData();
                    window.EGBankApp.loadSystemUsers();
                }
                
                resultArea.className = 'result-area result-success';
                resultArea.innerHTML = `
                    <h3>✅ تم إعادة تهيئة النظام!</h3>
                    <p>تم مسح جميع البيانات القديمة</p>
                    <p>تم إنشاء بيانات جديدة بكلمة المرور المحدثة</p>
                    <p style="margin-top: 15px; color: #48bb78;">يمكنك الآن اختبار كلمة المرور</p>
                `;
                
                updateSystemStatus();
            } catch (error) {
                console.error('Reset error:', error);
                resultArea.className = 'result-area result-error';
                resultArea.innerHTML = `
                    <h3>❌ خطأ في إعادة التهيئة!</h3>
                    <p><strong>التفاصيل:</strong> ${error.message}</p>
                `;
            }
        }
        
        function goToLogin() {
            window.location.href = 'login.html';
        }
        
        // Auto-test when page loads
        setTimeout(() => {
            if (window.EGBankApp && window.EGBankApp.isInitialized) {
                testPassword();
            }
        }, 2000);
    </script>
</body>
</html>
