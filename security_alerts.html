<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - Security Alerts</title>
    <link rel="stylesheet" href="style.css">
    <style>
        #loading-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #2c2c2c;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .loader {
            border: 16px solid #3a3a3a;
            border-top: 16px solid #1a365d;
            border-radius: 50%;
            width: 120px;
            height: 120px;
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .alert-card {
            background: #3a3a3a;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #f56565;
        }
        
        .alert-card.warning {
            border-left-color: #ed8936;
        }
        
        .alert-card.info {
            border-left-color: #4299e1;
        }
        
        .alert-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .alert-title {
            color: white;
            font-weight: bold;
            font-size: 16px;
        }
        
        .alert-time {
            color: #a0a0a0;
            font-size: 12px;
        }
        
        .alert-message {
            color: #a0a0a0;
            margin-bottom: 10px;
        }
        
        .alert-actions {
            display: flex;
            gap: 10px;
        }
        
        .alert-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .btn-acknowledge {
            background: #1a365d;
            color: white;
        }
        
        .btn-dismiss {
            background: #555555;
            color: white;
        }
    </style>
</head>
<body>
    <div id="loading-container">
        <div class="loader"></div>
    </div>
    
    <script>
        window.addEventListener('load', function() {
            document.getElementById('loading-container').style.display = 'none';
        });
    </script>
    
    <!-- Banking Header -->
    <header class="bank-header">
        <div class="bank-logo">EG BANK</div>
        <nav class="bank-nav">
            <ul>
                <li><a href="index.html" style="color: white;">لوحة التحكم</a></li>
                <li><a href="cameras.html" style="color: white;">الأمان</a></li>
                <li><a href="access.html" class="active" style="color: white;">التحكم في الوصول</a></li>
                <li><a href="reports.html" style="color: white;">التقارير</a></li>
                <li><a href="settings.html" style="color: white;">الإعدادات</a></li>
                <li><a href="gps.html" style="color: white;">تتبع GPS</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main Content Area -->
    <div class="bank-container">
        <!-- Sidebar -->
        <aside class="bank-sidebar">
            <h3 style="color: white;">عبد الفتاح محمود</h3>
            <ul>
                <li><a href="index.html" style="color: white;">مراقبة الكاميرات</a></li>
                <li><a href="cameras.html" style="color: white;">إدارة كاميرات IP</a></li>
                <li class="active" style="color: white;">التنبيهات الأمنية</li>
                <li><a href="reports.html" style="color: white;">التقارير</a></li>
                <li><a href="user_management.html" style="color: white;">إدارة المستخدمين</a></li>
            </ul>
        </aside>

        <!-- Security Alerts Section -->
        <main class="bank-main">
            <div style="display: flex; align-items: center; margin-bottom: 30px;">
                <button onclick="window.history.back()" style="background: #1a365d; color: white; border: none; padding: 10px 15px; border-radius: 4px; margin-left: 15px; cursor: pointer;">← رجوع</button>
                <h1 style="color: white; margin: 0;">التنبيهات الأمنية</h1>
            </div>
            
            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalAlerts">12</div>
                    <div style="color: white;">إجمالي التنبيهات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="criticalAlerts">3</div>
                    <div style="color: white;">تنبيهات حرجة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="warningAlerts">6</div>
                    <div style="color: white;">تحذيرات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="infoAlerts">3</div>
                    <div style="color: white;">معلومات</div>
                </div>
            </div>
            
            <!-- Search and Filter -->
            <div class="search-container">
                <div class="search-bar">
                    <input type="text" id="searchInput" class="search-input" placeholder="البحث في التنبيهات...">
                    <button class="search-btn" onclick="searchAlerts()">بحث</button>
                    <button class="search-btn" onclick="clearAllAlerts()">مسح جميع التنبيهات</button>
                </div>
            </div>
            
            <!-- Security Alerts List -->
            <div class="access-container">
                <h2 style="color: white; margin-bottom: 20px;">التنبيهات النشطة</h2>
                <div id="alertsList">
                    <!-- Alerts will be populated by JavaScript -->
                </div>
            </div>
        </main>
    </div>

    <!-- Banking Footer -->
    <footer class="bank-footer">
        <p>© 2024 البنك المصري - التنبيهات الأمنية</p>
    </footer>

    <!-- Floating Toolbar -->
    <div class="floating-toolbar">
        <div class="toolbar-header">
            لوحة التحكم الرئيسية
        </div>
        
        <div class="toolbar-section">
            <div class="toolbar-section-title">Control Panel</div>
            <a href="index.html" class="toolbar-item">
                <div class="toolbar-item-icon">🏠</div>
                <div class="toolbar-item-text">لوحة التحكم</div>
            </a>
            <a href="cameras.html" class="toolbar-item">
                <div class="toolbar-item-icon">🔒</div>
                <div class="toolbar-item-text">الأمان</div>
            </a>
            <a href="access.html" class="toolbar-item active">
                <div class="toolbar-item-icon">🚪</div>
                <div class="toolbar-item-text">التحكم في الوصول</div>
            </a>
            <a href="reports.html" class="toolbar-item">
                <div class="toolbar-item-icon">📊</div>
                <div class="toolbar-item-text">التقارير</div>
            </a>
            <a href="settings.html" class="toolbar-item">
                <div class="toolbar-item-icon">⚙️</div>
                <div class="toolbar-item-text">الإعدادات</div>
            </a>
            <a href="gps.html" class="toolbar-item">
                <div class="toolbar-item-icon">📍</div>
                <div class="toolbar-item-text">تتبع GPS</div>
            </a>
        </div>
        
        <div class="toolbar-section">
            <div class="toolbar-section-title">Abdel Fattah Mahmoud</div>
            <a href="index.html" class="toolbar-item">
                <div class="toolbar-item-icon">📹</div>
                <div class="toolbar-item-text">مراقبة الكاميرات</div>
            </a>
            <a href="cameras.html" class="toolbar-item">
                <div class="toolbar-item-icon">📷</div>
                <div class="toolbar-item-text">إدارة كاميرات IP</div>
            </a>
            <a href="access.html" class="toolbar-item active">
                <div class="toolbar-item-icon">🔐</div>
                <div class="toolbar-item-text">التحكم في الوصول</div>
            </a>
            <a href="reports.html" class="toolbar-item">
                <div class="toolbar-item-icon">📈</div>
                <div class="toolbar-item-text">التقارير</div>
            </a>
            <a href="user_management.html" class="toolbar-item">
                <div class="toolbar-item-icon">👥</div>
                <div class="toolbar-item-text">إدارة المستخدمين</div>
            </a>
        </div>
        
        <div class="toolbar-user">
            <div class="toolbar-user-name">عبد الفتاح محمود</div>
            <div class="toolbar-user-role">مدير النظام</div>
        </div>
    </div>

    <script src="security_alerts.js"></script>
</body>
</html>
