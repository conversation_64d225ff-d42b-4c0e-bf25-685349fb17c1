// EG BANK Security Tasks Management System
// نظام إدارة مهام الأنظمة الأمنية

class SecurityTasksManager {
    constructor() {
        this.tasks = [];
        this.currentEditingTask = null;
        this.init();
    }

    // Initialize the system
    init() {
        console.log('🔧 Initializing Security Tasks Management System...');
        
        this.loadTasks();
        this.updateStatistics();
        this.renderTasksTable();
        this.setupEventListeners();
        this.setDefaultDates();
        
        console.log('✅ Security Tasks Management System initialized successfully');
    }

    // Load tasks from localStorage or create sample data
    loadTasks() {
        try {
            const savedTasks = localStorage.getItem('security_tasks');
            if (savedTasks) {
                this.tasks = JSON.parse(savedTasks);
            } else {
                // Create sample tasks
                this.tasks = [
                    {
                        id: Date.now() + 1,
                        description: 'صيانة دورية لكاميرات المراقبة في الفرع الرئيسي',
                        type: 'صيانة',
                        client: 'الفرع الرئيسي - شارع التحرير',
                        systemType: 'كاميرات مراقبة',
                        date: new Date().toISOString().split('T')[0],
                        time: '09:00',
                        status: 'مكتمل',
                        responsible: 'أحمد محمد',
                        notes: 'تم تنظيف العدسات وفحص الاتصالات',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 2,
                        description: 'تركيب نظام إنذار جديد',
                        type: 'تركيب',
                        client: 'فرع المعادي',
                        systemType: 'أنظمة إنذار',
                        date: new Date().toISOString().split('T')[0],
                        time: '14:00',
                        status: 'قيد التنفيذ',
                        responsible: 'محمد علي',
                        notes: 'في انتظار وصول القطع',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 3,
                        description: 'فحص أنظمة الحريق الشهري',
                        type: 'فحص',
                        client: 'مبنى الإدارة العامة',
                        systemType: 'أنظمة حريق',
                        date: new Date(Date.now() + 86400000).toISOString().split('T')[0], // Tomorrow
                        time: '10:30',
                        status: 'مؤجل',
                        responsible: 'سارة أحمد',
                        notes: 'تأجيل بسبب ظروف الطقس',
                        createdAt: new Date().toISOString()
                    }
                ];
                this.saveTasks();
            }
            console.log(`📋 Loaded ${this.tasks.length} tasks`);
        } catch (error) {
            console.error('Error loading tasks:', error);
            this.tasks = [];
        }
    }

    // Save tasks to localStorage
    saveTasks() {
        try {
            localStorage.setItem('security_tasks', JSON.stringify(this.tasks));
            console.log('💾 Tasks saved to localStorage');
        } catch (error) {
            console.error('Error saving tasks:', error);
        }
    }

    // Setup event listeners
    setupEventListeners() {
        // Task form submission
        document.getElementById('taskForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveTask();
        });

        // Filter inputs
        ['startDate', 'endDate', 'taskTypeFilter', 'statusFilter', 'clientFilter', 'systemTypeFilter'].forEach(id => {
            document.getElementById(id).addEventListener('change', () => {
                this.filterTasks();
            });
        });

        document.getElementById('clientFilter').addEventListener('input', () => {
            this.filterTasks();
        });
    }

    // Set default dates
    setDefaultDates() {
        const today = new Date();
        const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        
        document.getElementById('startDate').value = weekAgo.toISOString().split('T')[0];
        document.getElementById('endDate').value = today.toISOString().split('T')[0];
        document.getElementById('taskDate').value = today.toISOString().split('T')[0];
    }

    // Update statistics
    updateStatistics() {
        const total = this.tasks.length;
        const pending = this.tasks.filter(task => task.status === 'قيد التنفيذ').length;
        const completed = this.tasks.filter(task => task.status === 'مكتمل').length;
        const postponed = this.tasks.filter(task => task.status === 'مؤجل').length;

        document.getElementById('totalTasks').textContent = total;
        document.getElementById('pendingTasks').textContent = pending;
        document.getElementById('completedTasks').textContent = completed;
        document.getElementById('postponedTasks').textContent = postponed;
    }

    // Render tasks table
    renderTasksTable(tasksToRender = this.tasks) {
        const tbody = document.getElementById('tasksTableBody');
        tbody.innerHTML = '';

        tasksToRender.forEach((task, index) => {
            const row = tbody.insertRow();
            
            let statusClass = 'status-pending';
            if (task.status === 'مكتمل') statusClass = 'status-completed';
            else if (task.status === 'مؤجل') statusClass = 'status-postponed';

            row.innerHTML = `
                <td>${index + 1}</td>
                <td style="text-align: right; max-width: 200px;">${task.description}</td>
                <td>${task.type}</td>
                <td>${task.client}</td>
                <td>${task.systemType}</td>
                <td>${new Date(task.date).toLocaleDateString('ar-EG')}</td>
                <td>${task.time}</td>
                <td><span class="${statusClass}">${task.status}</span></td>
                <td>${task.responsible}</td>
                <td style="text-align: right; max-width: 150px;">${task.notes || 'لا توجد'}</td>
                <td>
                    <button class="table-action-btn edit-btn" onclick="editTask('${task.id}')">
                        ✏️ تعديل
                    </button>
                    <button class="table-action-btn delete-btn" onclick="deleteTask('${task.id}')">
                        🗑️ حذف
                    </button>
                </td>
            `;
        });
    }

    // Filter tasks
    filterTasks() {
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        const taskType = document.getElementById('taskTypeFilter').value;
        const status = document.getElementById('statusFilter').value;
        const client = document.getElementById('clientFilter').value.toLowerCase();
        const systemType = document.getElementById('systemTypeFilter').value;

        let filteredTasks = this.tasks;

        if (startDate) {
            filteredTasks = filteredTasks.filter(task => task.date >= startDate);
        }

        if (endDate) {
            filteredTasks = filteredTasks.filter(task => task.date <= endDate);
        }

        if (taskType) {
            filteredTasks = filteredTasks.filter(task => task.type === taskType);
        }

        if (status) {
            filteredTasks = filteredTasks.filter(task => task.status === status);
        }

        if (client) {
            filteredTasks = filteredTasks.filter(task => 
                task.client.toLowerCase().includes(client)
            );
        }

        if (systemType) {
            filteredTasks = filteredTasks.filter(task => task.systemType === systemType);
        }

        this.renderTasksTable(filteredTasks);
    }

    // Open add task modal
    openAddTaskModal() {
        this.currentEditingTask = null;
        document.getElementById('modalTitle').textContent = 'إضافة مهمة جديدة';
        document.getElementById('taskForm').reset();
        document.getElementById('taskDate').value = new Date().toISOString().split('T')[0];
        document.getElementById('taskModal').style.display = 'block';
    }

    // Close task modal
    closeTaskModal() {
        document.getElementById('taskModal').style.display = 'none';
        this.currentEditingTask = null;
    }

    // Save task (add or edit)
    saveTask() {
        const description = document.getElementById('taskDescription').value.trim();
        const type = document.getElementById('taskType').value;
        const client = document.getElementById('taskClient').value.trim();
        const systemType = document.getElementById('taskSystemType').value;
        const date = document.getElementById('taskDate').value;
        const time = document.getElementById('taskTime').value;
        const status = document.getElementById('taskStatus').value;
        const responsible = document.getElementById('taskResponsible').value.trim();
        const notes = document.getElementById('taskNotes').value.trim();

        if (!description || !type || !client || !systemType || !date || !time || !responsible) {
            alert('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        if (this.currentEditingTask) {
            // Edit existing task
            const taskIndex = this.tasks.findIndex(task => task.id === this.currentEditingTask.id);
            if (taskIndex !== -1) {
                this.tasks[taskIndex] = {
                    ...this.currentEditingTask,
                    description,
                    type,
                    client,
                    systemType,
                    date,
                    time,
                    status,
                    responsible,
                    notes,
                    updatedAt: new Date().toISOString()
                };
            }
            alert('تم تحديث المهمة بنجاح');
        } else {
            // Add new task
            const newTask = {
                id: Date.now(),
                description,
                type,
                client,
                systemType,
                date,
                time,
                status,
                responsible,
                notes,
                createdAt: new Date().toISOString()
            };
            this.tasks.push(newTask);
            alert('تم إضافة المهمة بنجاح');
        }

        this.saveTasks();
        this.updateStatistics();
        this.renderTasksTable();
        this.closeTaskModal();
    }

    // Edit task
    editTask(taskId) {
        const task = this.tasks.find(t => t.id == taskId);
        if (!task) return;

        this.currentEditingTask = task;
        document.getElementById('modalTitle').textContent = 'تعديل المهمة';
        
        // Fill form with task data
        document.getElementById('taskDescription').value = task.description;
        document.getElementById('taskType').value = task.type;
        document.getElementById('taskClient').value = task.client;
        document.getElementById('taskSystemType').value = task.systemType;
        document.getElementById('taskDate').value = task.date;
        document.getElementById('taskTime').value = task.time;
        document.getElementById('taskStatus').value = task.status;
        document.getElementById('taskResponsible').value = task.responsible;
        document.getElementById('taskNotes').value = task.notes || '';
        
        document.getElementById('taskModal').style.display = 'block';
    }

    // Delete task
    deleteTask(taskId) {
        const task = this.tasks.find(t => t.id == taskId);
        if (!task) return;

        if (confirm(`هل أنت متأكد من حذف المهمة: ${task.description}؟`)) {
            this.tasks = this.tasks.filter(t => t.id != taskId);
            this.saveTasks();
            this.updateStatistics();
            this.renderTasksTable();
            alert('تم حذف المهمة بنجاح');
        }
    }

    // Generate report
    generateReport() {
        const reportType = prompt('اختر نوع التقرير:\n1 - يومي\n2 - أسبوعي\n3 - شهري', '1');
        
        if (!reportType || !['1', '2', '3'].includes(reportType)) {
            return;
        }

        const today = new Date();
        let startDate, endDate, reportTitle;

        switch (reportType) {
            case '1': // Daily
                startDate = new Date(today);
                endDate = new Date(today);
                reportTitle = 'التقرير اليومي';
                break;
            case '2': // Weekly
                startDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
                endDate = new Date(today);
                reportTitle = 'التقرير الأسبوعي';
                break;
            case '3': // Monthly
                startDate = new Date(today.getFullYear(), today.getMonth(), 1);
                endDate = new Date(today);
                reportTitle = 'التقرير الشهري';
                break;
        }

        const filteredTasks = this.tasks.filter(task => {
            const taskDate = new Date(task.date);
            return taskDate >= startDate && taskDate <= endDate;
        });

        this.showReportModal(reportTitle, filteredTasks, startDate, endDate);
    }

    // Show report modal
    showReportModal(title, tasks, startDate, endDate) {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.style.display = 'block';
        
        const completed = tasks.filter(t => t.status === 'مكتمل').length;
        const pending = tasks.filter(t => t.status === 'قيد التنفيذ').length;
        const postponed = tasks.filter(t => t.status === 'مؤجل').length;

        modal.innerHTML = `
            <div class="modal-content" style="max-width: 800px;">
                <div class="modal-header">
                    <h3 class="modal-title">${title}</h3>
                    <button class="close-btn" onclick="this.closest('.modal').remove()">&times;</button>
                </div>
                
                <div style="margin-bottom: 20px;">
                    <p><strong>الفترة:</strong> من ${startDate.toLocaleDateString('ar-EG')} إلى ${endDate.toLocaleDateString('ar-EG')}</p>
                    <p><strong>إجمالي المهام:</strong> ${tasks.length}</p>
                    <p><strong>مكتملة:</strong> ${completed} | <strong>قيد التنفيذ:</strong> ${pending} | <strong>مؤجلة:</strong> ${postponed}</p>
                </div>
                
                <div style="overflow-x: auto; max-height: 400px;">
                    <table class="tasks-table">
                        <thead>
                            <tr>
                                <th>المهمة</th>
                                <th>النوع</th>
                                <th>العميل</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                                <th>المسؤول</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${tasks.map(task => `
                                <tr>
                                    <td style="text-align: right;">${task.description}</td>
                                    <td>${task.type}</td>
                                    <td>${task.client}</td>
                                    <td>${new Date(task.date).toLocaleDateString('ar-EG')}</td>
                                    <td>${task.status}</td>
                                    <td>${task.responsible}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
                
                <div style="margin-top: 20px; text-align: center;">
                    <button class="action-btn export-btn" onclick="window.securityTasksManager.exportReportToPDF('${title}', ${JSON.stringify(tasks).replace(/"/g, '&quot;')})">
                        📄 تصدير PDF
                    </button>
                    <button class="action-btn export-btn" onclick="window.securityTasksManager.exportReportToExcel('${title}', ${JSON.stringify(tasks).replace(/"/g, '&quot;')})">
                        📊 تصدير Excel
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
    }

    // Export to PDF
    exportToPDF() {
        this.exportReportToPDF('تقرير المهام الشامل', this.tasks);
    }

    // Export report to PDF
    exportReportToPDF(title, tasks) {
        const printWindow = window.open('', '_blank');
        const today = new Date().toLocaleDateString('ar-EG');
        
        let html = `
            <html dir="rtl">
            <head>
                <title>${title} - ${today}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                    th { background-color: #f2f2f2; }
                    h1, h2 { color: #333; text-align: center; }
                    .stats { background: #f9f9f9; padding: 15px; margin: 20px 0; border-radius: 5px; }
                </style>
            </head>
            <body>
                <h1>🏦 EG BANK - ${title}</h1>
                <h2>تاريخ التقرير: ${today}</h2>
                
                <div class="stats">
                    <h3>إحصائيات المهام</h3>
                    <p><strong>إجمالي المهام:</strong> ${tasks.length}</p>
                    <p><strong>مكتملة:</strong> ${tasks.filter(t => t.status === 'مكتمل').length}</p>
                    <p><strong>قيد التنفيذ:</strong> ${tasks.filter(t => t.status === 'قيد التنفيذ').length}</p>
                    <p><strong>مؤجلة:</strong> ${tasks.filter(t => t.status === 'مؤجل').length}</p>
                </div>
                
                <table>
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>وصف المهمة</th>
                            <th>النوع</th>
                            <th>العميل/الموقع</th>
                            <th>نوع النظام</th>
                            <th>التاريخ</th>
                            <th>الوقت</th>
                            <th>الحالة</th>
                            <th>المسؤول</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        tasks.forEach((task, index) => {
            html += `
                <tr>
                    <td>${index + 1}</td>
                    <td style="text-align: right;">${task.description}</td>
                    <td>${task.type}</td>
                    <td>${task.client}</td>
                    <td>${task.systemType}</td>
                    <td>${new Date(task.date).toLocaleDateString('ar-EG')}</td>
                    <td>${task.time}</td>
                    <td>${task.status}</td>
                    <td>${task.responsible}</td>
                    <td style="text-align: right;">${task.notes || 'لا توجد'}</td>
                </tr>
            `;
        });
        
        html += `
                    </tbody>
                </table>
                
                <div style="margin-top: 30px; text-align: center; color: #666;">
                    <p>تم إنشاء هذا التقرير تلقائياً بواسطة نظام إدارة المهام - EG BANK</p>
                </div>
            </body>
            </html>
        `;
        
        printWindow.document.write(html);
        printWindow.document.close();
        printWindow.print();
    }

    // Export to Excel
    exportToExcel() {
        this.exportReportToExcel('تقرير المهام الشامل', this.tasks);
    }

    // Export report to Excel (CSV format)
    exportReportToExcel(title, tasks) {
        const csvHeaders = ['#', 'وصف المهمة', 'النوع', 'العميل/الموقع', 'نوع النظام', 'التاريخ', 'الوقت', 'الحالة', 'المسؤول', 'ملاحظات'];
        const csvRows = tasks.map((task, index) => [
            index + 1,
            task.description,
            task.type,
            task.client,
            task.systemType,
            new Date(task.date).toLocaleDateString('ar-EG'),
            task.time,
            task.status,
            task.responsible,
            task.notes || 'لا توجد'
        ]);
        
        const csvContent = [csvHeaders, ...csvRows]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');
        
        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `${title}_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();
        
        alert('تم تصدير البيانات بنجاح');
    }

    // Send email report
    sendEmailReport() {
        const email = prompt('أدخل عنوان البريد الإلكتروني:');
        if (!email) return;

        // Simulate email sending
        alert(`تم إرسال التقرير إلى: ${email}\n\nملاحظة: هذه محاكاة لإرسال الإيميل. في النظام الحقيقي سيتم الإرسال فعلياً.`);
        
        // In a real system, you would integrate with an email service
        console.log('Email would be sent to:', email);
    }
}

// Global functions
function openAddTaskModal() {
    if (window.securityTasksManager) {
        window.securityTasksManager.openAddTaskModal();
    }
}

function closeTaskModal() {
    if (window.securityTasksManager) {
        window.securityTasksManager.closeTaskModal();
    }
}

function filterTasks() {
    if (window.securityTasksManager) {
        window.securityTasksManager.filterTasks();
    }
}

function generateReport() {
    if (window.securityTasksManager) {
        window.securityTasksManager.generateReport();
    }
}

function exportToPDF() {
    if (window.securityTasksManager) {
        window.securityTasksManager.exportToPDF();
    }
}

function exportToExcel() {
    if (window.securityTasksManager) {
        window.securityTasksManager.exportToExcel();
    }
}

function sendEmailReport() {
    if (window.securityTasksManager) {
        window.securityTasksManager.sendEmailReport();
    }
}

function editTask(taskId) {
    if (window.securityTasksManager) {
        window.securityTasksManager.editTask(taskId);
    }
}

function deleteTask(taskId) {
    if (window.securityTasksManager) {
        window.securityTasksManager.deleteTask(taskId);
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 Security Tasks Management System loading...');
    window.securityTasksManager = new SecurityTasksManager();
});

// Close modal when clicking outside
window.addEventListener('click', function(event) {
    const modal = document.getElementById('taskModal');
    if (event.target === modal) {
        closeTaskModal();
    }
});

console.log('🔧 Security Tasks Management System Script Loaded');
