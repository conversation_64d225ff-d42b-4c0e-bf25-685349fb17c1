<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - إعادة تعيين كلمة المرور</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
        }
        
        .reset-container {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 40px;
            max-width: 400px;
            width: 100%;
            text-align: center;
        }
        
        .reset-title {
            font-size: 24px;
            font-weight: 900;
            margin-bottom: 30px;
            color: #3182ce;
        }
        
        .reset-btn {
            background: linear-gradient(135deg, #f56565, #e53e3e);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            width: 100%;
        }
        
        .reset-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(245, 101, 101, 0.4);
        }
        
        .success-btn {
            background: linear-gradient(135deg, #48bb78, #38a169);
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
        }
        
        .success {
            border: 1px solid #48bb78;
            color: #48bb78;
        }
        
        .error {
            border: 1px solid #f56565;
            color: #f56565;
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <h1 class="reset-title">🔄 إعادة تعيين كلمة المرور</h1>
        
        <p style="margin-bottom: 20px; color: rgba(255,255,255,0.8);">
            سيتم تعيين كلمة المرور إلى: <strong style="color: #3182ce;">SHeh#1981</strong>
        </p>
        
        <button class="reset-btn" onclick="resetPassword()">
            🔐 إعادة تعيين كلمة المرور
        </button>
        
        <button class="reset-btn success-btn" onclick="goToLogin()" style="margin-top: 20px;">
            🚪 الذهاب لتسجيل الدخول
        </button>
        
        <div id="result"></div>
    </div>
    
    <script>
        function resetPassword() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>🔄 جاري إعادة التعيين...</p>';
            
            try {
                // Clear all data
                localStorage.clear();
                
                // Set new users with correct password
                const users = [
                    {
                        id: 'admin001',
                        username: 'admin',
                        password: 'SHeh#1981',
                        fullName: 'عبد الفتاح محمود',
                        email: '<EMAIL>',
                        role: 'admin',
                        permissions: ['cameras', 'attendance', 'gps', 'reports', 'users'],
                        status: 'active',
                        createdAt: new Date().toISOString(),
                        lastLogin: null
                    },
                    {
                        id: 'user001',
                        username: 'operator',
                        password: 'SHeh#1981',
                        fullName: 'أحمد محمد',
                        email: '<EMAIL>',
                        role: 'operator',
                        permissions: ['cameras', 'attendance', 'gps', 'reports'],
                        status: 'active',
                        createdAt: new Date().toISOString(),
                        lastLogin: null
                    }
                ];
                
                // Save users
                localStorage.setItem('systemUsers', JSON.stringify(users));
                localStorage.setItem('egbank_initialized', 'true');
                localStorage.setItem('password_updated', 'SHeh#1981');
                
                // Initialize other data
                localStorage.setItem('systemCameras', JSON.stringify([]));
                localStorage.setItem('systemAccessPoints', JSON.stringify([]));
                localStorage.setItem('systemATMs', JSON.stringify([]));
                localStorage.setItem('securityRecordings', JSON.stringify([]));
                localStorage.setItem('accessLogs', JSON.stringify([]));
                localStorage.setItem('securityAlerts', JSON.stringify([]));
                localStorage.setItem('attendanceRecords', JSON.stringify([]));
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <h3>✅ تم إعادة التعيين بنجاح!</h3>
                    <p><strong>اسم المستخدم:</strong> admin</p>
                    <p><strong>كلمة المرور:</strong> SHeh#1981</p>
                    <p style="margin-top: 10px;">يمكنك الآن تسجيل الدخول</p>
                `;
                
                console.log('Password reset completed');
                console.log('Users created:', users);
                
            } catch (error) {
                console.error('Reset error:', error);
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>❌ خطأ في إعادة التعيين!</h3>
                    <p>${error.message}</p>
                `;
            }
        }
        
        function goToLogin() {
            window.location.href = 'login.html';
        }
        
        // Auto-reset on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Reset page loaded');
        });
    </script>
</body>
</html>
