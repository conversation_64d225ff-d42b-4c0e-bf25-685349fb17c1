// Track Mobile ATMs System
// نظام تتبع أجهزة الصراف المتنقلة

// Global Variables
let map;
let vehicles = JSON.parse(localStorage.getItem('vehicles')) || [];
let markers = {};
let routes = {};
let geofences = [];
let alerts = [];
let isTrackingActive = false;
let selectedVehicle = null;

// Map configuration
const mapConfig = {
    center: [30.0444, 31.2357], // Cairo coordinates
    zoom: 12,
    style: 'streets'
};

// Vehicle status types
const VehicleStatus = {
    ONLINE: 'online',
    OFFLINE: 'offline',
    MOVING: 'moving',
    STOPPED: 'stopped'
};

// Sample ATM vehicle data
const sampleVehicles = [
    {
        id: 'ATM001',
        name: 'صراف متنقل - 001',
        plateNumber: 'ق أ ح 1234',
        driverName: 'أحمد محمد علي',
        driverPhone: '01234567890',
        vehicleType: 'atm_van',
        deviceIMEI: 'GPS-ATM-001',
        company: 'البنك المصري',
        status: VehicleStatus.ONLINE,
        location: {
            lat: 30.0444,
            lng: 31.2357,
            address: 'وسط البلد، القاهرة'
        },
        speed: 45,
        direction: 90,
        lastUpdate: new Date().toISOString(),
        odometer: 15420,
        fuelLevel: 75,
        engineStatus: 'running',
        notes: 'جهاز صراف متنقل رئيسي'
    },
    {
        id: 'ATM002',
        name: 'صراف متنقل - 002',
        plateNumber: 'ق ب ج 5678',
        driverName: 'محمد أحمد حسن',
        driverPhone: '01123456789',
        vehicleType: 'atm_truck',
        deviceIMEI: 'GPS-ATM-002',
        company: 'البنك المصري',
        status: VehicleStatus.STOPPED,
        location: {
            lat: 30.0644,
            lng: 31.2557,
            address: 'مدينة نصر، القاهرة'
        },
        speed: 0,
        direction: 180,
        lastUpdate: new Date(Date.now() - 300000).toISOString(),
        odometer: 28750,
        fuelLevel: 60,
        engineStatus: 'stopped',
        notes: 'شاحنة صراف متنقل كبيرة'
    },
    {
        id: 'ATM003',
        name: 'صراف متنقل - 003',
        plateNumber: 'د هـ و 9012',
        driverName: 'سارة أحمد محمود',
        driverPhone: '01012345678',
        vehicleType: 'security_car',
        deviceIMEI: 'GPS-ATM-003',
        company: 'البنك المصري',
        status: VehicleStatus.MOVING,
        location: {
            lat: 30.0244,
            lng: 31.2157,
            address: 'الزمالك، القاهرة'
        },
        speed: 35,
        direction: 45,
        lastUpdate: new Date(Date.now() - 60000).toISOString(),
        odometer: 8950,
        fuelLevel: 90,
        engineStatus: 'running',
        notes: 'سيارة حراسة مرافقة للصراف'
    },
    {
        id: 'ATM004',
        name: 'صراف متنقل - 004',
        plateNumber: 'ح ز ي 3456',
        driverName: 'فاطمة علي حسن',
        driverPhone: '01098765432',
        vehicleType: 'service_van',
        deviceIMEI: 'GPS-ATM-004',
        company: 'البنك المصري',
        status: VehicleStatus.OFFLINE,
        location: {
            lat: 30.0844,
            lng: 31.2757,
            address: 'مصر الجديدة، القاهرة'
        },
        speed: 0,
        direction: 270,
        lastUpdate: new Date(Date.now() - 1800000).toISOString(),
        odometer: 45200,
        fuelLevel: 40,
        engineStatus: 'stopped',
        notes: 'شاحنة خدمة وصيانة الأجهزة'
    }
];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeSystem();
    loadSampleData();
    initializeMap();
    renderVehicles();
    updateStatistics();
    startRealTimeTracking();
});

// Initialize system
function initializeSystem() {
    console.log('Track Mobile ATMs System Initialized');
    
    // Load vehicles from localStorage or use sample data
    if (vehicles.length === 0) {
        vehicles = [...sampleVehicles];
        saveVehicles();
    }
    
    // Initialize alerts array
    alerts = [];
}

// Load sample data
function loadSampleData() {
    // Add some sample geofences
    geofences = [
        {
            id: 'GF001',
            name: 'منطقة وسط البلد',
            center: [30.0444, 31.2357],
            radius: 1000,
            type: 'safe'
        },
        {
            id: 'GF002',
            name: 'منطقة محظورة',
            center: [30.0644, 31.2557],
            radius: 500,
            type: 'restricted'
        }
    ];
}

// Initialize map
function initializeMap() {
    // Initialize Leaflet map
    map = L.map('map').setView(mapConfig.center, mapConfig.zoom);
    
    // Add tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);
    
    // Add vehicles to map
    vehicles.forEach(vehicle => {
        addVehicleToMap(vehicle);
    });
    
    // Add geofences to map
    geofences.forEach(geofence => {
        addGeofenceToMap(geofence);
    });
}

// Add vehicle to map
function addVehicleToMap(vehicle) {
    const icon = getVehicleIcon(vehicle);
    const marker = L.marker([vehicle.location.lat, vehicle.location.lng], { icon })
        .addTo(map)
        .bindPopup(createVehiclePopup(vehicle));
    
    markers[vehicle.id] = marker;
}

// Get vehicle icon based on type and status
function getVehicleIcon(vehicle) {
    const iconMap = {
        atm_van: '🏧',
        atm_truck: '🚛',
        security_car: '🚗',
        service_van: '🚐'
    };
    
    const statusColors = {
        online: '#48bb78',
        offline: '#f56565',
        moving: '#4299e1',
        stopped: '#ed8936'
    };
    
    return L.divIcon({
        html: `<div style="background: ${statusColors[vehicle.status]}; 
                           border-radius: 50%; 
                           width: 30px; 
                           height: 30px; 
                           display: flex; 
                           align-items: center; 
                           justify-content: center; 
                           font-size: 16px; 
                           border: 2px solid white;">
                   ${iconMap[vehicle.vehicleType] || '🚗'}
               </div>`,
        className: 'vehicle-marker',
        iconSize: [30, 30],
        iconAnchor: [15, 15]
    });
}

// Create vehicle popup content
function createVehiclePopup(vehicle) {
    return `
        <div style="min-width: 200px;">
            <h3 style="margin: 0 0 10px 0; color: #1a365d;">${vehicle.name}</h3>
            <p><strong>رقم اللوحة:</strong> ${vehicle.plateNumber}</p>
            <p><strong>السائق:</strong> ${vehicle.driverName}</p>
            <p><strong>السرعة:</strong> ${vehicle.speed} كم/س</p>
            <p><strong>الحالة:</strong> ${getStatusText(vehicle.status)}</p>
            <p><strong>آخر تحديث:</strong> ${formatDateTime(vehicle.lastUpdate)}</p>
            <button onclick="trackVehicle('${vehicle.id}')"
                    style="background: #1a365d; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer;">
                تتبع الصراف
            </button>
        </div>
    `;
}

// Add geofence to map
function addGeofenceToMap(geofence) {
    const color = geofence.type === 'safe' ? '#48bb78' : '#f56565';
    const circle = L.circle(geofence.center, {
        color: color,
        fillColor: color,
        fillOpacity: 0.2,
        radius: geofence.radius
    }).addTo(map).bindPopup(`<h3>${geofence.name}</h3><p>نوع المنطقة: ${geofence.type === 'safe' ? 'آمنة' : 'محظورة'}</p>`);
}

// Render vehicles list
function renderVehicles() {
    const vehiclesList = document.getElementById('vehiclesList');
    vehiclesList.innerHTML = '';

    vehicles.forEach(vehicle => {
        const vehicleCard = createVehicleCard(vehicle);
        vehiclesList.appendChild(vehicleCard);
    });
}

// Create vehicle card
function createVehicleCard(vehicle) {
    const card = document.createElement('div');
    card.className = 'vehicle-card';
    card.dataset.vehicleId = vehicle.id;

    card.innerHTML = `
        <div class="vehicle-header">
            <div class="vehicle-name">${vehicle.name}</div>
            <div class="vehicle-status status-${vehicle.status}">${getStatusText(vehicle.status)}</div>
        </div>

        <div class="vehicle-info">
            <div class="info-row">
                <span class="info-label">رقم اللوحة:</span>
                <span>${vehicle.plateNumber}</span>
            </div>
            <div class="info-row">
                <span class="info-label">السائق:</span>
                <span>${vehicle.driverName}</span>
            </div>
            <div class="info-row">
                <span class="info-label">السرعة:</span>
                <span>${vehicle.speed} كم/س</span>
            </div>
            <div class="info-row">
                <span class="info-label">الوقود:</span>
                <span>${vehicle.fuelLevel}%</span>
            </div>
            <div class="info-row">
                <span class="info-label">آخر تحديث:</span>
                <span>${formatDateTime(vehicle.lastUpdate)}</span>
            </div>
        </div>

        <div class="vehicle-actions">
            <button class="action-btn btn-track" onclick="trackVehicle('${vehicle.id}')">تتبع</button>
            <button class="action-btn btn-details" onclick="showVehicleDetails('${vehicle.id}')">تفاصيل</button>
            <button class="action-btn btn-edit" onclick="editVehicle('${vehicle.id}')">تعديل</button>
        </div>
    `;

    return card;
}

// Update statistics
function updateStatistics() {
    const stats = {
        total: vehicles.length,
        active: vehicles.filter(v => v.status === VehicleStatus.ONLINE || v.status === VehicleStatus.MOVING).length,
        distance: vehicles.reduce((sum, v) => sum + (v.odometer || 0), 0),
        alerts: alerts.length
    };

    document.getElementById('totalCount').textContent = stats.total;
    document.getElementById('activeCount').textContent = stats.active;
    document.getElementById('distanceToday').textContent = Math.round(stats.distance / 1000);
    document.getElementById('alertsCount').textContent = stats.alerts;
}

// Add new vehicle
function addNewVehicle() {
    document.getElementById('addVehicleModal').style.display = 'block';
}

// Close add vehicle modal
function closeAddVehicleModal() {
    document.getElementById('addVehicleModal').style.display = 'none';
    document.getElementById('addVehicleForm').reset();
}

// Handle add vehicle form submission
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('addVehicleForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const newVehicle = {
                id: 'VH' + String(Date.now()).slice(-6),
                name: document.getElementById('vehicleName').value,
                plateNumber: document.getElementById('plateNumber').value,
                driverName: document.getElementById('driverName').value,
                driverPhone: document.getElementById('driverPhone').value,
                vehicleType: document.getElementById('vehicleType').value,
                deviceIMEI: document.getElementById('deviceIMEI').value,
                company: document.getElementById('company').value,
                status: VehicleStatus.OFFLINE,
                location: {
                    lat: mapConfig.center[0] + (Math.random() - 0.5) * 0.1,
                    lng: mapConfig.center[1] + (Math.random() - 0.5) * 0.1,
                    address: 'موقع جديد'
                },
                speed: 0,
                direction: 0,
                lastUpdate: new Date().toISOString(),
                odometer: 0,
                fuelLevel: 100,
                engineStatus: 'stopped',
                notes: document.getElementById('notes').value
            };

            vehicles.push(newVehicle);
            saveVehicles();
            addVehicleToMap(newVehicle);
            renderVehicles();
            updateStatistics();
            closeAddVehicleModal();
            showNotification('تم إضافة جهاز الصراف بنجاح');
        });
    }
});

// Track vehicle
function trackVehicle(vehicleId) {
    const vehicle = vehicles.find(v => v.id === vehicleId);
    if (!vehicle) return;

    selectedVehicle = vehicle;
    map.setView([vehicle.location.lat, vehicle.location.lng], 15);
    markers[vehicleId].openPopup();

    // Highlight vehicle card
    document.querySelectorAll('.vehicle-card').forEach(card => {
        card.style.border = '2px solid #555555';
    });
    document.querySelector(`[data-vehicle-id="${vehicleId}"]`).style.border = '2px solid #1a365d';

    showNotification(`بدء تتبع ${vehicle.name}`);
}

// Show vehicle details
function showVehicleDetails(vehicleId) {
    const vehicle = vehicles.find(v => v.id === vehicleId);
    if (!vehicle) return;

    const modal = document.getElementById('vehicleDetailsModal');
    const content = document.getElementById('vehicleDetailsContent');

    content.innerHTML = `
        <h2 style="color: white; margin-bottom: 20px;">${vehicle.name}</h2>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
                <h3 style="color: #1a365d;">معلومات المركبة</h3>
                <p><strong>رقم اللوحة:</strong> ${vehicle.plateNumber}</p>
                <p><strong>النوع:</strong> ${getVehicleTypeText(vehicle.vehicleType)}</p>
                <p><strong>الشركة:</strong> ${vehicle.company}</p>
                <p><strong>رقم الجهاز:</strong> ${vehicle.deviceIMEI}</p>
            </div>
            <div>
                <h3 style="color: #1a365d;">معلومات السائق</h3>
                <p><strong>الاسم:</strong> ${vehicle.driverName}</p>
                <p><strong>الهاتف:</strong> ${vehicle.driverPhone}</p>
            </div>
        </div>
        <div style="margin-top: 20px;">
            <h3 style="color: #1a365d;">الحالة الحالية</h3>
            <p><strong>الحالة:</strong> ${getStatusText(vehicle.status)}</p>
            <p><strong>السرعة:</strong> ${vehicle.speed} كم/س</p>
            <p><strong>الوقود:</strong> ${vehicle.fuelLevel}%</p>
            <p><strong>العداد:</strong> ${vehicle.odometer} كم</p>
            <p><strong>الموقع:</strong> ${vehicle.location.address}</p>
            <p><strong>آخر تحديث:</strong> ${formatDateTime(vehicle.lastUpdate)}</p>
        </div>
        ${vehicle.notes ? `<div style="margin-top: 20px;"><h3 style="color: #1a365d;">ملاحظات</h3><p>${vehicle.notes}</p></div>` : ''}
    `;

    modal.style.display = 'block';
}

// Close vehicle details modal
function closeVehicleDetailsModal() {
    document.getElementById('vehicleDetailsModal').style.display = 'none';
}

// Start real-time tracking
function startRealTimeTracking() {
    isTrackingActive = true;

    setInterval(() => {
        if (isTrackingActive) {
            simulateVehicleMovement();
            updateStatistics();
            checkGeofenceViolations();
        }
    }, 5000); // Update every 5 seconds
}

// Simulate vehicle movement
function simulateVehicleMovement() {
    vehicles.forEach(vehicle => {
        if (vehicle.status === VehicleStatus.MOVING || vehicle.status === VehicleStatus.ONLINE) {
            // Simulate movement
            const deltaLat = (Math.random() - 0.5) * 0.001;
            const deltaLng = (Math.random() - 0.5) * 0.001;

            vehicle.location.lat += deltaLat;
            vehicle.location.lng += deltaLng;
            vehicle.speed = Math.floor(Math.random() * 80) + 10;
            vehicle.lastUpdate = new Date().toISOString();

            // Update marker position
            if (markers[vehicle.id]) {
                markers[vehicle.id].setLatLng([vehicle.location.lat, vehicle.location.lng]);
                markers[vehicle.id].setIcon(getVehicleIcon(vehicle));
            }
        }
    });

    saveVehicles();
    renderVehicles();
}

// Utility functions
function getStatusText(status) {
    const statusMap = {
        online: 'متصل',
        offline: 'غير متصل',
        moving: 'متحرك',
        stopped: 'متوقف'
    };
    return statusMap[status] || status;
}

function getVehicleTypeText(type) {
    const typeMap = {
        atm_van: 'شاحنة صراف',
        atm_truck: 'شاحنة صراف كبيرة',
        security_car: 'سيارة حراسة',
        service_van: 'شاحنة خدمة'
    };
    return typeMap[type] || type;
}

function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('ar-EG');
}

function saveVehicles() {
    localStorage.setItem('vehicles', JSON.stringify(vehicles));
}

function showNotification(message) {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: #48bb78;
        color: white;
        padding: 15px 25px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 1001;
        font-weight: 500;
    `;

    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
        document.body.removeChild(notification);
    }, 3000);
}

// Map control functions
function centerMap() {
    map.setView(mapConfig.center, mapConfig.zoom);
}

function toggleSatellite() {
    // This would switch to satellite view in a real implementation
    showNotification('تم تبديل عرض القمر الصناعي');
}

function toggleTraffic() {
    // This would toggle traffic layer in a real implementation
    showNotification('تم تبديل عرض حركة المرور');
}

function changeMapStyle() {
    const style = document.getElementById('mapStyle').value;
    showNotification(`تم تغيير نمط الخريطة إلى: ${style}`);
}

// Search and filter functions
function searchVehicles() {
    const searchTerm = document.getElementById('searchVehicle').value.toLowerCase();
    const cards = document.querySelectorAll('.vehicle-card');

    cards.forEach(card => {
        const vehicleId = card.dataset.vehicleId;
        const vehicle = vehicles.find(v => v.id === vehicleId);

        if (vehicle && (
            vehicle.name.toLowerCase().includes(searchTerm) ||
            vehicle.plateNumber.toLowerCase().includes(searchTerm) ||
            vehicle.driverName.toLowerCase().includes(searchTerm)
        )) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

function filterVehicles() {
    const filterStatus = document.getElementById('filterStatus').value;
    const cards = document.querySelectorAll('.vehicle-card');

    cards.forEach(card => {
        const vehicleId = card.dataset.vehicleId;
        const vehicle = vehicles.find(v => v.id === vehicleId);

        if (filterStatus === 'all' || vehicle.status === filterStatus) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

console.log('Track Mobile ATMs System JavaScript Loaded Successfully');
