// TODO: Add database connection and CRUD operations here
// GPS Tracking Logic
const vehicles = JSON.parse(localStorage.getItem('vehicles')) || [];
let map;
let currentRoute = null;
let trackingInterval = null;
let trackedVehicle = null;
const routeHistory = JSON.parse(localStorage.getItem('routeHistory')) || {};
const flowers = []; // Flower markers
const dangerZones = []; // Danger zone areas
let arrivalSound = new Audio('https://assets.mixkit.co/sfx/preview/mixkit-unlock-game-notification-253.mp3');
let alarmSound = new Audio('https://assets.mixkit.co/sfx/preview/mixkit-alarm-digital-clock-beep-989.mp3');

// Initialize map
function initMap() {
    map = L.map('map').setView([30.0444, 31.2357], 13); // Cairo coordinates
    
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);
    
    // Add sample flower marker (destination)
    const flower = L.marker([30.0444, 31.2357], {
        icon: L.divIcon({
            className: 'arrival-flower',
            iconSize: [30, 30]
        })
    }).addTo(map).bindPopup('Main Branch');
    flowers.push(flower);
    
    // Add sample danger zone
    const dangerZone = L.circle([30.0400, 31.2300], {
        color: 'red',
        fillColor: '#f03',
        fillOpacity: 0.3,
        radius: 500
    }).addTo(map).bindPopup('High Risk Area');
    dangerZones.push(dangerZone);
    
    // Create alarm indicator
    const alarmIndicator = document.createElement('div');
    alarmIndicator.id = 'alarmIndicator';
    alarmIndicator.className = 'alarm-indicator';
    alarmIndicator.textContent = 'DANGER ALERT!';
    document.body.appendChild(alarmIndicator);
}

document.addEventListener('DOMContentLoaded', () => {
    initMap();
    renderVehicles();
    
    const addVehicleBtn = document.getElementById('addVehicleBtn');
    const startTrackingBtn = document.getElementById('startTrackingBtn');
    const stopTrackingBtn = document.getElementById('stopTrackingBtn');
    
    // Add new vehicle
    addVehicleBtn.addEventListener('click', () => {
        const driverName = document.getElementById('driverName').value;
        const driverPhone = document.getElementById('driverPhone').value;
        const deviceSerial = document.getElementById('deviceSerial').value;
        const plateNumber = document.getElementById('plateNumber').value;
        
        if (!driverName || !driverPhone || !deviceSerial || !plateNumber) {
            alert('Please fill all fields');
            return;
        }
        
        const vehicle = {
            id: trackedVehicle || Date.now().toString(),
            driverName,
            driverPhone,
            deviceSerial,
            plateNumber,
            totalDistance: trackedVehicle ? vehicles.find(v => v.id === trackedVehicle).totalDistance : 0,
            locations: trackedVehicle ? [...vehicles.find(v => v.id === trackedVehicle).locations] : [],
            isTracking: trackedVehicle ? vehicles.find(v => v.id === trackedVehicle).isTracking : false
        };
        
        if (trackedVehicle) {
            // Update existing vehicle
            const index = vehicles.findIndex(v => v.id === trackedVehicle);
            vehicles[index] = vehicle;
            trackedVehicle = null;
        } else {
            // Add new vehicle
            vehicles.push(vehicle);
        }
        
        vehicles.push(vehicle);
        saveVehicles();
        renderVehicles();
        document.getElementById('vehicleForm').reset();
    });
    
    // Start tracking
    startTrackingBtn.addEventListener('click', () => {
        if (!trackedVehicle) {
            alert('Please select a vehicle first');
            return;
        }
        
        const vehicle = vehicles.find(v => v.id === trackedVehicle);
        if (!vehicle) return;
        
        if (vehicle.isTracking) {
            alert('Tracking already started for this vehicle');
            return;
        }
        
        // Start tracking simulation
        vehicle.isTracking = true;
        saveVehicles();
        updateVehicleCard(vehicle.id);
        
        // Clear existing route if any
        if (currentRoute) {
            map.removeLayer(currentRoute);
        }
        
        // Start new route
        const startLocation = vehicle.locations.length > 0 ? 
            vehicle.locations[vehicle.locations.length-1] : 
            [30.0444, 31.2357]; // Cairo
        
        vehicle.locations.push(startLocation);
        currentRoute = L.polyline([startLocation], {color: 'blue'}).addTo(map);
        
        trackingInterval = setInterval(() => {
            if (!vehicle.isTracking) {
                clearInterval(trackingInterval);
                return;
            }
            
            // Generate new location near last position
            const lastLocation = vehicle.locations[vehicle.locations.length-1];
            const newLat = lastLocation[0] + (Math.random() * 0.01 - 0.005);
            const newLng = lastLocation[1] + (Math.random() * 0.01 - 0.005);
            const newLocation = [newLat, newLng];
            
            // Calculate distance (simplified)
            const distance = Math.sqrt(
                Math.pow(newLat - lastLocation[0], 2) +
                Math.pow(newLng - lastLocation[1], 2)
            ) * 100; // approx km
            
            vehicle.totalDistance += distance;
            vehicle.locations.push(newLocation);
            
            // Update route
            currentRoute.addLatLng(newLocation);
            
            // Save updated data
            saveVehicles();
            updateVehicleCard(vehicle.id);
            
            // Save to history
            if (!routeHistory[vehicle.id]) routeHistory[vehicle.id] = [];
            routeHistory[vehicle.id].push({
                timestamp: new Date().toISOString(),
                location: newLocation,
                distance
            });
            localStorage.setItem('routeHistory', JSON.stringify(routeHistory));
            
            // Center map on new location
            map.setView(newLocation);
            
            // Check for arrival at flower markers
            flowers.forEach(flower => {
                const flowerPos = flower.getLatLng();
                const distToFlower = map.distance(newLocation, flowerPos);
                if (distToFlower < 50) { // 50 meters
                    playArrivalSound();
                    flower.bindPopup("Vehicle arrived!").openPopup();
                    
                    // Simulate SMS notification
                    sendSMSAlert(
                        vehicle.driverPhone,
                        `Vehicle ${vehicle.plateNumber} arrived at ${flowerPos.lat.toFixed(4)}, ${flowerPos.lng.toFixed(4)}`
                    );
                }
            });
            
            // Check for danger zones
            let inDanger = false;
            dangerZones.forEach(zone => {
                const zoneCenter = zone.getLatLng();
                const zoneRadius = zone.getRadius();
                const distToZone = map.distance(newLocation, zoneCenter);
                
                if (distToZone < zoneRadius) {
                    inDanger = true;
                    triggerDangerAlarm(vehicle);
                }
            });
            
            // Update alarm indicator
            const alarmIndicator = document.getElementById('alarmIndicator');
            alarmIndicator.style.display = inDanger ? 'block' : 'none';
            
        }, 5000); // Update every 5 seconds
    });
    
    // Stop tracking
    stopTrackingBtn.addEventListener('click', () => {
        if (!trackedVehicle) return;
        
        const vehicle = vehicles.find(v => v.id === trackedVehicle);
        if (!vehicle) return;
        
        vehicle.isTracking = false;
        clearInterval(trackingInterval);
        saveVehicles();
        updateVehicleCard(vehicle.id);
        
        // Convert active route to inactive (dashed line)
        if (currentRoute) {
            map.removeLayer(currentRoute);
            currentRoute = L.polyline(vehicle.locations, {
                color: 'red',
                dashArray: '5, 10'
            }).addTo(map);
        }
    });
});

function renderVehicles() {
    const container = document.getElementById('vehiclesContainer');
    container.innerHTML = '';
    
    vehicles.forEach(vehicle => {
        const card = document.createElement('div');
        card.className = `vehicle-card ${vehicle.isTracking ? 'active-route' : 'inactive-route'}`;
        card.dataset.id = vehicle.id;
        card.innerHTML = `
            <h3>${vehicle.driverName} - ${vehicle.plateNumber}</h3>
            <p>Phone: ${vehicle.driverPhone}</p>
            <p>Device: ${vehicle.deviceSerial}</p>
            <div class="route-info">
                <span>Distance: ${vehicle.totalDistance.toFixed(2)} km</span>
                <span>Locations: ${vehicle.locations.length}</span>
                <span>Status: ${vehicle.isTracking ? 'ACTIVE' : 'INACTIVE'}</span>
            </div>
            <div class="vehicle-actions">
                <button class="edit-vehicle" data-id="${vehicle.id}">Edit</button>
                <button class="delete-vehicle" data-id="${vehicle.id}">Delete</button>
            </div>
        `;
        
        card.addEventListener('click', (e) => {
            if (!e.target.classList.contains('edit-vehicle') &&
                !e.target.classList.contains('delete-vehicle')) {
                trackedVehicle = vehicle.id;
                
                // Highlight selected vehicle
                document.querySelectorAll('.vehicle-card').forEach(c => {
                    c.style.borderLeft = c.dataset.id === vehicle.id ?
                        '4px solid #3182ce' :
                        (vehicle.isTracking ? '4px solid #38a169' : '4px solid #e53e3e');
                });
                
                // Show route on map
                if (currentRoute) {
                    map.removeLayer(currentRoute);
                }
                
                if (vehicle.locations.length > 0) {
                    const style = vehicle.isTracking ?
                        {color: 'blue'} :
                        {color: 'red', dashArray: '5, 10'};
                    
                    currentRoute = L.polyline(vehicle.locations, style).addTo(map);
                    map.fitBounds(currentRoute.getBounds());
                }
            }
        });
        
        // Edit vehicle button
        card.querySelector('.edit-vehicle').addEventListener('click', (e) => {
            e.stopPropagation();
            document.getElementById('driverName').value = vehicle.driverName;
            document.getElementById('driverPhone').value = vehicle.driverPhone;
            document.getElementById('deviceSerial').value = vehicle.deviceSerial;
            document.getElementById('plateNumber').value = vehicle.plateNumber;
            trackedVehicle = vehicle.id;
        });
        
        // Delete vehicle button
        card.querySelector('.delete-vehicle').addEventListener('click', (e) => {
            e.stopPropagation();
            if (confirm(`Delete vehicle ${vehicle.plateNumber}?`)) {
                const index = vehicles.findIndex(v => v.id === vehicle.id);
                if (index !== -1) {
                    vehicles.splice(index, 1);
                    saveVehicles();
                    renderVehicles();
                    
                    // Clear map if deleted vehicle was tracked
                    if (trackedVehicle === vehicle.id) {
                        if (currentRoute) map.removeLayer(currentRoute);
                        trackedVehicle = null;
                    }
                }
            }
        });
        
        container.appendChild(card);
    });
}

function updateVehicleCard(vehicleId) {
    const vehicle = vehicles.find(v => v.id === vehicleId);
    if (!vehicle) return;
    
    const card = document.querySelector(`.vehicle-card[data-id="${vehicleId}"]`);
    if (card) {
        card.innerHTML = `
            <h3>${vehicle.driverName} - ${vehicle.plateNumber}</h3>
            <p>Phone: ${vehicle.driverPhone}</p>
            <div class="route-info">
                <span>Distance: ${vehicle.totalDistance.toFixed(2)} km</span>
                <span>Locations: ${vehicle.locations.length}</span>
                <span>Status: ${vehicle.isTracking ? 'ACTIVE' : 'INACTIVE'}</span>
            </div>
        `;
        
        card.className = `vehicle-card ${vehicle.isTracking ? 'active-route' : 'inactive-route'}`;
    }
}

function saveVehicles() {
    localStorage.setItem('vehicles', JSON.stringify(vehicles));
}
// SMS Alert Simulation (would be replaced with real API in production)
function sendSMSAlert(phone, message) {
    console.log(`SMS Alert to ${phone}: ${message}`);
    // In real implementation, this would call an SMS API like Twilio
    // For demo purposes, we just log to console
}
function triggerDangerAlarm(vehicle) {
    // Play alarm sound
    alarmSound.currentTime = 0;
    alarmSound.play().catch(e => console.log('Alarm sound failed:', e));
    
    // Flash the alarm indicator
    const alarmIndicator = document.getElementById('alarmIndicator');
    alarmIndicator.style.display = 'block';
    
    // Flash effect
    let flashCount = 0;
    const flashInterval = setInterval(() => {
        alarmIndicator.style.visibility = alarmIndicator.style.visibility === 'hidden' ? 'visible' : 'hidden';
        flashCount++;
        if (flashCount > 10) {
            clearInterval(flashInterval);
            alarmIndicator.style.visibility = 'visible';
        }
    }, 500);
    
    // Simulate SMS alert
    sendSMSAlert(
        vehicle.driverPhone,
        `DANGER ALERT! Vehicle ${vehicle.plateNumber} in danger zone!`
    );
    
    // Also send to security team
    sendSMSAlert(
        '+201234567890', // Security team number
        `ALERT: Vehicle ${vehicle.plateNumber} entered danger zone!`
    );
}