// Mobile ATM GPS Tracking System
// نظام تتبع أجهزة الصراف المتنقلة

// Current language state
let currentLanguage = 'ar';

// Vehicle database with sample data
let vehiclesDatabase = JSON.parse(localStorage.getItem('vehiclesDatabase')) || [
    {
        id: 'ATM001',
        vehicleName: 'صراف متنقل - 001',
        driverName: 'أحمد محمد علي',
        driverPhone: '01234567890',
        deviceSerial: 'GPS-ATM-001',
        plateNumber: 'ق أ ح 1234',
        distance: 45.67,
        locations: 15,
        status: 'active',
        lastUpdate: '2024-01-15 14:30:25',
        date: '2024-01-15',
        totalTrips: 8,
        avgSpeed: 35
    },
    {
        id: 'ATM002',
        vehicleName: 'صراف متنقل - 002',
        driverName: 'محمد أحمد حسن',
        driverPhone: '01123456789',
        deviceSerial: 'GPS-ATM-002',
        plateNumber: 'ق ب ج 5678',
        distance: 0.00,
        locations: 0,
        status: 'inactive',
        lastUpdate: '2024-01-15 09:15:10',
        date: '2024-01-15',
        totalTrips: 0,
        avgSpeed: 0
    },
    {
        id: 'ATM003',
        vehicleName: 'صراف متنقل - 003',
        driverName: 'سارة أحمد محمود',
        driverPhone: '01012345678',
        deviceSerial: 'GPS-ATM-003',
        plateNumber: 'ق د هـ 9012',
        distance: 78.23,
        locations: 25,
        status: 'active',
        lastUpdate: '2024-01-15 15:45:30',
        date: '2024-01-15',
        totalTrips: 12,
        avgSpeed: 42
    },
    {
        id: 'ATM004',
        vehicleName: 'صراف متنقل - 004',
        driverName: 'فاطمة علي حسن',
        driverPhone: '01098765432',
        deviceSerial: 'GPS-ATM-004',
        plateNumber: 'ق و ز 3456',
        distance: 23.45,
        locations: 8,
        status: 'active',
        lastUpdate: '2024-01-15 16:10:45',
        date: '2024-01-15',
        totalTrips: 5,
        avgSpeed: 28
    },
    {
        id: 'ATM005',
        vehicleName: 'صراف متنقل - 005',
        driverName: 'خالد محمد أحمد',
        driverPhone: '01187654321',
        deviceSerial: 'GPS-ATM-005',
        plateNumber: 'ق ح ط 7890',
        distance: 0.00,
        locations: 0,
        status: 'inactive',
        lastUpdate: '2024-01-15 08:20:15',
        date: '2024-01-15',
        totalTrips: 0,
        avgSpeed: 0
    },
    {
        id: 'ATM006',
        vehicleName: 'صراف متنقل - 006',
        driverName: 'نور الدين عبد الله',
        driverPhone: '01276543210',
        deviceSerial: 'GPS-ATM-006',
        plateNumber: 'ق ي ك 1357',
        distance: 56.78,
        locations: 18,
        status: 'active',
        lastUpdate: '2024-01-15 13:25:20',
        date: '2024-01-15',
        totalTrips: 9,
        avgSpeed: 38
    },
    {
        id: 'ATM007',
        vehicleName: 'صراف متنقل - 007',
        driverName: 'عبد الرحمن صالح',
        driverPhone: '01365432109',
        deviceSerial: 'GPS-ATM-007',
        plateNumber: 'ق ل م 2468',
        distance: 0.00,
        locations: 0,
        status: 'inactive',
        lastUpdate: '2024-01-15 11:40:30',
        date: '2024-01-15',
        totalTrips: 0,
        avgSpeed: 0
    },
    {
        id: 'ATM008',
        vehicleName: 'صراف متنقل - 008',
        driverName: 'ليلى حسن محمد',
        driverPhone: '01454321098',
        deviceSerial: 'GPS-ATM-008',
        plateNumber: 'ق ن س 9753',
        distance: 89.12,
        locations: 32,
        status: 'active',
        lastUpdate: '2024-01-15 12:55:10',
        date: '2024-01-15',
        totalTrips: 15,
        avgSpeed: 45
    }
];

// Filtered vehicles for display
let filteredVehicles = [...vehiclesDatabase];

// History database for detailed tracking
let historyDatabase = JSON.parse(localStorage.getItem('historyDatabase')) || [];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    renderVehicles();
    updateStatistics();
    populateHistoryDatabase();

    // Simulate real-time updates
    setInterval(simulateRealTimeUpdates, 30000); // Update every 30 seconds
});

// Initialize application
function initializeApp() {
    console.log('Mobile ATM GPS Tracking System Initialized');
    console.log('Total Vehicles:', vehiclesDatabase.length);
}

// Render vehicles in the grid
function renderVehicles() {
    const vehiclesContainer = document.getElementById('vehiclesContainer');
    vehiclesContainer.innerHTML = '';

    if (filteredVehicles.length === 0) {
        vehiclesContainer.innerHTML = `
            <div style="text-align: center; color: #a0a0a0; padding: 40px;">
                <h3>${currentLanguage === 'ar' ? 'لا توجد مركبات' : 'No vehicles found'}</h3>
                <p>${currentLanguage === 'ar' ? 'جرب تغيير معايير البحث' : 'Try changing search criteria'}</p>
            </div>
        `;
        return;
    }

    filteredVehicles.forEach(vehicle => {
        const vehicleCard = createVehicleCard(vehicle);
        vehiclesContainer.appendChild(vehicleCard);
    });
}

// Create individual vehicle card
function createVehicleCard(vehicle) {
    const card = document.createElement('div');
    card.className = 'vehicle-card';
    card.dataset.id = vehicle.id;

    const statusClass = vehicle.status === 'active' ? 'status-active' : 'status-inactive';
    const statusText = {
        ar: { active: 'نشط', inactive: 'غير نشط' },
        en: { active: 'ACTIVE', inactive: 'INACTIVE' }
    };

    card.innerHTML = `
        <div class="vehicle-header">
            <div class="vehicle-name">${vehicle.vehicleName}</div>
            <div class="vehicle-status ${statusClass}">
                ${statusText[currentLanguage][vehicle.status]}
            </div>
        </div>

        <div class="vehicle-info">
            <div class="info-item">
                <span class="info-label">${currentLanguage === 'ar' ? 'السائق:' : 'Driver:'}</span>
                ${vehicle.driverName}
            </div>
            <div class="info-item">
                <span class="info-label">${currentLanguage === 'ar' ? 'الهاتف:' : 'Phone:'}</span>
                ${vehicle.driverPhone}
            </div>
            <div class="info-item">
                <span class="info-label">${currentLanguage === 'ar' ? 'الجهاز:' : 'Device:'}</span>
                ${vehicle.deviceSerial}
            </div>
            <div class="info-item">
                <span class="info-label">${currentLanguage === 'ar' ? 'رقم اللوحة:' : 'Plate:'}</span>
                ${vehicle.plateNumber}
            </div>
            <div class="info-item">
                <span class="info-label">${currentLanguage === 'ar' ? 'المسافة:' : 'Distance:'}</span>
                ${vehicle.distance.toFixed(2)} km
            </div>
            <div class="info-item">
                <span class="info-label">${currentLanguage === 'ar' ? 'المواقع:' : 'Locations:'}</span>
                ${vehicle.locations}
            </div>
            <div class="info-item">
                <span class="info-label">${currentLanguage === 'ar' ? 'آخر تحديث:' : 'Last Update:'}</span>
                ${vehicle.lastUpdate}
            </div>
            <div class="info-item">
                <span class="info-label">${currentLanguage === 'ar' ? 'الرحلات:' : 'Trips:'}</span>
                ${vehicle.totalTrips}
            </div>
        </div>

        <div class="vehicle-actions">
            <button class="action-btn btn-edit" onclick="editVehicle('${vehicle.id}')">
                ${currentLanguage === 'ar' ? 'تعديل' : 'Edit'}
            </button>
            <button class="action-btn btn-delete" onclick="deleteVehicle('${vehicle.id}')">
                ${currentLanguage === 'ar' ? 'حذف' : 'Delete'}
            </button>
            <button class="action-btn btn-view-history" onclick="viewVehicleHistory('${vehicle.id}')">
                ${currentLanguage === 'ar' ? 'عرض التاريخ' : 'View History'}
            </button>
        </div>
    `;

    return card;
}

// Search vehicles function
function searchVehicles() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();

    if (searchTerm === '') {
        filteredVehicles = [...vehiclesDatabase];
    } else {
        filteredVehicles = vehiclesDatabase.filter(vehicle =>
            vehicle.vehicleName.toLowerCase().includes(searchTerm) ||
            vehicle.driverName.toLowerCase().includes(searchTerm) ||
            vehicle.driverPhone.includes(searchTerm) ||
            vehicle.plateNumber.toLowerCase().includes(searchTerm) ||
            vehicle.deviceSerial.toLowerCase().includes(searchTerm)
        );
    }

    renderVehicles();
    updateStatistics();
}

// Clear search function
function clearSearch() {
    document.getElementById('searchInput').value = '';
    filteredVehicles = [...vehiclesDatabase];
    document.getElementById('statusFilter').value = 'all';
    document.getElementById('dateFilter').value = 'today';
    renderVehicles();
    updateStatistics();
}

// Filter vehicles function
function filterVehicles() {
    const statusFilter = document.getElementById('statusFilter').value;
    const dateFilter = document.getElementById('dateFilter').value;

    let filtered = [...vehiclesDatabase];

    // Apply status filter
    if (statusFilter !== 'all') {
        filtered = filtered.filter(vehicle => vehicle.status === statusFilter);
    }

    // Apply date filter (simplified for demo)
    const today = new Date().toISOString().split('T')[0];
    if (dateFilter === 'today') {
        filtered = filtered.filter(vehicle => vehicle.date === today);
    }
    // Add more date filtering logic as needed

    filteredVehicles = filtered;
    renderVehicles();
    updateStatistics();
}
// Edit vehicle function
function editVehicle(vehicleId) {
    const vehicle = vehiclesDatabase.find(v => v.id === vehicleId);
    if (!vehicle) return;

    const newDriverName = prompt(
        currentLanguage === 'ar' ? 'اسم السائق الجديد:' : 'New driver name:',
        vehicle.driverName
    );
    const newDriverPhone = prompt(
        currentLanguage === 'ar' ? 'رقم الهاتف الجديد:' : 'New phone number:',
        vehicle.driverPhone
    );

    if (newDriverName && newDriverPhone) {
        vehicle.driverName = newDriverName;
        vehicle.driverPhone = newDriverPhone;
        vehicle.lastUpdate = new Date().toLocaleString('sv-SE').replace('T', ' ').substring(0, 19);

        saveDatabase();
        renderVehicles();

        showNotification(
            currentLanguage === 'ar' ?
            'تم تحديث بيانات المركبة بنجاح' :
            'Vehicle data updated successfully'
        );
    }
}

// Delete vehicle function
function deleteVehicle(vehicleId) {
    const vehicle = vehiclesDatabase.find(v => v.id === vehicleId);
    if (!vehicle) return;

    const confirmMessage = currentLanguage === 'ar' ?
        `هل أنت متأكد من حذف المركبة ${vehicle.vehicleName}؟` :
        `Are you sure you want to delete ${vehicle.vehicleName}?`;

    if (confirm(confirmMessage)) {
        const index = vehiclesDatabase.findIndex(v => v.id === vehicleId);
        if (index !== -1) {
            vehiclesDatabase.splice(index, 1);
            filteredVehicles = filteredVehicles.filter(v => v.id !== vehicleId);

            saveDatabase();
            renderVehicles();
            updateStatistics();

            showNotification(
                currentLanguage === 'ar' ?
                'تم حذف المركبة بنجاح' :
                'Vehicle deleted successfully'
            );
        }
    }
}

// View vehicle history function
function viewVehicleHistory(vehicleId) {
    const vehicle = vehiclesDatabase.find(v => v.id === vehicleId);
    if (!vehicle) return;

    // Generate some sample history data for the vehicle
    const vehicleHistory = generateVehicleHistory(vehicle);

    // Open database modal and show vehicle-specific history
    openDatabaseModal();
    filterHistoryByVehicle(vehicleId, vehicleHistory);
}

// Update statistics
function updateStatistics() {
    const stats = {
        total: filteredVehicles.length,
        active: filteredVehicles.filter(v => v.status === 'active').length,
        inactive: filteredVehicles.filter(v => v.status === 'inactive').length,
        totalDistance: filteredVehicles.reduce((sum, v) => sum + v.distance, 0)
    };

    document.getElementById('totalVehicles').textContent = stats.total;
    document.getElementById('activeVehicles').textContent = stats.active;
    document.getElementById('inactiveVehicles').textContent = stats.inactive;
    document.getElementById('totalDistance').textContent = Math.round(stats.totalDistance);
}

// Open database modal
function openDatabaseModal() {
    const modal = document.getElementById('databaseModal');
    modal.style.display = 'block';
    populateHistoryTable();
}

// Close database modal
function closeDatabaseModal() {
    const modal = document.getElementById('databaseModal');
    modal.style.display = 'none';
}

// Populate history table
function populateHistoryTable() {
    const tableBody = document.getElementById('historyTableBody');
    tableBody.innerHTML = '';

    // Combine current vehicles with history data
    const allData = [...vehiclesDatabase, ...historyDatabase];

    allData.forEach(record => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${record.vehicleName || record.id}</td>
            <td>${record.driverName}</td>
            <td>${record.driverPhone}</td>
            <td>${record.deviceSerial}</td>
            <td>${record.distance.toFixed(2)} km</td>
            <td>
                <span class="vehicle-status ${record.status === 'active' ? 'status-active' : 'status-inactive'}">
                    ${currentLanguage === 'ar' ?
                        (record.status === 'active' ? 'نشط' : 'غير نشط') :
                        record.status.toUpperCase()}
                </span>
            </td>
            <td>${record.lastUpdate}</td>
            <td>${record.date}</td>
        `;
        tableBody.appendChild(row);
    });
}

// Search database function
function searchDatabase() {
    const searchTerm = document.getElementById('dbSearchInput').value.toLowerCase().trim();
    const tableBody = document.getElementById('historyTableBody');
    const rows = tableBody.getElementsByTagName('tr');

    for (let row of rows) {
        const cells = row.getElementsByTagName('td');
        let found = false;

        for (let cell of cells) {
            if (cell.textContent.toLowerCase().includes(searchTerm)) {
                found = true;
                break;
            }
        }

        row.style.display = found ? '' : 'none';
    }
}

// Generate vehicle history
function generateVehicleHistory(vehicle) {
    const history = [];
    const today = new Date();

    // Generate 30 days of history
    for (let i = 0; i < 30; i++) {
        const date = new Date(today);
        date.setDate(date.getDate() - i);

        const record = {
            ...vehicle,
            date: date.toISOString().split('T')[0],
            lastUpdate: date.toLocaleString('sv-SE').replace('T', ' ').substring(0, 19),
            distance: Math.random() * 100,
            locations: Math.floor(Math.random() * 50),
            status: Math.random() > 0.3 ? 'active' : 'inactive'
        };

        history.push(record);
    }

    return history;
}

// Filter history by vehicle
function filterHistoryByVehicle(vehicleId, vehicleHistory) {
    const tableBody = document.getElementById('historyTableBody');
    tableBody.innerHTML = '';

    vehicleHistory.forEach(record => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${record.vehicleName}</td>
            <td>${record.driverName}</td>
            <td>${record.driverPhone}</td>
            <td>${record.deviceSerial}</td>
            <td>${record.distance.toFixed(2)} km</td>
            <td>
                <span class="vehicle-status ${record.status === 'active' ? 'status-active' : 'status-inactive'}">
                    ${currentLanguage === 'ar' ?
                        (record.status === 'active' ? 'نشط' : 'غير نشط') :
                        record.status.toUpperCase()}
                </span>
            </td>
            <td>${record.lastUpdate}</td>
            <td>${record.date}</td>
        `;
        tableBody.appendChild(row);
    });
}

// Populate history database with sample data
function populateHistoryDatabase() {
    if (historyDatabase.length === 0) {
        // Generate historical data for all vehicles
        vehiclesDatabase.forEach(vehicle => {
            const history = generateVehicleHistory(vehicle);
            historyDatabase.push(...history.slice(1, 10)); // Add 9 historical records per vehicle
        });

        localStorage.setItem('historyDatabase', JSON.stringify(historyDatabase));
    }
}

// Simulate real-time updates
function simulateRealTimeUpdates() {
    // Randomly update some vehicles
    const activeVehicles = vehiclesDatabase.filter(v => v.status === 'active');

    activeVehicles.forEach(vehicle => {
        if (Math.random() > 0.7) { // 30% chance of update
            // Update distance and locations
            vehicle.distance += Math.random() * 5; // Add 0-5 km
            vehicle.locations += Math.floor(Math.random() * 3); // Add 0-2 locations
            vehicle.lastUpdate = new Date().toLocaleString('sv-SE').replace('T', ' ').substring(0, 19);

            // Occasionally change status
            if (Math.random() > 0.9) {
                vehicle.status = vehicle.status === 'active' ? 'inactive' : 'active';
            }
        }
    });

    saveDatabase();

    // Update display if current view matches
    if (filteredVehicles.length > 0) {
        renderVehicles();
        updateStatistics();
    }

    console.log('Real-time update completed');
}

// Save database to localStorage
function saveDatabase() {
    localStorage.setItem('vehiclesDatabase', JSON.stringify(vehiclesDatabase));
}

// Show notification
function showNotification(message) {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: #48bb78;
        color: white;
        padding: 15px 25px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 1001;
        font-weight: 500;
        animation: slideDown 0.3s ease;
    `;

    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.animation = 'slideUp 0.3s ease';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideDown {
        from { opacity: 0; transform: translateX(-50%) translateY(-20px); }
        to { opacity: 1; transform: translateX(-50%) translateY(0); }
    }

    @keyframes slideUp {
        from { opacity: 1; transform: translateX(-50%) translateY(0); }
        to { opacity: 0; transform: translateX(-50%) translateY(-20px); }
    }
`;
document.head.appendChild(style);

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('databaseModal');
    if (event.target === modal) {
        closeDatabaseModal();
    }
}

// Handle search input enter key
document.addEventListener('keypress', function(event) {
    if (event.key === 'Enter') {
        if (event.target.id === 'searchInput') {
            searchVehicles();
        } else if (event.target.id === 'dbSearchInput') {
            searchDatabase();
        }
    }
});

console.log('Mobile ATM GPS Tracking System loaded successfully');