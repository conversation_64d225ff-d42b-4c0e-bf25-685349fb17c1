<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - إدارة التسجيلات</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            background-color: #2c2c2c;
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: white;
        }
        
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            background: #3a3a3a;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            color: white;
            font-size: 32px;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .controls-panel {
            background: linear-gradient(135deg, #1a365d, #2c5282);
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .control-label {
            color: white;
            font-weight: 500;
            font-size: 14px;
        }
        
        .control-input {
            background: #2c2c2c;
            color: white;
            border: 1px solid #555555;
            padding: 10px 12px;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .control-button {
            background: linear-gradient(135deg, #1a365d, #2c5282);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .control-button:hover {
            background: linear-gradient(135deg, #2c5282, #3182ce);
            transform: translateY(-2px);
        }
        
        .recordings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .recording-card {
            background: #4a4a4a;
            border-radius: 12px;
            padding: 20px;
            border: 2px solid #555555;
            transition: all 0.3s ease;
        }
        
        .recording-card:hover {
            border-color: #1a365d;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }
        
        .recording-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .recording-title {
            color: white;
            font-size: 16px;
            font-weight: bold;
        }
        
        .recording-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-recording {
            background: #e53e3e;
            color: white;
        }
        
        .status-saved {
            background: #48bb78;
            color: white;
        }
        
        .status-processing {
            background: #ed8936;
            color: white;
        }
        
        .recording-info {
            margin-bottom: 15px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .info-label {
            color: #a0a0a0;
        }
        
        .info-value {
            color: white;
            font-weight: 500;
        }
        
        .recording-preview {
            width: 100%;
            height: 150px;
            background: #2c2c2c;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
        }
        
        .preview-placeholder {
            color: #a0a0a0;
            font-size: 14px;
            text-align: center;
        }
        
        .recording-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            flex: 1;
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-play {
            background: #1a365d;
            color: white;
        }
        
        .btn-play:hover {
            background: #2c5282;
        }
        
        .btn-download {
            background: #38a169;
            color: white;
        }
        
        .btn-download:hover {
            background: #2f855a;
        }
        
        .btn-delete {
            background: #e53e3e;
            color: white;
        }
        
        .btn-delete:hover {
            background: #c53030;
        }
        
        .storage-info {
            background: #4a4a4a;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .storage-header {
            color: white;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .storage-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
            background: #3a3a3a;
            border-radius: 8px;
        }
        
        .stat-value {
            color: #1a365d;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: white;
            font-size: 12px;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #2c2c2c;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 10px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #1a365d, #2c5282);
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        .back-btn {
            background: #1a365d;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: #2c5282;
        }
        
        @media (max-width: 768px) {
            .controls-panel {
                grid-template-columns: 1fr;
            }
            
            .recordings-grid {
                grid-template-columns: 1fr;
            }
            
            .storage-stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <button class="back-btn" onclick="window.history.back()">← رجوع</button>
        
        <div class="header">
            <h1>📹 إدارة التسجيلات</h1>
            <p style="color: #a0a0a0; margin: 10px 0 0 0;">نظام حفظ وإدارة تسجيلات المراقبة</p>
        </div>
        
        <!-- Storage Information -->
        <div class="storage-info">
            <div class="storage-header">📊 معلومات التخزين</div>
            <div class="storage-stats">
                <div class="stat-item">
                    <div class="stat-value" id="totalRecordings">0</div>
                    <div class="stat-label">إجمالي التسجيلات</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="storageUsed">0 GB</div>
                    <div class="stat-label">المساحة المستخدمة</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="storageAvailable">500 GB</div>
                    <div class="stat-label">المساحة المتاحة</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="recordingTime">0h</div>
                    <div class="stat-label">مدة التسجيل الإجمالية</div>
                </div>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="storageProgress" style="width: 0%"></div>
            </div>
        </div>
        
        <!-- Controls Panel -->
        <div class="controls-panel">
            <div class="control-group">
                <label class="control-label">البحث بالتاريخ:</label>
                <input type="date" class="control-input" id="searchDate">
            </div>
            
            <div class="control-group">
                <label class="control-label">البحث بالكاميرا:</label>
                <select class="control-input" id="searchCamera">
                    <option value="">جميع الكاميرات</option>
                    <option value="camera1">كاميرا المدخل الرئيسي</option>
                    <option value="camera2">كاميرا الخزينة</option>
                    <option value="camera3">كاميرا صالة العملاء</option>
                    <option value="camera4">كاميرا الموقف</option>
                </select>
            </div>
            
            <div class="control-group">
                <label class="control-label">نوع التسجيل:</label>
                <select class="control-input" id="recordingType">
                    <option value="">جميع الأنواع</option>
                    <option value="continuous">تسجيل مستمر</option>
                    <option value="motion">كشف الحركة</option>
                    <option value="manual">تسجيل يدوي</option>
                    <option value="alarm">تنبيه أمني</option>
                </select>
            </div>
            
            <div class="control-group">
                <label class="control-label">الإجراءات:</label>
                <button class="control-button" onclick="searchRecordings()">🔍 بحث</button>
            </div>
            
            <div class="control-group">
                <label class="control-label">تسجيل جديد:</label>
                <button class="control-button" onclick="startNewRecording()">🔴 بدء التسجيل</button>
            </div>
            
            <div class="control-group">
                <label class="control-label">إدارة التخزين:</label>
                <button class="control-button" onclick="cleanupOldRecordings()">🗑️ تنظيف قديم</button>
            </div>
        </div>
        
        <!-- Recordings Grid -->
        <div class="recordings-grid" id="recordingsGrid">
            <!-- Recordings will be populated here -->
        </div>
    </div>
    
    <script src="app_core.js"></script>
    <script>
        // Wait for EG BANK system to be ready
        document.addEventListener('egbankReady', function() {
            console.log('EG BANK system ready, initializing recordings page');
            initializeRecordingsPage();
        });

        // Initialize recordings page
        function initializeRecordingsPage() {
            if (!window.egbank.isAuthenticated()) {
                console.log('User not authenticated, redirecting to login');
                window.location.href = 'login.html';
                return;
            }

            // Check if user has cameras permission
            if (!window.egbank.hasPermission('cameras')) {
                window.egbank.showNotification('ليس لديك صلاحية للوصول إلى التسجيلات', 'error');
                setTimeout(() => {
                    window.location.href = 'home_simple.html';
                }, 2000);
                return;
            }

            initializeRecordings();
            renderRecordings();
            updateStorageInfo();

            // Log page access
            window.egbank.logAction('page_access', 'دخول إلى صفحة إدارة التسجيلات');

            console.log('Recordings page initialized successfully');
        }

        // Sample recordings data
        let recordings = JSON.parse(localStorage.getItem('securityRecordings') || []);
        
        // Default sample recordings
        const sampleRecordings = [
            {
                id: 'rec001',
                title: 'تسجيل المدخل الرئيسي',
                camera: 'camera1',
                cameraName: 'كاميرا المدخل الرئيسي',
                startTime: new Date(Date.now() - 3600000).toISOString(),
                endTime: new Date().toISOString(),
                duration: 3600,
                size: 1.2,
                type: 'continuous',
                status: 'saved',
                quality: 'HD',
                hasAudio: true,
                motionDetected: true,
                thumbnail: null
            },
            {
                id: 'rec002',
                title: 'كشف حركة - الخزينة',
                camera: 'camera2',
                cameraName: 'كاميرا الخزينة',
                startTime: new Date(Date.now() - 7200000).toISOString(),
                endTime: new Date(Date.now() - 6900000).toISOString(),
                duration: 300,
                size: 0.8,
                type: 'motion',
                status: 'saved',
                quality: '4K',
                hasAudio: false,
                motionDetected: true,
                thumbnail: null
            },
            {
                id: 'rec003',
                title: 'تسجيل يدوي - صالة العملاء',
                camera: 'camera3',
                cameraName: 'كاميرا صالة العملاء',
                startTime: new Date(Date.now() - 1800000).toISOString(),
                endTime: null,
                duration: 1800,
                size: 0.5,
                type: 'manual',
                status: 'recording',
                quality: 'HD',
                hasAudio: true,
                motionDetected: false,
                thumbnail: null
            }
        ];
        
        // Initialize recordings
        function initializeRecordings() {
            if (recordings.length === 0) {
                recordings = [...sampleRecordings];
                saveRecordings();
            }
            renderRecordings();
            updateStorageInfo();
        }
        
        // Render recordings
        function renderRecordings() {
            const grid = document.getElementById('recordingsGrid');
            grid.innerHTML = '';
            
            recordings.forEach(recording => {
                const card = createRecordingCard(recording);
                grid.appendChild(card);
            });
        }
        
        // Create recording card
        function createRecordingCard(recording) {
            const card = document.createElement('div');
            card.className = 'recording-card';
            
            const startTime = new Date(recording.startTime);
            const duration = formatDuration(recording.duration);
            const size = recording.size.toFixed(1);
            
            card.innerHTML = `
                <div class="recording-header">
                    <div class="recording-title">${recording.title}</div>
                    <div class="recording-status status-${recording.status}">
                        ${getStatusText(recording.status)}
                    </div>
                </div>
                
                <div class="recording-preview">
                    <div class="preview-placeholder">
                        📹 ${recording.quality}<br>
                        ${recording.hasAudio ? '🔊 مع صوت' : '🔇 بدون صوت'}
                    </div>
                </div>
                
                <div class="recording-info">
                    <div class="info-row">
                        <span class="info-label">الكاميرا:</span>
                        <span class="info-value">${recording.cameraName}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">وقت البداية:</span>
                        <span class="info-value">${startTime.toLocaleString('ar-EG')}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">المدة:</span>
                        <span class="info-value">${duration}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">الحجم:</span>
                        <span class="info-value">${size} GB</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">النوع:</span>
                        <span class="info-value">${getTypeText(recording.type)}</span>
                    </div>
                </div>
                
                <div class="recording-actions">
                    <button class="action-btn btn-play" onclick="playRecording('${recording.id}')">
                        ▶️ تشغيل
                    </button>
                    <button class="action-btn btn-download" onclick="downloadRecording('${recording.id}')">
                        💾 تحميل
                    </button>
                    <button class="action-btn btn-delete" onclick="deleteRecording('${recording.id}')">
                        🗑️ حذف
                    </button>
                </div>
            `;
            
            return card;
        }
        
        // Update storage information
        function updateStorageInfo() {
            const totalRecordings = recordings.length;
            const totalSize = recordings.reduce((sum, rec) => sum + rec.size, 0);
            const totalDuration = recordings.reduce((sum, rec) => sum + rec.duration, 0);
            const storageLimit = 500; // GB
            const usagePercentage = (totalSize / storageLimit) * 100;
            
            document.getElementById('totalRecordings').textContent = totalRecordings;
            document.getElementById('storageUsed').textContent = totalSize.toFixed(1) + ' GB';
            document.getElementById('recordingTime').textContent = Math.floor(totalDuration / 3600) + 'h';
            document.getElementById('storageProgress').style.width = usagePercentage + '%';
        }
        
        // Helper functions
        function getStatusText(status) {
            const statusMap = {
                recording: 'جاري التسجيل',
                saved: 'محفوظ',
                processing: 'جاري المعالجة'
            };
            return statusMap[status] || status;
        }
        
        function getTypeText(type) {
            const typeMap = {
                continuous: 'تسجيل مستمر',
                motion: 'كشف الحركة',
                manual: 'تسجيل يدوي',
                alarm: 'تنبيه أمني'
            };
            return typeMap[type] || type;
        }
        
        function formatDuration(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;
            
            if (hours > 0) {
                return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            } else {
                return `${minutes}:${secs.toString().padStart(2, '0')}`;
            }
        }
        
        // Recording actions
        function playRecording(id) {
            const recording = recordings.find(r => r.id === id);
            if (recording) {
                showNotification(`تشغيل التسجيل: ${recording.title}`, 'info');
                // Here you would implement actual video playback
            }
        }
        
        function downloadRecording(id) {
            const recording = recordings.find(r => r.id === id);
            if (recording) {
                showNotification(`جاري تحميل: ${recording.title}`, 'info');
                // Here you would implement actual download functionality
            }
        }
        
        function deleteRecording(id) {
            const recording = recordings.find(r => r.id === id);
            if (recording && confirm(`هل أنت متأكد من حذف التسجيل: ${recording.title}؟`)) {
                const index = recordings.findIndex(r => r.id === id);
                recordings.splice(index, 1);
                saveRecordings();
                renderRecordings();
                updateStorageInfo();
                showNotification('تم حذف التسجيل بنجاح', 'success');
            }
        }
        
        function startNewRecording() {
            const newRecording = {
                id: 'rec' + Date.now(),
                title: 'تسجيل جديد - ' + new Date().toLocaleString('ar-EG'),
                camera: 'camera1',
                cameraName: 'كاميرا المدخل الرئيسي',
                startTime: new Date().toISOString(),
                endTime: null,
                duration: 0,
                size: 0,
                type: 'manual',
                status: 'recording',
                quality: 'HD',
                hasAudio: true,
                motionDetected: false,
                thumbnail: null
            };
            
            recordings.unshift(newRecording);
            saveRecordings();
            renderRecordings();
            updateStorageInfo();
            showNotification('تم بدء التسجيل الجديد', 'success');
        }
        
        function searchRecordings() {
            const date = document.getElementById('searchDate').value;
            const camera = document.getElementById('searchCamera').value;
            const type = document.getElementById('recordingType').value;
            
            let filteredRecordings = recordings;
            
            if (date) {
                filteredRecordings = filteredRecordings.filter(rec => {
                    const recDate = new Date(rec.startTime).toISOString().split('T')[0];
                    return recDate === date;
                });
            }
            
            if (camera) {
                filteredRecordings = filteredRecordings.filter(rec => rec.camera === camera);
            }
            
            if (type) {
                filteredRecordings = filteredRecordings.filter(rec => rec.type === type);
            }
            
            renderFilteredRecordings(filteredRecordings);
            showNotification(`تم العثور على ${filteredRecordings.length} تسجيل`, 'info');
        }
        
        function renderFilteredRecordings(filteredRecordings) {
            const grid = document.getElementById('recordingsGrid');
            grid.innerHTML = '';
            
            filteredRecordings.forEach(recording => {
                const card = createRecordingCard(recording);
                grid.appendChild(card);
            });
        }
        
        function cleanupOldRecordings() {
            const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
            const oldRecordings = recordings.filter(rec => new Date(rec.startTime) < thirtyDaysAgo);
            
            if (oldRecordings.length > 0 && confirm(`هل تريد حذف ${oldRecordings.length} تسجيل قديم (أكثر من 30 يوم)؟`)) {
                recordings = recordings.filter(rec => new Date(rec.startTime) >= thirtyDaysAgo);
                saveRecordings();
                renderRecordings();
                updateStorageInfo();
                showNotification(`تم حذف ${oldRecordings.length} تسجيل قديم`, 'success');
            } else if (oldRecordings.length === 0) {
                showNotification('لا توجد تسجيلات قديمة للحذف', 'info');
            }
        }
        
        function saveRecordings() {
            localStorage.setItem('securityRecordings', JSON.stringify(recordings));
        }
        
        // Use integrated notification system
        function showNotification(message, type = 'info') {
            if (window.egbank) {
                window.egbank.showNotification(message, type);
            } else {
                // Fallback notification
                alert(message);
            }
        }

        // Enhanced recording actions with logging
        function playRecording(id) {
            const recording = recordings.find(r => r.id === id);
            if (recording) {
                window.egbank.logAction('recording_play', `تشغيل التسجيل: ${recording.title}`, { recordingId: id });
                showNotification(`تشغيل التسجيل: ${recording.title}`, 'info');
                // Here you would implement actual video playback
            }
        }

        function downloadRecording(id) {
            const recording = recordings.find(r => r.id === id);
            if (recording) {
                window.egbank.logAction('recording_download', `تحميل التسجيل: ${recording.title}`, { recordingId: id });
                showNotification(`جاري تحميل: ${recording.title}`, 'info');
                // Here you would implement actual download functionality
            }
        }

        function deleteRecording(id) {
            const recording = recordings.find(r => r.id === id);
            if (recording && confirm(`هل أنت متأكد من حذف التسجيل: ${recording.title}؟`)) {
                const index = recordings.findIndex(r => r.id === id);
                recordings.splice(index, 1);
                saveRecordings();
                renderRecordings();
                updateStorageInfo();

                window.egbank.logAction('recording_delete', `حذف التسجيل: ${recording.title}`, { recordingId: id });
                showNotification('تم حذف التسجيل بنجاح', 'success');
            }
        }

        function startNewRecording() {
            const newRecording = {
                id: 'rec' + Date.now(),
                title: 'تسجيل جديد - ' + new Date().toLocaleString('ar-EG'),
                camera: 'camera1',
                cameraName: 'كاميرا المدخل الرئيسي',
                startTime: new Date().toISOString(),
                endTime: null,
                duration: 0,
                size: 0,
                type: 'manual',
                status: 'recording',
                quality: 'HD',
                hasAudio: true,
                motionDetected: false,
                thumbnail: null
            };

            recordings.unshift(newRecording);
            saveRecordings();
            renderRecordings();
            updateStorageInfo();

            window.egbank.logAction('recording_start', 'بدء تسجيل جديد', { recordingId: newRecording.id });
            showNotification('تم بدء التسجيل الجديد', 'success');
        }

        // Initialize page when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Recordings page DOM loaded...');

            // If EG BANK system is already ready, initialize immediately
            if (window.EGBankApp && window.EGBankApp.isInitialized) {
                initializeRecordingsPage();
            }
        });

        console.log('Recordings Management System Loaded Successfully');
    </script>
</body>
</html>
