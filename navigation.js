// Advanced Navigation System
// نظام التنقل المتقدم

class NavigationManager {
    constructor() {
        this.currentPage = this.getCurrentPage();
        this.navigationHistory = JSON.parse(localStorage.getItem('navigationHistory') || '[]');
        this.init();
    }

    init() {
        this.addNavigationBar();
        this.setupKeyboardShortcuts();
        this.trackNavigation();
        console.log('Navigation Manager initialized');
    }

    // Get current page name
    getCurrentPage() {
        const path = window.location.pathname;
        const filename = path.split('/').pop();
        return filename.replace('.html', '') || 'index';
    }

    // Add navigation bar to pages
    addNavigationBar() {
        // Skip navigation bar on login page
        if (this.currentPage === 'login') return;

        const navBar = this.createNavigationBar();
        document.body.insertBefore(navBar, document.body.firstChild);
    }

    // Create navigation bar HTML
    createNavigationBar() {
        const nav = document.createElement('nav');
        nav.className = 'advanced-nav-bar';
        nav.innerHTML = `
            <div class="nav-container">
                <div class="nav-left">
                    <button class="nav-btn home-btn" onclick="navigateToPage('home')" title="الصفحة الرئيسية">
                        🏠 الرئيسية
                    </button>
                    <button class="nav-btn back-btn" onclick="goBack()" title="رجوع">
                        ← رجوع
                    </button>
                </div>
                
                <div class="nav-center">
                    <div class="breadcrumb" id="breadcrumb">
                        <!-- Breadcrumb will be populated here -->
                    </div>
                </div>
                
                <div class="nav-right">
                    <div class="nav-menu">
                        <button class="nav-btn menu-toggle" onclick="toggleNavMenu()" title="القائمة">
                            ☰ القائمة
                        </button>
                        <div class="nav-dropdown" id="navDropdown">
                            <a href="home.html" class="nav-link">🏠 الصفحة الرئيسية</a>
                            <a href="cameras.html" class="nav-link">📹 نظام المراقبة</a>
                            <a href="access.html" class="nav-link">🔐 التحكم في الوصول</a>
                            <a href="gps.html" class="nav-link">🛰️ تتبع الصرافات</a>
                            <a href="reports.html" class="nav-link">📊 التقارير</a>
                            <a href="recordings_management.html" class="nav-link">💾 إدارة التسجيلات</a>
                            <a href="settings.html" class="nav-link">⚙️ الإعدادات</a>
                            <div class="nav-divider"></div>
                            <a href="#" onclick="logout()" class="nav-link logout-link">🚪 تسجيل الخروج</a>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add CSS styles
        this.addNavigationStyles();
        
        // Update breadcrumb
        setTimeout(() => this.updateBreadcrumb(), 100);

        return nav;
    }

    // Add navigation styles
    addNavigationStyles() {
        if (document.getElementById('nav-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'nav-styles';
        styles.textContent = `
            .advanced-nav-bar {
                background: rgba(26, 54, 93, 0.95);
                backdrop-filter: blur(20px);
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                padding: 15px 0;
                position: sticky;
                top: 0;
                z-index: 1000;
                font-family: 'Cairo', sans-serif;
            }
            
            .nav-container {
                max-width: 1400px;
                margin: 0 auto;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 30px;
            }
            
            .nav-left, .nav-right {
                display: flex;
                align-items: center;
                gap: 15px;
            }
            
            .nav-center {
                flex: 1;
                display: flex;
                justify-content: center;
            }
            
            .nav-btn {
                background: rgba(255, 255, 255, 0.1);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.2);
                padding: 10px 20px;
                border-radius: 8px;
                cursor: pointer;
                font-weight: 500;
                transition: all 0.3s ease;
                font-size: 14px;
            }
            
            .nav-btn:hover {
                background: rgba(255, 255, 255, 0.2);
                transform: translateY(-2px);
            }
            
            .home-btn {
                background: linear-gradient(135deg, #1a365d, #2c5282);
            }
            
            .breadcrumb {
                color: rgba(255, 255, 255, 0.8);
                font-size: 14px;
                display: flex;
                align-items: center;
                gap: 8px;
            }
            
            .breadcrumb-item {
                display: flex;
                align-items: center;
                gap: 5px;
            }
            
            .breadcrumb-separator {
                color: rgba(255, 255, 255, 0.5);
            }
            
            .nav-menu {
                position: relative;
            }
            
            .nav-dropdown {
                position: absolute;
                top: 100%;
                right: 0;
                background: rgba(26, 54, 93, 0.95);
                backdrop-filter: blur(20px);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                padding: 10px 0;
                min-width: 250px;
                opacity: 0;
                visibility: hidden;
                transform: translateY(-10px);
                transition: all 0.3s ease;
                margin-top: 10px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            }
            
            .nav-dropdown.show {
                opacity: 1;
                visibility: visible;
                transform: translateY(0);
            }
            
            .nav-link {
                display: block;
                color: white;
                text-decoration: none;
                padding: 12px 20px;
                transition: all 0.3s ease;
                border-left: 3px solid transparent;
            }
            
            .nav-link:hover {
                background: rgba(255, 255, 255, 0.1);
                border-left-color: #3182ce;
                transform: translateX(5px);
            }
            
            .nav-divider {
                height: 1px;
                background: rgba(255, 255, 255, 0.1);
                margin: 10px 0;
            }
            
            .logout-link {
                color: #fc8181 !important;
            }
            
            .logout-link:hover {
                background: rgba(252, 129, 129, 0.1) !important;
                border-left-color: #fc8181 !important;
            }
            
            @media (max-width: 768px) {
                .nav-container {
                    padding: 0 15px;
                }
                
                .nav-center {
                    display: none;
                }
                
                .nav-btn {
                    padding: 8px 15px;
                    font-size: 12px;
                }
                
                .nav-dropdown {
                    min-width: 200px;
                }
            }
        `;
        
        document.head.appendChild(styles);
    }

    // Update breadcrumb
    updateBreadcrumb() {
        const breadcrumb = document.getElementById('breadcrumb');
        if (!breadcrumb) return;

        const pageNames = {
            home: '🏠 الصفحة الرئيسية',
            cameras: '📹 نظام المراقبة',
            access: '🔐 التحكم في الوصول',
            gps: '🛰️ تتبع الصرافات',
            reports: '📊 التقارير والتحليلات',
            settings: '⚙️ الإعدادات',
            recordings_management: '💾 إدارة التسجيلات',
            modern_interface: '🎨 الواجهة الحديثة',
            futuristic_dashboard: '🌌 الواجهة المستقبلية'
        };

        const currentPageName = pageNames[this.currentPage] || this.currentPage;
        
        breadcrumb.innerHTML = `
            <span class="breadcrumb-item">EG BANK</span>
            <span class="breadcrumb-separator">›</span>
            <span class="breadcrumb-item">${currentPageName}</span>
        `;
    }

    // Setup keyboard shortcuts
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Alt + H = Home
            if (e.altKey && e.key === 'h') {
                e.preventDefault();
                this.navigateToPage('home');
            }
            
            // Alt + B = Back
            if (e.altKey && e.key === 'b') {
                e.preventDefault();
                this.goBack();
            }
            
            // Alt + M = Menu
            if (e.altKey && e.key === 'm') {
                e.preventDefault();
                this.toggleNavMenu();
            }
            
            // Escape = Close menu
            if (e.key === 'Escape') {
                this.closeNavMenu();
            }
        });
    }

    // Track navigation
    trackNavigation() {
        const currentEntry = {
            page: this.currentPage,
            timestamp: new Date().toISOString(),
            url: window.location.href
        };

        this.navigationHistory.push(currentEntry);
        
        // Keep only last 50 entries
        if (this.navigationHistory.length > 50) {
            this.navigationHistory = this.navigationHistory.slice(-50);
        }
        
        localStorage.setItem('navigationHistory', JSON.stringify(this.navigationHistory));
    }

    // Navigate to page
    navigateToPage(page) {
        const pageUrls = {
            home: 'home.html',
            cameras: 'cameras.html',
            access: 'access.html',
            gps: 'gps.html',
            reports: 'reports.html',
            settings: 'settings.html',
            recordings: 'recordings_management.html',
            modern: 'modern_interface.html',
            futuristic: 'futuristic_dashboard.html'
        };

        if (pageUrls[page]) {
            // Log navigation
            if (window.logAction) {
                window.logAction('navigation', `التنقل إلى: ${page}`, {
                    fromPage: this.currentPage,
                    toPage: page
                });
            }
            
            window.location.href = pageUrls[page];
        }
    }

    // Go back
    goBack() {
        if (this.navigationHistory.length > 1) {
            // Remove current page
            this.navigationHistory.pop();
            const previousPage = this.navigationHistory[this.navigationHistory.length - 1];
            
            if (previousPage) {
                window.location.href = previousPage.url;
            } else {
                window.history.back();
            }
        } else {
            window.history.back();
        }
    }

    // Toggle navigation menu
    toggleNavMenu() {
        const dropdown = document.getElementById('navDropdown');
        if (dropdown) {
            dropdown.classList.toggle('show');
        }
    }

    // Close navigation menu
    closeNavMenu() {
        const dropdown = document.getElementById('navDropdown');
        if (dropdown) {
            dropdown.classList.remove('show');
        }
    }

    // Get navigation history
    getNavigationHistory() {
        return this.navigationHistory;
    }

    // Clear navigation history
    clearNavigationHistory() {
        this.navigationHistory = [];
        localStorage.removeItem('navigationHistory');
    }
}

// Global navigation functions
function navigateToPage(page) {
    if (window.navManager) {
        window.navManager.navigateToPage(page);
    }
}

function goBack() {
    if (window.navManager) {
        window.navManager.goBack();
    }
}

function toggleNavMenu() {
    if (window.navManager) {
        window.navManager.toggleNavMenu();
    }
}

// Close menu when clicking outside
document.addEventListener('click', (e) => {
    const dropdown = document.getElementById('navDropdown');
    const menuToggle = document.querySelector('.menu-toggle');
    
    if (dropdown && !dropdown.contains(e.target) && !menuToggle.contains(e.target)) {
        dropdown.classList.remove('show');
    }
});

// Initialize navigation manager
document.addEventListener('DOMContentLoaded', () => {
    // Skip on login page
    const currentPage = window.location.pathname.split('/').pop().replace('.html', '');
    if (currentPage !== 'login') {
        window.navManager = new NavigationManager();
    }
});

console.log('Advanced Navigation System Loaded Successfully');
