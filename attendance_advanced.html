<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - نظام الحضور والانصراف المتقدم</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(26, 54, 93, 0.9);
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
        }
        
        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(45deg, #1a365d, #2c5282);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .logo-text {
            font-size: 28px;
            font-weight: 900;
            color: white;
        }
        
        .nav-buttons {
            display: flex;
            gap: 15px;
        }
        
        .nav-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
        }
        
        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .page-title {
            text-align: center;
            font-size: 36px;
            font-weight: 900;
            margin-bottom: 40px;
            color: #3182ce;
            text-shadow: 0 0 20px rgba(49, 130, 206, 0.5);
        }
        
        .main-grid {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .attendance-panel {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            backdrop-filter: blur(20px);
        }
        
        .panel-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 20px;
            color: white;
        }
        
        .current-time {
            font-size: 48px;
            font-weight: 900;
            color: #3182ce;
            margin-bottom: 10px;
            font-family: 'Orbitron', monospace;
            text-shadow: 0 0 15px rgba(49, 130, 206, 0.5);
        }
        
        .current-date {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 30px;
        }
        
        .employee-selector {
            margin-bottom: 20px;
        }
        
        .employee-select {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 12px;
            color: white;
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .employee-select option {
            background: #1a1a2e;
            color: white;
        }
        
        .attendance-btn {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            border: none;
            font-size: 24px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .btn-checkin {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
        }
        
        .btn-checkout {
            background: linear-gradient(135deg, #f56565, #e53e3e);
            color: white;
        }
        
        .btn-disabled {
            background: linear-gradient(135deg, #666, #888);
            color: #ccc;
            cursor: not-allowed;
        }
        
        .attendance-btn:hover:not(.btn-disabled) {
            transform: scale(1.05);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
        }
        
        .employees-panel {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(20px);
        }
        
        .employees-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .add-employee-btn {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .add-employee-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
        }
        
        .employees-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .employees-table th,
        .employees-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .employees-table th {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-weight: 600;
        }
        
        .employees-table td {
            color: rgba(255, 255, 255, 0.9);
        }
        
        .action-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            margin: 0 2px;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .edit-btn {
            background: linear-gradient(135deg, #f6e05e, #d69e2e);
            color: black;
        }
        
        .delete-btn {
            background: linear-gradient(135deg, #f56565, #e53e3e);
        }
        
        .status-present {
            color: #48bb78;
            font-weight: 600;
        }
        
        .status-absent {
            color: #f56565;
            font-weight: 600;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }
        
        .modal-content {
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            margin: 5% auto;
            padding: 30px;
            border-radius: 20px;
            width: 90%;
            max-width: 500px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .modal-title {
            font-size: 24px;
            font-weight: 700;
            color: #3182ce;
        }
        
        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 12px;
            color: white;
            font-size: 14px;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #3182ce;
            box-shadow: 0 0 0 2px rgba(49, 130, 206, 0.2);
        }
        
        .save-btn {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .save-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
        }
        
        .attendance-log {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-top: 30px;
            backdrop-filter: blur(20px);
        }
        
        .log-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            margin-bottom: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .log-employee {
            font-weight: 600;
            color: white;
        }
        
        .log-action {
            color: #3182ce;
            font-weight: 500;
        }
        
        .log-time {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
        }
        
        .voice-indicator {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px 40px;
            border-radius: 15px;
            font-size: 18px;
            font-weight: 600;
            z-index: 2000;
            display: none;
            text-align: center;
        }
        
        .voice-indicator.show {
            display: block;
            animation: fadeInOut 3s ease-in-out;
        }
        
        @keyframes fadeInOut {
            0%, 100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
            50% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        }
        
        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
            }
            
            .current-time {
                font-size: 36px;
            }
            
            .attendance-btn {
                width: 150px;
                height: 150px;
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">🏦</div>
                <div class="logo-text">EG BANK</div>
            </div>
            
            <div class="nav-buttons">
                <a href="home_simple.html" class="nav-btn">🏠 الرئيسية</a>
                <a href="attendance_reports.html" class="nav-btn">📊 التقارير</a>
                <a href="attendance_database.html" class="nav-btn">🗄️ قاعدة البيانات</a>
            </div>
        </div>
    </header>
    
    <div class="container">
        <h1 class="page-title">⏰ نظام الحضور والانصراف المتقدم</h1>
        
        <div class="main-grid">
            <!-- Attendance Panel -->
            <div class="attendance-panel">
                <h2 class="panel-title">تسجيل الحضور والانصراف</h2>
                
                <div class="current-time" id="currentTime">00:00:00</div>
                <div class="current-date" id="currentDate">الأحد، 1 يناير 2024</div>
                
                <div class="employee-selector">
                    <select class="employee-select" id="employeeSelect">
                        <option value="">اختر الموظف</option>
                    </select>
                </div>
                
                <button class="attendance-btn btn-checkin" id="attendanceBtn" onclick="toggleAttendance()">
                    <span id="btnIcon">🟢</span>
                    <span id="btnText">تسجيل حضور</span>
                </button>
                
                <div style="margin-top: 20px;">
                    <div style="font-size: 14px; color: rgba(255,255,255,0.7);">الموظف المحدد:</div>
                    <div style="font-size: 18px; font-weight: 600; color: white;" id="selectedEmployee">لم يتم الاختيار</div>
                </div>
            </div>
            
            <!-- Employees Management Panel -->
            <div class="employees-panel">
                <div class="employees-header">
                    <h2 class="panel-title">إدارة الموظفين</h2>
                    <button class="add-employee-btn" onclick="openAddEmployeeModal()">
                        ➕ إضافة موظف
                    </button>
                </div>
                
                <div style="overflow-x: auto;">
                    <table class="employees-table">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>الرقم الوظيفي</th>
                                <th>القسم</th>
                                <th>الحالة</th>
                                <th>آخر حضور</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="employeesTableBody">
                            <!-- Data will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- Attendance Log -->
        <div class="attendance-log">
            <h2 class="panel-title">سجل الحضور والانصراف اليوم</h2>
            <div id="attendanceLog">
                <!-- Log items will be populated here -->
            </div>
        </div>
    </div>
    
    <!-- Add/Edit Employee Modal -->
    <div id="employeeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">إضافة موظف جديد</h3>
                <button class="close-btn" onclick="closeEmployeeModal()">&times;</button>
            </div>
            
            <form id="employeeForm">
                <div class="form-group">
                    <label class="form-label">الاسم الكامل:</label>
                    <input type="text" class="form-input" id="employeeName" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">الرقم الوظيفي:</label>
                    <input type="text" class="form-input" id="employeeId" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">القسم:</label>
                    <select class="form-input" id="employeeDepartment" required>
                        <option value="">اختر القسم</option>
                        <option value="إدارة">إدارة</option>
                        <option value="محاسبة">محاسبة</option>
                        <option value="خدمة عملاء">خدمة عملاء</option>
                        <option value="أمان">أمان</option>
                        <option value="تقنية معلومات">تقنية معلومات</option>
                        <option value="موارد بشرية">موارد بشرية</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">المنصب:</label>
                    <input type="text" class="form-input" id="employeePosition">
                </div>
                
                <div class="form-group">
                    <label class="form-label">رقم الهاتف:</label>
                    <input type="tel" class="form-input" id="employeePhone">
                </div>
                
                <div class="form-group">
                    <label class="form-label">البريد الإلكتروني:</label>
                    <input type="email" class="form-input" id="employeeEmail">
                </div>
                
                <button type="submit" class="save-btn">💾 حفظ البيانات</button>
            </form>
        </div>
    </div>
    
    <!-- Voice Indicator -->
    <div id="voiceIndicator" class="voice-indicator">
        <div id="voiceMessage">🔊 تم تسجيل الحضور بنجاح</div>
    </div>
    
    <script src="app_core.js"></script>
    <script src="attendance_advanced.js"></script>
</body>
</html>
