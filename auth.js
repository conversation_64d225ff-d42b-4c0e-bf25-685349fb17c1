// Authentication System
const users = JSON.parse(localStorage.getItem('users')) || [
    { 
        username: 'admin', 
        password: 'admin123',
        permissions: {
            camera: true,
            access: true,
            reports: true,
            settings: true
        }
    }
];

function login(username, password) {
    const user = users.find(u => u.username === username && u.password === password);
    if (user) {
        localStorage.setItem('currentUser', JSON.stringify(user));
        return true;
    }
    return false;
}

function getCurrentUser() {
    return JSON.parse(localStorage.getItem('currentUser'));
}

function logout() {
    localStorage.removeItem('currentUser');
}

function updateUser(username, updates) {
    const index = users.findIndex(u => u.username === username);
    if (index !== -1) {
        users[index] = {...users[index], ...updates};
        localStorage.setItem('users', JSON.stringify(users));
        
        // Update current user if it's the same user
        const currentUser = getCurrentUser();
        if (currentUser && currentUser.username === username) {
            localStorage.setItem('currentUser', JSON.stringify(users[index]));
        }
        return true;
    }
    return false;
}

function addUser(user) {
    if (users.some(u => u.username === user.username)) {
        return false; // Username exists
    }
    users.push(user);
    localStorage.setItem('users', JSON.stringify(users));
    return true;
}