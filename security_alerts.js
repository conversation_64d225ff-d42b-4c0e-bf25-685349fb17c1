// Security Alerts Management JavaScript
// إدارة التنبيهات الأمنية

// Sample security alerts data
let securityAlerts = [
    {
        id: 1,
        type: 'critical',
        title: 'محاولة دخول غير مصرح بها',
        message: 'تم رصد محاولة دخول غير مصرح بها في الباب الرئيسي من عنوان IP: *************',
        timestamp: '2024-01-15 15:45:30',
        acknowledged: false
    },
    {
        id: 2,
        type: 'warning',
        title: 'فتح باب الطوارئ',
        message: 'تم فتح باب الطوارئ بدون تصريح مسبق',
        timestamp: '2024-01-15 15:30:15',
        acknowledged: false
    },
    {
        id: 3,
        type: 'critical',
        title: 'محاولات دخول متكررة فاشلة',
        message: 'تم رصد 5 محاولات دخول فاشلة متتالية في باب الخزينة',
        timestamp: '2024-01-15 15:15:22',
        acknowledged: false
    },
    {
        id: 4,
        type: 'warning',
        title: 'انقطاع الاتصال بالكاميرا',
        message: 'انقطع الاتصال بكاميرا المراقبة رقم 3 في المدخل الرئيسي',
        timestamp: '2024-01-15 14:45:10',
        acknowledged: true
    },
    {
        id: 5,
        type: 'info',
        title: 'تحديث النظام',
        message: 'تم تحديث نظام التحكم في الوصول بنجاح إلى الإصدار 2.1.5',
        timestamp: '2024-01-15 14:30:00',
        acknowledged: true
    },
    {
        id: 6,
        type: 'warning',
        title: 'باب مفتوح لفترة طويلة',
        message: 'باب المكاتب مفتوح لأكثر من 30 دقيقة',
        timestamp: '2024-01-15 14:15:33',
        acknowledged: false
    },
    {
        id: 7,
        type: 'critical',
        title: 'تنبيه أمني عاجل',
        message: 'تم رصد حركة مشبوهة في منطقة الخزينة خارج ساعات العمل',
        timestamp: '2024-01-15 13:45:18',
        acknowledged: false
    },
    {
        id: 8,
        type: 'info',
        title: 'نسخ احتياطي مجدول',
        message: 'تم إنشاء نسخة احتياطية من قاعدة البيانات بنجاح',
        timestamp: '2024-01-15 13:00:00',
        acknowledged: true
    },
    {
        id: 9,
        type: 'warning',
        title: 'مستوى البطارية منخفض',
        message: 'مستوى البطارية في جهاز التحكم رقم 4 أقل من 20%',
        timestamp: '2024-01-15 12:30:45',
        acknowledged: false
    },
    {
        id: 10,
        type: 'info',
        title: 'صيانة مجدولة',
        message: 'تم جدولة صيانة دورية لنظام التحكم في الوصول غداً الساعة 2:00 ص',
        timestamp: '2024-01-15 12:00:00',
        acknowledged: true
    }
];

// Filtered alerts for display
let filteredAlerts = [...securityAlerts];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    renderAlerts();
    updateStatistics();
    
    // Simulate new alerts
    setInterval(generateRandomAlert, 60000); // Generate new alert every minute
});

// Initialize application
function initializeApp() {
    console.log('Security Alerts Management System Initialized');
    console.log('Total Alerts:', securityAlerts.length);
}

// Render alerts
function renderAlerts() {
    const alertsList = document.getElementById('alertsList');
    alertsList.innerHTML = '';
    
    if (filteredAlerts.length === 0) {
        alertsList.innerHTML = `
            <div style="text-align: center; color: #a0a0a0; padding: 40px;">
                <h3>لا توجد تنبيهات</h3>
                <p>لا توجد تنبيهات أمنية في الوقت الحالي</p>
            </div>
        `;
        return;
    }
    
    // Sort alerts by timestamp (newest first)
    const sortedAlerts = filteredAlerts.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    
    sortedAlerts.forEach(alert => {
        const alertCard = createAlertCard(alert);
        alertsList.appendChild(alertCard);
    });
}

// Create alert card
function createAlertCard(alert) {
    const card = document.createElement('div');
    card.className = `alert-card ${alert.type}`;
    card.style.opacity = alert.acknowledged ? '0.6' : '1';
    
    const typeIcons = {
        critical: '🚨',
        warning: '⚠️',
        info: 'ℹ️'
    };
    
    const typeLabels = {
        critical: 'حرج',
        warning: 'تحذير',
        info: 'معلومات'
    };
    
    card.innerHTML = `
        <div class="alert-header">
            <div class="alert-title">
                ${typeIcons[alert.type]} ${alert.title}
                <span style="background: ${alert.type === 'critical' ? '#f56565' : alert.type === 'warning' ? '#ed8936' : '#4299e1'}; 
                             color: white; padding: 2px 8px; border-radius: 10px; font-size: 10px; margin-right: 10px;">
                    ${typeLabels[alert.type]}
                </span>
            </div>
            <div class="alert-time">${alert.timestamp}</div>
        </div>
        
        <div class="alert-message">${alert.message}</div>
        
        <div class="alert-actions">
            ${!alert.acknowledged ? `
                <button class="alert-btn btn-acknowledge" onclick="acknowledgeAlert(${alert.id})">
                    تأكيد الاستلام
                </button>
            ` : `
                <span style="color: #48bb78; font-size: 12px;">✓ تم التأكيد</span>
            `}
            <button class="alert-btn btn-dismiss" onclick="dismissAlert(${alert.id})">
                إزالة
            </button>
        </div>
    `;
    
    return card;
}

// Acknowledge alert
function acknowledgeAlert(alertId) {
    const alert = securityAlerts.find(a => a.id === alertId);
    if (alert) {
        alert.acknowledged = true;
        renderAlerts();
        updateStatistics();
        showNotification('تم تأكيد استلام التنبيه');
    }
}

// Dismiss alert
function dismissAlert(alertId) {
    const index = securityAlerts.findIndex(a => a.id === alertId);
    if (index !== -1) {
        securityAlerts.splice(index, 1);
        filteredAlerts = filteredAlerts.filter(a => a.id !== alertId);
        renderAlerts();
        updateStatistics();
        showNotification('تم حذف التنبيه');
    }
}

// Search alerts
function searchAlerts() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
    
    if (searchTerm === '') {
        filteredAlerts = [...securityAlerts];
    } else {
        filteredAlerts = securityAlerts.filter(alert => 
            alert.title.toLowerCase().includes(searchTerm) ||
            alert.message.toLowerCase().includes(searchTerm)
        );
    }
    
    renderAlerts();
    updateStatistics();
}

// Clear all alerts
function clearAllAlerts() {
    if (confirm('هل أنت متأكد من حذف جميع التنبيهات؟')) {
        securityAlerts = [];
        filteredAlerts = [];
        renderAlerts();
        updateStatistics();
        showNotification('تم حذف جميع التنبيهات');
    }
}

// Update statistics
function updateStatistics() {
    const stats = {
        total: filteredAlerts.length,
        critical: filteredAlerts.filter(a => a.type === 'critical').length,
        warning: filteredAlerts.filter(a => a.type === 'warning').length,
        info: filteredAlerts.filter(a => a.type === 'info').length
    };
    
    document.getElementById('totalAlerts').textContent = stats.total;
    document.getElementById('criticalAlerts').textContent = stats.critical;
    document.getElementById('warningAlerts').textContent = stats.warning;
    document.getElementById('infoAlerts').textContent = stats.info;
}

// Generate random alert for demonstration
function generateRandomAlert() {
    const alertTypes = ['critical', 'warning', 'info'];
    const alertTitles = {
        critical: ['محاولة دخول غير مصرح بها', 'تنبيه أمني عاجل', 'محاولات دخول متكررة فاشلة'],
        warning: ['فتح باب الطوارئ', 'انقطاع الاتصال بالكاميرا', 'باب مفتوح لفترة طويلة'],
        info: ['تحديث النظام', 'نسخ احتياطي مجدول', 'صيانة مجدولة']
    };
    
    const alertMessages = {
        critical: ['تم رصد نشاط مشبوه', 'محاولة اختراق النظام', 'تحذير أمني فوري'],
        warning: ['يتطلب انتباه المشرف', 'حالة غير طبيعية', 'تحذير من النظام'],
        info: ['عملية روتينية', 'تحديث معلومات', 'إشعار نظام']
    };
    
    if (Math.random() > 0.7) { // 30% chance of generating new alert
        const type = alertTypes[Math.floor(Math.random() * alertTypes.length)];
        const title = alertTitles[type][Math.floor(Math.random() * alertTitles[type].length)];
        const message = alertMessages[type][Math.floor(Math.random() * alertMessages[type].length)];
        
        const newAlert = {
            id: securityAlerts.length + 1,
            type: type,
            title: title,
            message: message,
            timestamp: new Date().toLocaleString('sv-SE').replace('T', ' ').substring(0, 19),
            acknowledged: false
        };
        
        securityAlerts.unshift(newAlert);
        filteredAlerts = [...securityAlerts];
        renderAlerts();
        updateStatistics();
        
        // Show notification for critical alerts
        if (type === 'critical') {
            showNotification(`تنبيه أمني جديد: ${title}`, 'error');
        }
    }
}

// Show notification
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    const bgColor = type === 'success' ? '#48bb78' : '#f56565';
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: ${bgColor};
        color: white;
        padding: 15px 25px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 1001;
        font-weight: 500;
        animation: slideDown 0.3s ease;
    `;
    
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideUp 0.3s ease';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideDown {
        from { opacity: 0; transform: translateX(-50%) translateY(-20px); }
        to { opacity: 1; transform: translateX(-50%) translateY(0); }
    }
    
    @keyframes slideUp {
        from { opacity: 1; transform: translateX(-50%) translateY(0); }
        to { opacity: 0; transform: translateX(-50%) translateY(-20px); }
    }
`;
document.head.appendChild(style);

console.log('Security Alerts Management JavaScript Loaded Successfully');
