<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - أنظمة هيك فيجن</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            min-height: 100vh;
        }

        .header {
            background: rgba(26, 54, 93, 0.9);
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(45deg, #1a365d, #2c5282);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .logo-text {
            font-size: 28px;
            font-weight: 900;
            color: white;
        }

        .nav-buttons {
            display: flex;
            gap: 15px;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .page-title {
            text-align: center;
            font-size: 36px;
            font-weight: 900;
            margin-bottom: 10px;
            color: #3182ce;
            text-shadow: 0 0 20px rgba(49, 130, 206, 0.5);
        }

        .page-subtitle {
            text-align: center;
            font-size: 18px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 40px;
        }

        .systems-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .system-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(20px);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .system-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(49, 130, 206, 0.5);
        }

        .system-icon {
            font-size: 48px;
            text-align: center;
            margin-bottom: 20px;
            display: block;
        }

        .system-title {
            font-size: 24px;
            font-weight: 700;
            text-align: center;
            margin-bottom: 15px;
            color: #3182ce;
        }

        .system-description {
            font-size: 16px;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
            margin-bottom: 20px;
        }

        .system-features {
            list-style: none;
            padding: 0;
        }

        .system-features li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
        }

        .system-features li:last-child {
            border-bottom: none;
        }

        .system-features li::before {
            content: "✓";
            color: #48bb78;
            font-weight: bold;
            margin-left: 10px;
        }

        .control-panel {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(20px);
        }

        .panel-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 20px;
            color: white;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .live-feeds {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .camera-feed {
            background: #000;
            border-radius: 10px;
            overflow: hidden;
            position: relative;
            aspect-ratio: 16/9;
            border: 2px solid rgba(255, 255, 255, 0.1);
        }

        .camera-feed.active {
            border-color: #48bb78;
        }

        .camera-feed.motion-detected {
            border-color: #f56565;
            animation: motionAlert 1s infinite;
        }

        @keyframes motionAlert {
            0%, 100% { border-color: #f56565; }
            50% { border-color: #ff8a80; }
        }

        .feed-overlay {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            font-weight: 600;
        }

        .feed-status {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #48bb78;
        }

        .feed-status.offline {
            background: #f56565;
        }

        .feed-content {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: rgba(255, 255, 255, 0.3);
        }

        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .control-btn {
            background: linear-gradient(135deg, #1a365d, #2c5282);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(26, 54, 93, 0.4);
        }

        .record-btn {
            background: linear-gradient(135deg, #f56565, #e53e3e);
        }

        .motion-btn {
            background: linear-gradient(135deg, #ed8936, #dd6b20);
        }

        .face-btn {
            background: linear-gradient(135deg, #9f7aea, #805ad5);
        }

        .alert-btn {
            background: linear-gradient(135deg, #48bb78, #38a169);
        }

        .alerts-panel {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .alert-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .alert-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .alert-icon {
            font-size: 24px;
            width: 40px;
            text-align: center;
        }

        .alert-content {
            flex: 1;
        }

        .alert-title {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .alert-time {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
        }

        .alert-critical {
            border-left: 4px solid #f56565;
        }

        .alert-warning {
            border-left: 4px solid #ed8936;
        }

        .alert-info {
            border-left: 4px solid #4299e1;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            backdrop-filter: blur(20px);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }

        .stat-icon {
            font-size: 32px;
            margin-bottom: 10px;
            display: block;
        }

        .stat-value {
            font-size: 28px;
            font-weight: 900;
            color: #3182ce;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            margin: 3% auto;
            padding: 30px;
            border-radius: 20px;
            width: 90%;
            max-width: 800px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .modal-title {
            font-size: 24px;
            font-weight: 700;
            color: #3182ce;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 28px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            color: #f56565;
            transform: scale(1.1);
        }

        @media (max-width: 768px) {
            .systems-grid {
                grid-template-columns: 1fr;
            }

            .live-feeds {
                grid-template-columns: 1fr;
            }

            .controls-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">🏦</div>
                <div class="logo-text">EG BANK</div>
            </div>

            <div class="nav-buttons">
                <a href="home_simple.html" class="nav-btn">🏠 الرئيسية</a>
                <a href="cameras.html" class="nav-btn">📹 المراقبة</a>
                <a href="laptop_camera.html" class="nav-btn">📷 الكاميرا</a>
                <a href="fire_alarm_system.html" class="nav-btn">🔥 إنذار الحريق</a>
            </div>
        </div>
    </header>

    <div class="container">
        <h1 class="page-title">🎯 أنظمة هيك فيجن</h1>
        <p class="page-subtitle">حلول أمنية متكاملة ومتقدمة للمراقبة والحماية</p>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <span class="stat-icon">📹</span>
                <div class="stat-value" id="activeCameras">12</div>
                <div class="stat-label">كاميرات نشطة</div>
            </div>

            <div class="stat-card">
                <span class="stat-icon">🎯</span>
                <div class="stat-value" id="motionDetections">3</div>
                <div class="stat-label">كشف حركة اليوم</div>
            </div>

            <div class="stat-card">
                <span class="stat-icon">👤</span>
                <div class="stat-value" id="faceRecognitions">8</div>
                <div class="stat-label">تعرف على الوجوه</div>
            </div>

            <div class="stat-card">
                <span class="stat-icon">🚨</span>
                <div class="stat-value" id="alertsToday">2</div>
                <div class="stat-label">تنبيهات اليوم</div>
            </div>
        </div>

        <!-- Systems Grid -->
        <div class="systems-grid">
            <!-- CCTV System -->
            <div class="system-card" onclick="openSystemModal('cctv')">
                <span class="system-icon">📹</span>
                <h3 class="system-title">أنظمة المراقبة بالكاميرات</h3>
                <p class="system-description">نظام مراقبة شامل بكاميرات عالية الدقة مع تسجيل مستمر وبث مباشر</p>
                <ul class="system-features">
                    <li>بث مباشر عالي الدقة (4K)</li>
                    <li>تسجيل مستمر مع التاريخ والوقت</li>
                    <li>رؤية ليلية متقدمة</li>
                    <li>مقاومة للطقس (IP67)</li>
                    <li>تحكم عن بعد PTZ</li>
                </ul>
            </div>

            <!-- Face Recognition -->
            <div class="system-card" onclick="openSystemModal('face')">
                <span class="system-icon">👤</span>
                <h3 class="system-title">أنظمة التعرف على الوجوه</h3>
                <p class="system-description">تقنية ذكية للتعرف على الأشخاص المصرح لهم ومنع الدخول غير المشروع</p>
                <ul class="system-features">
                    <li>دقة تعرف تصل إلى 99.7%</li>
                    <li>قاعدة بيانات تصل لـ 50,000 وجه</li>
                    <li>كشف الأقنعة والنظارات</li>
                    <li>تسجيل الحضور والانصراف</li>
                    <li>تنبيهات فورية للغرباء</li>
                </ul>
            </div>

            <!-- Access Control -->
            <div class="system-card" onclick="openSystemModal('access')">
                <span class="system-icon">🚪</span>
                <h3 class="system-title">أنظمة التحكم في الدخول</h3>
                <p class="system-description">تحكم ذكي في الأبواب والمداخل مع صلاحيات متعددة المستويات</p>
                <ul class="system-features">
                    <li>بطاقات RFID وبصمة الإصبع</li>
                    <li>صلاحيات حسب الوقت والمكان</li>
                    <li>سجل دخول وخروج مفصل</li>
                    <li>تحكم في عدة أبواب</li>
                    <li>إنذار عند الدخول القسري</li>
                </ul>
            </div>

            <!-- Alarm Systems -->
            <div class="system-card" onclick="openSystemModal('alarm')">
                <span class="system-icon">🚨</span>
                <h3 class="system-title">أنظمة الإنذار المتقدمة</h3>
                <p class="system-description">حماية شاملة ضد السرقة والحريق مع استجابة فورية</p>
                <ul class="system-features">
                    <li>حساسات حركة ذكية</li>
                    <li>كشف الكسر والاهتزاز</li>
                    <li>إنذار حريق ودخان</li>
                    <li>تنبيهات SMS وإيميل</li>
                    <li>ربط مع شركات الأمن</li>
                </ul>
            </div>

            <!-- Remote Surveillance -->
            <div class="system-card" onclick="openSystemModal('remote')">
                <span class="system-icon">🌐</span>
                <h3 class="system-title">المراقبة عن بُعد</h3>
                <p class="system-description">مراقبة شاملة من أي مكان في العالم عبر الإنترنت والتطبيقات</p>
                <ul class="system-features">
                    <li>تطبيق موبايل متقدم</li>
                    <li>مراقبة من أي مكان</li>
                    <li>تنبيهات فورية</li>
                    <li>تحكم كامل عن بعد</li>
                    <li>تخزين سحابي آمن</li>
                </ul>
            </div>

            <!-- AI Analytics -->
            <div class="system-card" onclick="openSystemModal('ai')">
                <span class="system-icon">🧠</span>
                <h3 class="system-title">التحليل الذكي بالذكاء الاصطناعي</h3>
                <p class="system-description">تحليل متقدم للفيديو باستخدام الذكاء الاصطناعي لكشف الأنماط والسلوكيات</p>
                <ul class="system-features">
                    <li>كشف السلوك المشبوه</li>
                    <li>عد الأشخاص والمركبات</li>
                    <li>تحليل الحشود</li>
                    <li>كشف الأشياء المتروكة</li>
                    <li>تتبع المسارات</li>
                </ul>
            </div>
        </div>

        <!-- Live Control Panel -->
        <div class="control-panel">
            <h2 class="panel-title">🎛️ لوحة التحكم المباشرة</h2>

            <!-- Live Camera Feeds -->
            <div class="live-feeds">
                <div class="camera-feed active" id="camera1">
                    <div class="feed-overlay">كاميرا المدخل الرئيسي</div>
                    <div class="feed-status"></div>
                    <div class="feed-content">📹</div>
                </div>

                <div class="camera-feed motion-detected" id="camera2">
                    <div class="feed-overlay">كاميرا الخزينة</div>
                    <div class="feed-status"></div>
                    <div class="feed-content">🎯</div>
                </div>

                <div class="camera-feed" id="camera3">
                    <div class="feed-overlay">كاميرا الموظفين</div>
                    <div class="feed-status"></div>
                    <div class="feed-content">👥</div>
                </div>

                <div class="camera-feed" id="camera4">
                    <div class="feed-overlay">كاميرا الخارجية</div>
                    <div class="feed-status"></div>
                    <div class="feed-content">🏢</div>
                </div>
            </div>

            <!-- Control Buttons -->
            <div class="controls-grid">
                <button class="control-btn record-btn" onclick="toggleRecording()">
                    🔴 بدء التسجيل
                </button>
                <button class="control-btn motion-btn" onclick="toggleMotionDetection()">
                    🎯 كشف الحركة
                </button>
                <button class="control-btn face-btn" onclick="toggleFaceRecognition()">
                    👤 التعرف على الوجوه
                </button>
                <button class="control-btn alert-btn" onclick="testAlerts()">
                    🚨 اختبار التنبيهات
                </button>
                <button class="control-btn" onclick="exportFootage()">
                    📤 تصدير التسجيلات
                </button>
                <button class="control-btn" onclick="openSettings()">
                    ⚙️ الإعدادات
                </button>
            </div>
        </div>

        <!-- Alerts Panel -->
        <div class="control-panel">
            <h2 class="panel-title">🚨 التنبيهات والإشعارات</h2>

            <div class="alerts-panel">
                <div class="alert-item alert-critical">
                    <div class="alert-icon">🚨</div>
                    <div class="alert-content">
                        <div class="alert-title">كشف حركة مشبوهة</div>
                        <div class="alert-time">منذ 5 دقائق - كاميرا الخزينة</div>
                    </div>
                </div>

                <div class="alert-item alert-warning">
                    <div class="alert-icon">⚠️</div>
                    <div class="alert-content">
                        <div class="alert-title">شخص غير مصرح له</div>
                        <div class="alert-time">منذ 15 دقيقة - المدخل الرئيسي</div>
                    </div>
                </div>

                <div class="alert-item alert-info">
                    <div class="alert-icon">ℹ️</div>
                    <div class="alert-content">
                        <div class="alert-title">تم التعرف على موظف</div>
                        <div class="alert-time">منذ 30 دقيقة - أحمد محمد</div>
                    </div>
                </div>

                <div class="alert-item alert-info">
                    <div class="alert-icon">📹</div>
                    <div class="alert-content">
                        <div class="alert-title">بدء تسجيل تلقائي</div>
                        <div class="alert-time">منذ ساعة - جميع الكاميرات</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Details Modals -->
    <div id="systemModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">تفاصيل النظام</h3>
                <button class="close-btn" onclick="closeSystemModal()">&times;</button>
            </div>

            <div id="modalContent">
                <!-- Content will be populated dynamically -->
            </div>
        </div>
    </div>

    <script>
        // Hikvision Systems Management
        class HikvisionSystems {
            constructor() {
                this.isRecording = false;
                this.motionDetectionEnabled = true;
                this.faceRecognitionEnabled = true;
                this.cameras = [
                    { id: 1, name: 'كاميرا المدخل الرئيسي', status: 'active', motionDetected: false },
                    { id: 2, name: 'كاميرا الخزينة', status: 'active', motionDetected: true },
                    { id: 3, name: 'كاميرا الموظفين', status: 'active', motionDetected: false },
                    { id: 4, name: 'كاميرا الخارجية', status: 'active', motionDetected: false }
                ];
                this.alerts = [];
                this.init();
            }

            init() {
                console.log('🎯 Initializing Hikvision Systems...');

                this.updateStatistics();
                this.simulateRealTimeData();
                this.setupEventListeners();

                console.log('✅ Hikvision Systems initialized successfully');
            }

            updateStatistics() {
                const activeCameras = this.cameras.filter(cam => cam.status === 'active').length;
                const motionDetections = this.cameras.filter(cam => cam.motionDetected).length;

                document.getElementById('activeCameras').textContent = activeCameras;
                document.getElementById('motionDetections').textContent = Math.floor(Math.random() * 10) + 1;
                document.getElementById('faceRecognitions').textContent = Math.floor(Math.random() * 15) + 5;
                document.getElementById('alertsToday').textContent = this.alerts.length;
            }

            simulateRealTimeData() {
                setInterval(() => {
                    // Simulate motion detection
                    this.cameras.forEach(camera => {
                        if (Math.random() < 0.1) { // 10% chance
                            camera.motionDetected = !camera.motionDetected;
                            this.updateCameraFeed(camera);
                        }
                    });

                    this.updateStatistics();
                }, 5000);
            }

            updateCameraFeed(camera) {
                const feedElement = document.getElementById(`camera${camera.id}`);
                if (feedElement) {
                    if (camera.motionDetected) {
                        feedElement.classList.add('motion-detected');
                    } else {
                        feedElement.classList.remove('motion-detected');
                    }
                }
            }

            setupEventListeners() {
                // Add any additional event listeners here
            }

            toggleRecording() {
                this.isRecording = !this.isRecording;
                const btn = event.target;

                if (this.isRecording) {
                    btn.innerHTML = '⏹️ إيقاف التسجيل';
                    btn.style.background = 'linear-gradient(135deg, #48bb78, #38a169)';
                    this.addAlert('بدء التسجيل', 'تم بدء تسجيل جميع الكاميرات', 'info');
                } else {
                    btn.innerHTML = '🔴 بدء التسجيل';
                    btn.style.background = 'linear-gradient(135deg, #f56565, #e53e3e)';
                    this.addAlert('إيقاف التسجيل', 'تم إيقاف تسجيل جميع الكاميرات', 'info');
                }

                console.log(`Recording ${this.isRecording ? 'started' : 'stopped'}`);
            }

            toggleMotionDetection() {
                this.motionDetectionEnabled = !this.motionDetectionEnabled;
                const btn = event.target;

                if (this.motionDetectionEnabled) {
                    btn.innerHTML = '🎯 كشف الحركة';
                    btn.style.background = 'linear-gradient(135deg, #ed8936, #dd6b20)';
                    this.addAlert('تفعيل كشف الحركة', 'تم تفعيل كشف الحركة لجميع الكاميرات', 'info');
                } else {
                    btn.innerHTML = '⏸️ إيقاف كشف الحركة';
                    btn.style.background = 'linear-gradient(135deg, #666, #888)';
                    this.addAlert('إيقاف كشف الحركة', 'تم إيقاف كشف الحركة', 'warning');
                }

                console.log(`Motion detection ${this.motionDetectionEnabled ? 'enabled' : 'disabled'}`);
            }

            toggleFaceRecognition() {
                this.faceRecognitionEnabled = !this.faceRecognitionEnabled;
                const btn = event.target;

                if (this.faceRecognitionEnabled) {
                    btn.innerHTML = '👤 التعرف على الوجوه';
                    btn.style.background = 'linear-gradient(135deg, #9f7aea, #805ad5)';
                    this.addAlert('تفعيل التعرف على الوجوه', 'تم تفعيل نظام التعرف على الوجوه', 'info');
                } else {
                    btn.innerHTML = '👤 إيقاف التعرف';
                    btn.style.background = 'linear-gradient(135deg, #666, #888)';
                    this.addAlert('إيقاف التعرف على الوجوه', 'تم إيقاف نظام التعرف على الوجوه', 'warning');
                }

                console.log(`Face recognition ${this.faceRecognitionEnabled ? 'enabled' : 'disabled'}`);
            }

            testAlerts() {
                const alertTypes = [
                    { type: 'critical', title: 'تنبيه أمني عاجل', message: 'تم اكتشاف دخول غير مصرح به' },
                    { type: 'warning', title: 'تحذير', message: 'كاميرا غير متصلة' },
                    { type: 'info', title: 'معلومات', message: 'تم تحديث النظام بنجاح' }
                ];

                const randomAlert = alertTypes[Math.floor(Math.random() * alertTypes.length)];
                this.addAlert(randomAlert.title, randomAlert.message, randomAlert.type);

                alert('تم إرسال تنبيه تجريبي');
            }

            addAlert(title, message, type) {
                const alert = {
                    id: Date.now(),
                    title,
                    message,
                    type,
                    time: new Date().toLocaleString('ar-EG')
                };

                this.alerts.unshift(alert);

                // Keep only last 10 alerts
                if (this.alerts.length > 10) {
                    this.alerts = this.alerts.slice(0, 10);
                }

                this.updateAlertsPanel();
            }

            updateAlertsPanel() {
                // This would update the alerts panel in a real implementation
                console.log('Alerts updated:', this.alerts);
            }

            exportFootage() {
                const startDate = prompt('أدخل تاريخ البداية (YYYY-MM-DD):');
                const endDate = prompt('أدخل تاريخ النهاية (YYYY-MM-DD):');

                if (startDate && endDate) {
                    // Simulate export process
                    alert(`جاري تصدير التسجيلات من ${startDate} إلى ${endDate}...\n\nسيتم إشعارك عند اكتمال العملية.`);

                    setTimeout(() => {
                        alert('تم تصدير التسجيلات بنجاح!');
                    }, 3000);
                }
            }

            openSettings() {
                alert('ستفتح صفحة الإعدادات قريباً...');
            }
        }

        // System Modal Functions
        function openSystemModal(systemType) {
            const modal = document.getElementById('systemModal');
            const title = document.getElementById('modalTitle');
            const content = document.getElementById('modalContent');

            const systemData = {
                cctv: {
                    title: 'أنظمة المراقبة بالكاميرات (CCTV)',
                    content: `
                        <h3>🎯 المواصفات التقنية:</h3>
                        <ul style="margin: 20px 0; padding-right: 20px;">
                            <li>دقة 4K Ultra HD (3840×2160)</li>
                            <li>معدل إطارات 30 FPS</li>
                            <li>رؤية ليلية حتى 50 متر</li>
                            <li>زاوية رؤية 110 درجة</li>
                            <li>مقاومة الطقس IP67</li>
                        </ul>

                        <h3>🔧 المكونات:</h3>
                        <ul style="margin: 20px 0; padding-right: 20px;">
                            <li>كاميرات IP عالية الدقة</li>
                            <li>جهاز تسجيل NVR</li>
                            <li>أقراص تخزين عالية السعة</li>
                            <li>شاشات مراقبة متعددة</li>
                            <li>كابلات وملحقات التركيب</li>
                        </ul>

                        <h3>💡 المميزات:</h3>
                        <ul style="margin: 20px 0; padding-right: 20px;">
                            <li>بث مباشر عبر الإنترنت</li>
                            <li>تسجيل مستمر أو حسب الحركة</li>
                            <li>تنبيهات فورية للأحداث</li>
                            <li>تحكم PTZ (تحريك وتكبير)</li>
                            <li>تطبيق موبايل للمراقبة</li>
                        </ul>
                    `
                },
                face: {
                    title: 'أنظمة التعرف على الوجوه',
                    content: `
                        <h3>🧠 تقنية الذكاء الاصطناعي:</h3>
                        <ul style="margin: 20px 0; padding-right: 20px;">
                            <li>خوارزميات التعلم العميق</li>
                            <li>دقة تعرف 99.7%</li>
                            <li>سرعة استجابة أقل من ثانية</li>
                            <li>كشف الوجوه في الإضاءة المنخفضة</li>
                            <li>تمييز الوجوه مع الأقنعة</li>
                        </ul>

                        <h3>📊 قاعدة البيانات:</h3>
                        <ul style="margin: 20px 0; padding-right: 20px;">
                            <li>تخزين حتى 50,000 وجه</li>
                            <li>تصنيف حسب المجموعات</li>
                            <li>سجل دخول وخروج مفصل</li>
                            <li>تقارير الحضور والانصراف</li>
                            <li>نسخ احتياطية آمنة</li>
                        </ul>

                        <h3>🔒 الأمان:</h3>
                        <ul style="margin: 20px 0; padding-right: 20px;">
                            <li>تشفير البيانات البيومترية</li>
                            <li>حماية من التلاعب</li>
                            <li>مصادقة متعددة العوامل</li>
                            <li>سجل أمان شامل</li>
                            <li>تنبيهات الدخول غير المصرح</li>
                        </ul>
                    `
                },
                access: {
                    title: 'أنظمة التحكم في الدخول',
                    content: `
                        <h3>🔑 طرق المصادقة:</h3>
                        <ul style="margin: 20px 0; padding-right: 20px;">
                            <li>بطاقات RFID/NFC</li>
                            <li>بصمة الإصبع</li>
                            <li>التعرف على الوجه</li>
                            <li>رمز PIN</li>
                            <li>مصادقة متعددة العوامل</li>
                        </ul>

                        <h3>⏰ إدارة الصلاحيات:</h3>
                        <ul style="margin: 20px 0; padding-right: 20px;">
                            <li>صلاحيات حسب الوقت</li>
                            <li>صلاحيات حسب المنطقة</li>
                            <li>مجموعات مستخدمين</li>
                            <li>صلاحيات مؤقتة</li>
                            <li>إدارة الزوار</li>
                        </ul>

                        <h3>🚪 التحكم في الأبواب:</h3>
                        <ul style="margin: 20px 0; padding-right: 20px;">
                            <li>أقفال إلكترونية ذكية</li>
                            <li>أبواب دوارة وحواجز</li>
                            <li>أنظمة الطوارئ</li>
                            <li>مراقبة حالة الأبواب</li>
                            <li>تحكم عن بعد</li>
                        </ul>
                    `
                },
                alarm: {
                    title: 'أنظمة الإنذار المتقدمة',
                    content: `
                        <h3>🔍 أنواع الحساسات:</h3>
                        <ul style="margin: 20px 0; padding-right: 20px;">
                            <li>حساسات الحركة PIR</li>
                            <li>حساسات الكسر والاهتزاز</li>
                            <li>حساسات الدخان والحريق</li>
                            <li>حساسات الفيضان</li>
                            <li>حساسات الغازات</li>
                        </ul>

                        <h3>📱 التنبيهات:</h3>
                        <ul style="margin: 20px 0; padding-right: 20px;">
                            <li>رسائل SMS فورية</li>
                            <li>إشعارات التطبيق</li>
                            <li>مكالمات صوتية</li>
                            <li>إيميلات مع الصور</li>
                            <li>ربط مع شركات الأمن</li>
                        </ul>

                        <h3>🎛️ لوحة التحكم:</h3>
                        <ul style="margin: 20px 0; padding-right: 20px;">
                            <li>شاشة تعمل باللمس</li>
                            <li>لوحة مفاتيح مشفرة</li>
                            <li>مؤشرات LED ملونة</li>
                            <li>صفارات إنذار متدرجة</li>
                            <li>بطارية احتياطية</li>
                        </ul>
                    `
                },
                remote: {
                    title: 'المراقبة عن بُعد',
                    content: `
                        <h3>📱 التطبيقات:</h3>
                        <ul style="margin: 20px 0; padding-right: 20px;">
                            <li>تطبيق iOS و Android</li>
                            <li>واجهة ويب متقدمة</li>
                            <li>برنامج كمبيوتر مخصص</li>
                            <li>دعم الأجهزة اللوحية</li>
                            <li>واجهة Apple TV</li>
                        </ul>

                        <h3>🌐 الاتصال:</h3>
                        <ul style="margin: 20px 0; padding-right: 20px;">
                            <li>اتصال إنترنت مشفر</li>
                            <li>شبكة VPN آمنة</li>
                            <li>اتصال 4G/5G</li>
                            <li>Wi-Fi مزدوج التردد</li>
                            <li>اتصال احتياطي</li>
                        </ul>

                        <h3>☁️ التخزين السحابي:</h3>
                        <ul style="margin: 20px 0; padding-right: 20px;">
                            <li>تخزين آمن مشفر</li>
                            <li>نسخ احتياطية تلقائية</li>
                            <li>وصول من أي مكان</li>
                            <li>مشاركة محدودة</li>
                            <li>أرشفة طويلة المدى</li>
                        </ul>
                    `
                },
                ai: {
                    title: 'التحليل الذكي بالذكاء الاصطناعي',
                    content: `
                        <h3>🤖 خوارزميات الذكاء الاصطناعي:</h3>
                        <ul style="margin: 20px 0; padding-right: 20px;">
                            <li>التعلم العميق (Deep Learning)</li>
                            <li>الشبكات العصبية التطورية</li>
                            <li>معالجة الصور المتقدمة</li>
                            <li>تحليل الأنماط السلوكية</li>
                            <li>التعرف على الكائنات</li>
                        </ul>

                        <h3>📊 التحليلات:</h3>
                        <ul style="margin: 20px 0; padding-right: 20px;">
                            <li>عد الأشخاص والمركبات</li>
                            <li>تحليل كثافة الحشود</li>
                            <li>كشف السلوك المشبوه</li>
                            <li>تتبع المسارات</li>
                            <li>إحصائيات الحركة</li>
                        </ul>

                        <h3>⚡ الاستجابة الذكية:</h3>
                        <ul style="margin: 20px 0; padding-right: 20px;">
                            <li>تنبيهات ذكية مخصصة</li>
                            <li>تصنيف الأحداث تلقائياً</li>
                            <li>تقليل الإنذارات الكاذبة</li>
                            <li>تحسين الأداء المستمر</li>
                            <li>تقارير تحليلية شاملة</li>
                        </ul>
                    `
                }
            };

            const system = systemData[systemType];
            if (system) {
                title.textContent = system.title;
                content.innerHTML = system.content;
                modal.style.display = 'block';
            }
        }

        function closeSystemModal() {
            document.getElementById('systemModal').style.display = 'none';
        }

        // Global functions
        function toggleRecording() {
            if (window.hikvisionSystems) {
                window.hikvisionSystems.toggleRecording();
            }
        }

        function toggleMotionDetection() {
            if (window.hikvisionSystems) {
                window.hikvisionSystems.toggleMotionDetection();
            }
        }

        function toggleFaceRecognition() {
            if (window.hikvisionSystems) {
                window.hikvisionSystems.toggleFaceRecognition();
            }
        }

        function testAlerts() {
            if (window.hikvisionSystems) {
                window.hikvisionSystems.testAlerts();
            }
        }

        function exportFootage() {
            if (window.hikvisionSystems) {
                window.hikvisionSystems.exportFootage();
            }
        }

        function openSettings() {
            if (window.hikvisionSystems) {
                window.hikvisionSystems.openSettings();
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎯 Hikvision Systems loading...');
            window.hikvisionSystems = new HikvisionSystems();
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            const modal = document.getElementById('systemModal');
            if (event.target === modal) {
                closeSystemModal();
            }
        });

        console.log('🎯 Hikvision Systems Script Loaded');
    </script>
</body>
</html>