<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - Settings</title>
    <link rel="stylesheet" href="style.css">
    <style>
        #loading-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #121212;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .loader {
            border: 16px solid #301934; /* Dark purple */
            border-top: 16px solid #4B0082; /* Darker purple */
            border-radius: 50%;
            width: 120px;
            height: 120px;
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
    <script src="auth.js"></script>
</head>
<body>
    <div id="loading-container">
        <div class="loader"></div>
    </div>
    <script>
        window.addEventListener('load', function() {
            document.getElementById('loading-container').style.display = 'none';
        });
    </script>
    <!-- Banking Header -->
    <header class="bank-header">
        <div class="bank-logo">EG BANK</div>
        <nav class="bank-nav">
            <ul>
                <li><a href="index.html" style="color: white;">Dashboard</a></li>
                <li><a href="cameras.html" style="color: white;">Security</a></li>
                <li><a href="access.html" style="color: white;">Access Control</a></li>
                <li><a href="reports.html" style="color: white;">Reports</a></li>
                <li><a href="settings.html" class="active" style="color: white;">Settings</a></li>
                <li><a href="gps.html" style="color: white;">GPS Tracking</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main Content Area -->
    <div class="bank-container">
        <!-- Sidebar -->
        <aside class="bank-sidebar">
            <h3 style="color: white;">Abdel Fattah Mahmoud</h3>
            <ul>
                <li><a href="index.html" style="color: white;">Camera Surveillance</a></li>
                <li><a href="cameras.html" style="color: white;">IP Camera Management</a></li>
                <li><a href="access.html" style="color: white;">Access Control</a></li>
                <li><a href="reports.html" style="color: white;">Reports</a></li>
                <li class="active" style="color: white;">User Management</li>
            </ul>
        </aside>

        <!-- Settings Section -->
        <main class="bank-main">
            <h1 style="color: white;">User Management</h1>

            <!-- Current User Info -->
            <div class="current-user">
                <h2 style="color: white;">Current User: <span id="currentUsername">admin</span></h2>
                <div class="form-container" style="background: linear-gradient(135deg, #1a1f36, #2d3748);">
                    <h3 style="color: #ff6b6b; margin-bottom: 20px;">Change Password</h3>
                    <div class="form-group">
                        <label for="currentPassword" style="color: #f8f9fa;">Current Password:</label>
                        <input type="password" id="currentPassword" class="vibrant-input">
                    </div>
                    <div class="form-group">
                        <label for="newPassword" style="color: #f8f9fa;">New Password:</label>
                        <input type="password" id="newPassword" class="vibrant-input">
                    </div>
                    <div class="form-group">
                        <label for="confirmPassword" style="color: #f8f9fa;">Confirm Password:</label>
                        <input type="password" id="confirmPassword" class="vibrant-input">
                    </div>
                    <div class="form-actions">
                        <button id="changePasswordBtn" style="background: linear-gradient(135deg, #ff6b6b, #ff8e8e); color: white;">Change Password</button>
                    </div>
                </div>
            </div>

            <!-- User Management -->
            <div class="form-container" style="background: linear-gradient(135deg, #1a1f36, #2d3748); margin-top: 30px;">
                <h3 style="color: #4fd1c5; margin-bottom: 20px;">Manage Users</h3>
                <form id="userForm">
                    <div class="form-group">
                        <label for="username" style="color: #f8f9fa;">Username:</label>
                        <input type="text" id="username" class="vibrant-input" required>
                    </div>

                    <div class="form-group">
                        <label for="password" style="color: #f8f9fa;">Password:</label>
                        <input type="password" id="password" class="vibrant-input" required>
                    </div>

                    <h4 style="color: #f8f9fa; margin: 20px 0 10px;">Permissions:</h4>
                    <div class="permissions-grid">
                        <div class="permission-item">
                            <input type="checkbox" id="permCamera" name="permissions" value="camera" checked>
                            <label for="permCamera" style="color: #f8f9fa;">Camera Access</label>
                        </div>
                        <div class="permission-item">
                            <input type="checkbox" id="permAccess" name="permissions" value="access" checked>
                            <label for="permAccess" style="color: #f8f9fa;">Access Control</label>
                        </div>
                        <div class="permission-item">
                            <input type="checkbox" id="permReports" name="permissions" value="reports" checked>
                            <label for="permReports" style="color: #f8f9fa;">Reports</label>
                        </div>
                        <div class="permission-item">
                            <input type="checkbox" id="permSettings" name="permissions" value="settings" checked>
                            <label for="permSettings" style="color: #f8f9fa;">Settings</label>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" id="addUserBtn" style="background: linear-gradient(135deg, #4fd1c5, #81e6d9); color: white;">Add User</button>
                        <button type="button" id="editUserBtn" disabled style="background: linear-gradient(135deg, #f6e05e, #faf089); color: white;">Edit User</button>
                        <button type="button" id="deleteUserBtn" disabled style="background: linear-gradient(135deg, #fc8181, #fed7d7); color: white;">Delete User</button>
                        <button type="reset" style="background: linear-gradient(135deg, #a0aec0, #cbd5e0); color: white;">Clear</button>
                    </div>
                </form>

                <div class="user-list">
                    <h3 style="color: #f8f9fa; margin-top: 30px; border-bottom: 1px solid #4a5568; padding-bottom: 10px;">User List</h3>
                    <table id="userTable" style="width: 100%;">
                        <thead>
                            <tr>
                                <th style="background: #2d3748; color: #f8f9fa;">Username</th>
                                <th style="background: #2d3748; color: #f8f9fa;">Permissions</th>
                                <th style="background: #2d3748; color: #f8f9fa;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- User entries will be added here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <!-- Banking Footer -->
    <footer class="bank-footer">
    </footer>

    <!-- Floating Toolbar -->
    <div class="floating-toolbar">
        <div class="toolbar-header">
            لوحة التحكم الرئيسية
        </div>

        <div class="toolbar-section">
            <div class="toolbar-section-title">Control Panel</div>
            <a href="index.html" class="toolbar-item">
                <div class="toolbar-item-icon">🏠</div>
                <div class="toolbar-item-text">لوحة التحكم</div>
            </a>
            <a href="cameras.html" class="toolbar-item">
                <div class="toolbar-item-icon">🔒</div>
                <div class="toolbar-item-text">الأمان</div>
            </a>
            <a href="access.html" class="toolbar-item">
                <div class="toolbar-item-icon">🚪</div>
                <div class="toolbar-item-text">التحكم في الوصول</div>
            </a>
            <a href="reports.html" class="toolbar-item">
                <div class="toolbar-item-icon">📊</div>
                <div class="toolbar-item-text">التقارير</div>
            </a>
            <a href="settings.html" class="toolbar-item active">
                <div class="toolbar-item-icon">⚙️</div>
                <div class="toolbar-item-text">الإعدادات</div>
            </a>
            <a href="gps.html" class="toolbar-item">
                <div class="toolbar-item-icon">📍</div>
                <div class="toolbar-item-text">تتبع GPS</div>
            </a>
        </div>

        <div class="toolbar-section">
            <div class="toolbar-section-title">Abdel Fattah Mahmoud</div>
            <a href="index.html" class="toolbar-item">
                <div class="toolbar-item-icon">📹</div>
                <div class="toolbar-item-text">مراقبة الكاميرات</div>
            </a>
            <a href="cameras.html" class="toolbar-item">
                <div class="toolbar-item-icon">📷</div>
                <div class="toolbar-item-text">إدارة كاميرات IP</div>
            </a>
            <a href="access.html" class="toolbar-item">
                <div class="toolbar-item-icon">🔐</div>
                <div class="toolbar-item-text">التحكم في الوصول</div>
            </a>
            <a href="reports.html" class="toolbar-item">
                <div class="toolbar-item-icon">📈</div>
                <div class="toolbar-item-text">التقارير</div>
            </a>
            <a href="user_management.html" class="toolbar-item">
                <div class="toolbar-item-icon">👥</div>
                <div class="toolbar-item-text">إدارة المستخدمين</div>
            </a>
        </div>

        <div class="toolbar-user">
            <div class="toolbar-user-name">عبد الفتاح محمود</div>
            <div class="toolbar-user-role">مدير النظام</div>
        </div>
    </div>

    <script src="settings.js"></script>
</body>
</html>