<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - Settings</title>
    <link rel="stylesheet" href="style.css">
    <style>
        #loading-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #121212;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .loader {
            border: 16px solid #301934; /* Dark purple */
            border-top: 16px solid #4B0082; /* Darker purple */
            border-radius: 50%;
            width: 120px;
            height: 120px;
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
    <script src="app_core.js"></script>
</head>
<body>
    <div id="loading-container">
        <div class="loader"></div>
    </div>
    <script>
        window.addEventListener('load', function() {
            document.getElementById('loading-container').style.display = 'none';
        });
    </script>
    <!-- Banking Header -->
    <header class="bank-header">
        <div class="bank-logo">EG BANK</div>
        <nav class="bank-nav">
            <ul>
                <li><a href="index.html" style="color: white;">Dashboard</a></li>
                <li><a href="cameras.html" style="color: white;">Security</a></li>
                <li><a href="access.html" style="color: white;">Access Control</a></li>
                <li><a href="reports.html" style="color: white;">Reports</a></li>
                <li><a href="settings.html" class="active" style="color: white;">Settings</a></li>
                <li><a href="gps.html" style="color: white;">GPS Tracking</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main Content Area -->
    <div class="bank-container">
        <!-- Sidebar -->
        <aside class="bank-sidebar">
            <h3 style="color: white;">Abdel Fattah Mahmoud</h3>
            <ul>
                <li><a href="index.html" style="color: white;">Camera Surveillance</a></li>
                <li><a href="cameras.html" style="color: white;">IP Camera Management</a></li>
                <li><a href="access.html" style="color: white;">Access Control</a></li>
                <li><a href="reports.html" style="color: white;">Reports</a></li>
                <li class="active" style="color: white;">User Management</li>
            </ul>
        </aside>

        <!-- Settings Section -->
        <main class="bank-main">
            <h1 style="color: white;">User Management</h1>

            <!-- Current User Info -->
            <div class="current-user">
                <h2 style="color: white;">Current User: <span id="currentUsername">admin</span></h2>
                <div class="form-container" style="background: linear-gradient(135deg, #1a1f36, #2d3748);">
                    <h3 style="color: #ff6b6b; margin-bottom: 20px;">Change Password</h3>
                    <div class="form-group">
                        <label for="currentPassword" style="color: #f8f9fa;">Current Password:</label>
                        <input type="password" id="currentPassword" class="vibrant-input">
                    </div>
                    <div class="form-group">
                        <label for="newPassword" style="color: #f8f9fa;">New Password:</label>
                        <input type="password" id="newPassword" class="vibrant-input">
                    </div>
                    <div class="form-group">
                        <label for="confirmPassword" style="color: #f8f9fa;">Confirm Password:</label>
                        <input type="password" id="confirmPassword" class="vibrant-input">
                    </div>
                    <div class="form-actions">
                        <button id="changePasswordBtn" style="background: linear-gradient(135deg, #ff6b6b, #ff8e8e); color: white;">Change Password</button>
                    </div>
                </div>
            </div>

            <!-- Current User Settings -->
            <div class="form-container" style="background: linear-gradient(135deg, #1a365d, #2c5282); margin-top: 30px;">
                <h3 style="color: white; margin-bottom: 20px;">🔐 إعدادات المستخدم الحالي</h3>
                <form id="currentUserForm">
                    <div class="form-group">
                        <label for="currentUsername" style="color: #f8f9fa;">اسم المستخدم الحالي:</label>
                        <input type="text" id="currentUsername" class="vibrant-input" readonly style="background: #2c2c2c;">
                    </div>

                    <div class="form-group">
                        <label for="newUsername" style="color: #f8f9fa;">اسم المستخدم الجديد:</label>
                        <input type="text" id="newUsername" class="vibrant-input" placeholder="أدخل اسم المستخدم الجديد">
                    </div>

                    <div class="form-group">
                        <label for="currentPassword" style="color: #f8f9fa;">كلمة المرور الحالية:</label>
                        <input type="password" id="currentPassword" class="vibrant-input" placeholder="أدخل كلمة المرور الحالية">
                    </div>

                    <div class="form-group">
                        <label for="newPassword" style="color: #f8f9fa;">كلمة المرور الجديدة:</label>
                        <input type="password" id="newPassword" class="vibrant-input" placeholder="أدخل كلمة المرور الجديدة">
                    </div>

                    <div class="form-group">
                        <label for="confirmPassword" style="color: #f8f9fa;">تأكيد كلمة المرور:</label>
                        <input type="password" id="confirmPassword" class="vibrant-input" placeholder="أعد إدخال كلمة المرور الجديدة">
                    </div>

                    <div class="form-actions">
                        <button type="button" onclick="updateCurrentUser()" style="background: linear-gradient(135deg, #1a365d, #2c5282); color: white; padding: 12px 25px; border: none; border-radius: 8px; cursor: pointer; font-weight: 600;">
                            💾 حفظ التغييرات
                        </button>
                        <button type="button" onclick="resetCurrentUserForm()" style="background: linear-gradient(135deg, #555555, #666666); color: white; padding: 12px 25px; border: none; border-radius: 8px; cursor: pointer; font-weight: 600;">
                            🔄 إعادة تعيين
                        </button>
                    </div>
                </form>
            </div>

            <!-- User Management -->
            <div class="form-container" style="background: linear-gradient(135deg, #1a1f36, #2d3748); margin-top: 30px;">
                <h3 style="color: #4fd1c5; margin-bottom: 20px;">👥 إدارة المستخدمين</h3>
                <form id="userForm">
                    <div class="form-group">
                        <label for="username" style="color: #f8f9fa;">اسم المستخدم:</label>
                        <input type="text" id="username" class="vibrant-input" placeholder="أدخل اسم المستخدم" required>
                    </div>

                    <div class="form-group">
                        <label for="fullName" style="color: #f8f9fa;">الاسم الكامل:</label>
                        <input type="text" id="fullName" class="vibrant-input" placeholder="أدخل الاسم الكامل" required>
                    </div>

                    <div class="form-group">
                        <label for="email" style="color: #f8f9fa;">البريد الإلكتروني:</label>
                        <input type="email" id="email" class="vibrant-input" placeholder="أدخل البريد الإلكتروني">
                    </div>

                    <div class="form-group">
                        <label for="password" style="color: #f8f9fa;">كلمة المرور:</label>
                        <input type="password" id="password" class="vibrant-input" placeholder="أدخل كلمة المرور" required>
                    </div>

                    <div class="form-group">
                        <label for="userRole" style="color: #f8f9fa;">الدور:</label>
                        <select id="userRole" class="vibrant-input" required>
                            <option value="">اختر الدور</option>
                            <option value="admin">مدير النظام</option>
                            <option value="operator">مشغل</option>
                            <option value="viewer">مشاهد</option>
                            <option value="security">أمان</option>
                        </select>
                    </div>

                    <h4 style="color: #f8f9fa; margin: 20px 0 10px;">الصلاحيات:</h4>
                    <div class="permissions-grid" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
                        <div class="permission-item" style="display: flex; align-items: center; gap: 8px;">
                            <input type="checkbox" id="permCamera" name="permissions" value="camera" checked>
                            <label for="permCamera" style="color: #f8f9fa;">📹 نظام المراقبة</label>
                        </div>
                        <div class="permission-item" style="display: flex; align-items: center; gap: 8px;">
                            <input type="checkbox" id="permAccess" name="permissions" value="access" checked>
                            <label for="permAccess" style="color: #f8f9fa;">🚪 التحكم في الوصول</label>
                        </div>
                        <div class="permission-item" style="display: flex; align-items: center; gap: 8px;">
                            <input type="checkbox" id="permGPS" name="permissions" value="gps" checked>
                            <label for="permGPS" style="color: #f8f9fa;">🏧 تتبع الصرافات</label>
                        </div>
                        <div class="permission-item" style="display: flex; align-items: center; gap: 8px;">
                            <input type="checkbox" id="permReports" name="permissions" value="reports" checked>
                            <label for="permReports" style="color: #f8f9fa;">📊 التقارير</label>
                        </div>
                        <div class="permission-item" style="display: flex; align-items: center; gap: 8px;">
                            <input type="checkbox" id="permSettings" name="permissions" value="settings">
                            <label for="permSettings" style="color: #f8f9fa;">⚙️ الإعدادات</label>
                        </div>
                        <div class="permission-item" style="display: flex; align-items: center; gap: 8px;">
                            <input type="checkbox" id="permUsers" name="permissions" value="users">
                            <label for="permUsers" style="color: #f8f9fa;">👥 إدارة المستخدمين</label>
                        </div>
                    </div>

                    <div class="form-actions" style="display: flex; gap: 10px; margin-top: 20px;">
                        <button type="button" id="addUserBtn" style="background: linear-gradient(135deg, #4fd1c5, #81e6d9); color: white; padding: 12px 20px; border: none; border-radius: 8px; cursor: pointer; font-weight: 600;">
                            ➕ إضافة مستخدم
                        </button>
                        <button type="button" id="editUserBtn" disabled style="background: linear-gradient(135deg, #f6e05e, #faf089); color: white; padding: 12px 20px; border: none; border-radius: 8px; cursor: pointer; font-weight: 600;">
                            ✏️ تعديل مستخدم
                        </button>
                        <button type="button" id="deleteUserBtn" disabled style="background: linear-gradient(135deg, #fc8181, #fed7d7); color: white; padding: 12px 20px; border: none; border-radius: 8px; cursor: pointer; font-weight: 600;">
                            🗑️ حذف مستخدم
                        </button>
                        <button type="reset" style="background: linear-gradient(135deg, #a0aec0, #cbd5e0); color: white; padding: 12px 20px; border: none; border-radius: 8px; cursor: pointer; font-weight: 600;">
                            🔄 مسح
                        </button>
                    </div>
                </form>

                <div class="user-list">
                    <h3 style="color: #f8f9fa; margin-top: 30px; border-bottom: 1px solid #4a5568; padding-bottom: 10px;">📋 قائمة المستخدمين</h3>
                    <table id="userTable" style="width: 100%; border-collapse: collapse; background: #2c2c2c; border-radius: 8px; overflow: hidden;">
                        <thead>
                            <tr style="background: linear-gradient(135deg, #1a365d, #2c5282);">
                                <th style="padding: 15px; color: white; text-align: right;">اسم المستخدم</th>
                                <th style="padding: 15px; color: white; text-align: right;">الاسم الكامل</th>
                                <th style="padding: 15px; color: white; text-align: right;">البريد الإلكتروني</th>
                                <th style="padding: 15px; color: white; text-align: right;">الدور</th>
                                <th style="padding: 15px; color: white; text-align: right;">الصلاحيات</th>
                                <th style="padding: 15px; color: white; text-align: right;">الحالة</th>
                                <th style="padding: 15px; color: white; text-align: center;">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <!-- User entries will be added here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Database Management -->
            <div class="form-container" style="background: linear-gradient(135deg, #2d1b69, #553c9a); margin-top: 30px;">
                <h3 style="color: white; margin-bottom: 20px;">💾 إدارة قاعدة البيانات</h3>

                <!-- Database Statistics -->
                <div style="background: rgba(255,255,255,0.1); border-radius: 8px; padding: 20px; margin-bottom: 20px;">
                    <h4 style="color: white; margin-bottom: 15px;">📊 إحصائيات قاعدة البيانات</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px;">
                        <div style="text-align: center; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 6px;">
                            <div style="color: #4fd1c5; font-size: 24px; font-weight: bold;" id="dbUsers">0</div>
                            <div style="color: white; font-size: 12px;">المستخدمين</div>
                        </div>
                        <div style="text-align: center; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 6px;">
                            <div style="color: #4fd1c5; font-size: 24px; font-weight: bold;" id="dbRecordings">0</div>
                            <div style="color: white; font-size: 12px;">التسجيلات</div>
                        </div>
                        <div style="text-align: center; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 6px;">
                            <div style="color: #4fd1c5; font-size: 24px; font-weight: bold;" id="dbLogs">0</div>
                            <div style="color: white; font-size: 12px;">سجلات الوصول</div>
                        </div>
                        <div style="text-align: center; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 6px;">
                            <div style="color: #4fd1c5; font-size: 24px; font-weight: bold;" id="dbSize">0 KB</div>
                            <div style="color: white; font-size: 12px;">حجم البيانات</div>
                        </div>
                    </div>
                </div>

                <!-- Backup and Restore -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px;">
                    <button onclick="createBackup()" style="background: linear-gradient(135deg, #38a169, #2f855a); color: white; padding: 15px; border: none; border-radius: 8px; cursor: pointer; font-weight: 600;">
                        💾 إنشاء نسخة احتياطية
                    </button>
                    <button onclick="document.getElementById('restoreFile').click()" style="background: linear-gradient(135deg, #3182ce, #2c5282); color: white; padding: 15px; border: none; border-radius: 8px; cursor: pointer; font-weight: 600;">
                        📥 استعادة نسخة احتياطية
                    </button>
                    <button onclick="cleanOldData()" style="background: linear-gradient(135deg, #ed8936, #dd6b20); color: white; padding: 15px; border: none; border-radius: 8px; cursor: pointer; font-weight: 600;">
                        🗑️ تنظيف البيانات القديمة
                    </button>
                    <button onclick="exportAllData()" style="background: linear-gradient(135deg, #805ad5, #6b46c1); color: white; padding: 15px; border: none; border-radius: 8px; cursor: pointer; font-weight: 600;">
                        📤 تصدير جميع البيانات
                    </button>
                </div>

                <input type="file" id="restoreFile" accept=".json" style="display: none;" onchange="restoreBackup(this.files[0])">

                <!-- Data Management Options -->
                <div style="background: rgba(255,255,255,0.1); border-radius: 8px; padding: 20px;">
                    <h4 style="color: white; margin-bottom: 15px;">⚙️ خيارات إدارة البيانات</h4>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                        <div>
                            <label style="color: white; display: block; margin-bottom: 5px;">مدة الاحتفاظ بالتسجيلات (أيام):</label>
                            <input type="number" id="recordingRetention" value="30" min="1" max="365" style="width: 100%; padding: 8px; border-radius: 4px; border: 1px solid #555; background: #2c2c2c; color: white;">
                        </div>

                        <div>
                            <label style="color: white; display: block; margin-bottom: 5px;">مدة الاحتفاظ بسجلات الوصول (أيام):</label>
                            <input type="number" id="logRetention" value="90" min="1" max="365" style="width: 100%; padding: 8px; border-radius: 4px; border: 1px solid #555; background: #2c2c2c; color: white;">
                        </div>

                        <div>
                            <label style="color: white; display: block; margin-bottom: 5px;">النسخ الاحتياطي التلقائي:</label>
                            <select id="autoBackup" style="width: 100%; padding: 8px; border-radius: 4px; border: 1px solid #555; background: #2c2c2c; color: white;">
                                <option value="disabled">معطل</option>
                                <option value="daily">يومي</option>
                                <option value="weekly">أسبوعي</option>
                                <option value="monthly">شهري</option>
                            </select>
                        </div>

                        <div>
                            <button onclick="saveDataSettings()" style="background: linear-gradient(135deg, #4fd1c5, #38b2ac); color: white; padding: 10px 20px; border: none; border-radius: 6px; cursor: pointer; font-weight: 600; width: 100%;">
                                💾 حفظ الإعدادات
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recordings Management Link -->
            <div class="form-container" style="background: linear-gradient(135deg, #1a365d, #2c5282); margin-top: 30px;">
                <h3 style="color: white; margin-bottom: 20px;">📹 إدارة التسجيلات</h3>
                <p style="color: #a0a0a0; margin-bottom: 20px;">إدارة شاملة لجميع تسجيلات المراقبة والأمان</p>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <button onclick="window.open('recordings_management.html', '_blank')" style="background: linear-gradient(135deg, #1a365d, #2c5282); color: white; padding: 15px; border: none; border-radius: 8px; cursor: pointer; font-weight: 600;">
                        📹 فتح إدارة التسجيلات
                    </button>
                    <button onclick="viewRecordingStats()" style="background: linear-gradient(135deg, #38a169, #2f855a); color: white; padding: 15px; border: none; border-radius: 8px; cursor: pointer; font-weight: 600;">
                        📊 إحصائيات التسجيلات
                    </button>
                    <button onclick="startBulkRecording()" style="background: linear-gradient(135deg, #e53e3e, #c53030); color: white; padding: 15px; border: none; border-radius: 8px; cursor: pointer; font-weight: 600;">
                        🔴 تسجيل جماعي
                    </button>
                </div>
            </div>
        </main>
    </div>

    <!-- Banking Footer -->
    <footer class="bank-footer">
    </footer>

    <!-- Floating Toolbar -->
    <div class="floating-toolbar">
        <div class="toolbar-header">
            لوحة التحكم الرئيسية
        </div>

        <div class="toolbar-section">
            <div class="toolbar-section-title">Control Panel</div>
            <a href="index.html" class="toolbar-item">
                <div class="toolbar-item-icon">🏠</div>
                <div class="toolbar-item-text">لوحة التحكم</div>
            </a>
            <a href="cameras.html" class="toolbar-item">
                <div class="toolbar-item-icon">🔒</div>
                <div class="toolbar-item-text">الأمان</div>
            </a>
            <a href="access.html" class="toolbar-item">
                <div class="toolbar-item-icon">🚪</div>
                <div class="toolbar-item-text">التحكم في الوصول</div>
            </a>
            <a href="reports.html" class="toolbar-item">
                <div class="toolbar-item-icon">📊</div>
                <div class="toolbar-item-text">التقارير</div>
            </a>
            <a href="settings.html" class="toolbar-item active">
                <div class="toolbar-item-icon">⚙️</div>
                <div class="toolbar-item-text">الإعدادات</div>
            </a>
            <a href="gps.html" class="toolbar-item">
                <div class="toolbar-item-icon">📍</div>
                <div class="toolbar-item-text">تتبع GPS</div>
            </a>
        </div>

        <div class="toolbar-section">
            <div class="toolbar-section-title">Abdel Fattah Mahmoud</div>
            <a href="index.html" class="toolbar-item">
                <div class="toolbar-item-icon">📹</div>
                <div class="toolbar-item-text">مراقبة الكاميرات</div>
            </a>
            <a href="cameras.html" class="toolbar-item">
                <div class="toolbar-item-icon">📷</div>
                <div class="toolbar-item-text">إدارة كاميرات IP</div>
            </a>
            <a href="access.html" class="toolbar-item">
                <div class="toolbar-item-icon">🔐</div>
                <div class="toolbar-item-text">التحكم في الوصول</div>
            </a>
            <a href="reports.html" class="toolbar-item">
                <div class="toolbar-item-icon">📈</div>
                <div class="toolbar-item-text">التقارير</div>
            </a>
            <a href="user_management.html" class="toolbar-item">
                <div class="toolbar-item-icon">👥</div>
                <div class="toolbar-item-text">إدارة المستخدمين</div>
            </a>
        </div>

        <div class="toolbar-user">
            <div class="toolbar-user-name">عبد الفتاح محمود</div>
            <div class="toolbar-user-role">مدير النظام</div>
        </div>
    </div>

    <script>
        // Wait for EG BANK system to be ready
        document.addEventListener('egbankReady', function() {
            console.log('EG BANK system ready, initializing settings page');
            initializeSettingsPage();
        });

        // Initialize settings page
        function initializeSettingsPage() {
            if (!window.egbank.isAuthenticated()) {
                console.log('User not authenticated, redirecting to login');
                window.location.href = 'login.html';
                return;
            }

            // Check if user has settings permission
            if (!window.egbank.hasPermission('settings')) {
                window.egbank.showNotification('ليس لديك صلاحية للوصول إلى الإعدادات', 'error');
                setTimeout(() => {
                    window.location.href = 'home_simple.html';
                }, 2000);
                return;
            }

            loadCurrentUser();
            loadUsers();
            setupEventListeners();

            // Log page access
            window.egbank.logAction('page_access', 'دخول إلى صفحة الإعدادات');

            console.log('Settings page initialized successfully');
        }

        // Load current user info
        function loadCurrentUser() {
            try {
                const currentUser = window.egbank.getCurrentUser();
                if (currentUser) {
                    const currentUsernameField = document.getElementById('currentUsername');
                    if (currentUsernameField) {
                        currentUsernameField.value = currentUser.username;
                    }
                }
            } catch (error) {
                console.error('Error loading current user:', error);
            }
        }

        // Load users list
        function loadUsers() {
            try {
                const users = JSON.parse(localStorage.getItem('systemUsers') || '[]');
                renderUsersTable(users);
            } catch (error) {
                console.error('Error loading users:', error);
            }
        }

        // Render users table
        function renderUsersTable(users) {
            const tbody = document.getElementById('usersTableBody');
            if (!tbody) return;

            tbody.innerHTML = '';

            users.forEach(user => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td style="padding: 15px; color: white; border-bottom: 1px solid #444;">${user.username}</td>
                    <td style="padding: 15px; color: white; border-bottom: 1px solid #444;">${user.fullName}</td>
                    <td style="padding: 15px; color: white; border-bottom: 1px solid #444;">${user.email || 'غير محدد'}</td>
                    <td style="padding: 15px; color: white; border-bottom: 1px solid #444;">${getRoleText(user.role)}</td>
                    <td style="padding: 15px; color: white; border-bottom: 1px solid #444;">${user.permissions.length} صلاحية</td>
                    <td style="padding: 15px; border-bottom: 1px solid #444;">
                        <span style="background: ${user.status === 'active' ? '#48bb78' : '#f56565'}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                            ${user.status === 'active' ? 'نشط' : 'غير نشط'}
                        </span>
                    </td>
                    <td style="padding: 15px; text-align: center; border-bottom: 1px solid #444;">
                        <button onclick="editUser('${user.id}')" style="background: #f6e05e; color: black; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; margin-left: 5px;">
                            ✏️ تعديل
                        </button>
                        <button onclick="toggleUserStatus('${user.id}')" style="background: ${user.status === 'active' ? '#f56565' : '#48bb78'}; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer;">
                            ${user.status === 'active' ? '🚫 إيقاف' : '✅ تفعيل'}
                        </button>
                    </td>
                `;
            });
        }

        // Get role text in Arabic
        function getRoleText(role) {
            const roleMap = {
                admin: 'مدير النظام',
                operator: 'مشغل',
                viewer: 'مشاهد',
                security: 'أمان'
            };
            return roleMap[role] || role;
        }

        // Setup event listeners
        function setupEventListeners() {
            // Add user button
            const addUserBtn = document.getElementById('addUserBtn');
            if (addUserBtn) {
                addUserBtn.addEventListener('click', addNewUser);
            }

            // Update current user button
            const updateBtn = document.querySelector('button[onclick="updateCurrentUser()"]');
            if (updateBtn) {
                updateBtn.onclick = updateCurrentUser;
            }
        }

        // Update current user
        function updateCurrentUser() {
            try {
                const newUsername = document.getElementById('newUsername').value.trim();
                const currentPassword = document.getElementById('currentPassword').value;
                const newPassword = document.getElementById('newPassword').value;
                const confirmPassword = document.getElementById('confirmPassword').value;

                const currentUser = window.egbank.getCurrentUser();
                if (!currentUser) {
                    window.egbank.showNotification('المستخدم الحالي غير موجود', 'error');
                    return;
                }

                // Validate current password if provided
                if (currentPassword && currentUser.password !== currentPassword) {
                    window.egbank.showNotification('كلمة المرور الحالية غير صحيحة', 'error');
                    return;
                }

                // Validate new password confirmation
                if (newPassword && newPassword !== confirmPassword) {
                    window.egbank.showNotification('كلمة المرور الجديدة غير متطابقة', 'error');
                    return;
                }

                // Update user data
                const users = JSON.parse(localStorage.getItem('systemUsers') || '[]');
                const userIndex = users.findIndex(u => u.id === currentUser.id);

                if (userIndex !== -1) {
                    if (newUsername && newUsername !== currentUser.username) {
                        // Check if username already exists
                        if (users.find(u => u.username === newUsername && u.id !== currentUser.id)) {
                            window.egbank.showNotification('اسم المستخدم موجود بالفعل', 'error');
                            return;
                        }
                        users[userIndex].username = newUsername;
                        localStorage.setItem('username', newUsername);
                    }

                    if (newPassword) {
                        users[userIndex].password = newPassword;
                    }

                    localStorage.setItem('systemUsers', JSON.stringify(users));
                    loadCurrentUser();
                    resetCurrentUserForm();
                    window.egbank.showNotification('تم تحديث بيانات المستخدم بنجاح', 'success');
                }
            } catch (error) {
                console.error('Error updating current user:', error);
                window.egbank.showNotification('حدث خطأ أثناء تحديث البيانات', 'error');
            }
        }

        // Reset current user form
        function resetCurrentUserForm() {
            const fields = ['newUsername', 'currentPassword', 'newPassword', 'confirmPassword'];
            fields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) field.value = '';
            });
        }

        // Add new user
        function addNewUser() {
            try {
                const username = document.getElementById('username').value.trim();
                const fullName = document.getElementById('fullName').value.trim();
                const email = document.getElementById('email').value.trim();
                const password = document.getElementById('password').value;
                const role = document.getElementById('userRole').value;
                const permissions = getSelectedPermissions();

                if (!username || !fullName || !password || !role) {
                    window.egbank.showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
                    return;
                }

                const users = JSON.parse(localStorage.getItem('systemUsers') || '[]');

                if (users.find(u => u.username === username)) {
                    window.egbank.showNotification('اسم المستخدم موجود بالفعل', 'error');
                    return;
                }

                const newUser = {
                    id: 'user_' + Date.now(),
                    username,
                    fullName,
                    email,
                    password,
                    role,
                    permissions,
                    status: 'active',
                    createdAt: new Date().toISOString(),
                    lastLogin: null
                };

                users.push(newUser);
                localStorage.setItem('systemUsers', JSON.stringify(users));

                loadUsers();
                resetUserForm();
                window.egbank.showNotification('تم إضافة المستخدم بنجاح', 'success');

                // Log action
                window.egbank.logAction('user_added', `إضافة مستخدم جديد: ${username}`);
            } catch (error) {
                console.error('Error adding user:', error);
                window.egbank.showNotification('حدث خطأ أثناء إضافة المستخدم', 'error');
            }
        }

        // Get selected permissions
        function getSelectedPermissions() {
            const permissions = [];
            const checkboxes = document.querySelectorAll('input[name="permissions"]:checked');
            checkboxes.forEach(cb => permissions.push(cb.value));
            return permissions;
        }

        // Reset user form
        function resetUserForm() {
            const form = document.getElementById('userForm');
            if (form) form.reset();
        }

        // Edit user
        function editUser(userId) {
            try {
                const users = JSON.parse(localStorage.getItem('systemUsers') || '[]');
                const user = users.find(u => u.id === userId);

                if (user) {
                    // Fill form with user data
                    document.getElementById('username').value = user.username;
                    document.getElementById('fullName').value = user.fullName;
                    document.getElementById('email').value = user.email || '';
                    document.getElementById('userRole').value = user.role;

                    // Set permissions
                    document.querySelectorAll('input[name="permissions"]').forEach(checkbox => {
                        checkbox.checked = user.permissions.includes(checkbox.value);
                    });

                    window.egbank.showNotification(`تم تحميل بيانات المستخدم: ${user.fullName}`, 'info');
                }
            } catch (error) {
                console.error('Error editing user:', error);
                window.egbank.showNotification('حدث خطأ أثناء تحميل بيانات المستخدم', 'error');
            }
        }

        // Toggle user status
        function toggleUserStatus(userId) {
            try {
                const users = JSON.parse(localStorage.getItem('systemUsers') || '[]');
                const user = users.find(u => u.id === userId);

                if (user) {
                    if (user.username === 'admin') {
                        window.egbank.showNotification('لا يمكن تغيير حالة المدير الرئيسي', 'error');
                        return;
                    }

                    user.status = user.status === 'active' ? 'inactive' : 'active';
                    localStorage.setItem('systemUsers', JSON.stringify(users));

                    loadUsers();

                    const statusText = user.status === 'active' ? 'تم تفعيل' : 'تم إيقاف';
                    window.egbank.showNotification(`${statusText} المستخدم ${user.fullName}`, 'success');

                    // Log action
                    window.egbank.logAction('user_status_changed', `تغيير حالة المستخدم: ${user.username} إلى ${user.status}`);
                }
            } catch (error) {
                console.error('Error toggling user status:', error);
                window.egbank.showNotification('حدث خطأ أثناء تغيير حالة المستخدم', 'error');
            }
        }

        // Initialize page when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Settings page DOM loaded...');

            // If EG BANK system is already ready, initialize immediately
            if (window.EGBankApp && window.EGBankApp.isInitialized) {
                initializeSettingsPage();
            }
        });
    </script>
</body>
</html>