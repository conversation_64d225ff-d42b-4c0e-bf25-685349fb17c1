// TODO: Add database connection and CRUD operations here
document.addEventListener('DOMContentLoaded', () => {
    const eventTypeFilter = document.getElementById('eventType');
    const startDateFilter = document.getElementById('startDate');
    const endDateFilter = document.getElementById('endDate');
    const applyFiltersBtn = document.getElementById('applyFilters');
    const eventsTable = document.getElementById('eventsTable').getElementsByTagName('tbody')[0];
    
    // Sample events data (in real app, this would come from localStorage/API)
    let events = [
        {
            timestamp: '2025-06-25 08:30',
            type: 'System',
            description: 'Application started',
            user: 'System',
            details: 'Initialization complete'
        },
        {
            timestamp: '2025-06-25 08:45',
            type: 'Camera',
            description: 'Recording started',
            user: 'Admin',
            details: 'Branch camera recording'
        }
    ];
    
    // Load events
    renderEvents(events);
    
    // Apply filters
    applyFiltersBtn.addEventListener('click', () => {
        const filteredEvents = events.filter(event => {
            // Filter by event type
            if (eventTypeFilter.value !== 'all' && event.type.toLowerCase() !== eventTypeFilter.value) {
                return false;
            }
            
            // Filter by date range
            if (startDateFilter.value) {
                const eventDate = new Date(event.timestamp.split(' ')[0]);
                const startDate = new Date(startDateFilter.value);
                if (eventDate < startDate) return false;
            }
            
            if (endDateFilter.value) {
                const eventDate = new Date(event.timestamp.split(' ')[0]);
                const endDate = new Date(endDateFilter.value);
                if (eventDate > endDate) return false;
            }
            
            return true;
        });
        
        renderEvents(filteredEvents);
    });
    
    function renderEvents(events) {
        eventsTable.innerHTML = '';
        events.forEach(event => {
            const row = eventsTable.insertRow();
            row.innerHTML = `
                <td>${event.timestamp}</td>
                <td>${event.type}</td>
                <td>${event.description}</td>
                <td>${event.user}</td>
                <td>${event.details}</td>
            `;
        });
    }
});