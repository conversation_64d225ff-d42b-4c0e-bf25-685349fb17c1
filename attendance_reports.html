<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - تقارير الحضور والانصراف</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(26, 54, 93, 0.9);
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(45deg, #1a365d, #2c5282);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .logo-text {
            font-size: 28px;
            font-weight: 900;
            color: white;
        }
        
        .nav-buttons {
            display: flex;
            gap: 15px;
        }
        
        .nav-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
        }
        
        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .page-title {
            text-align: center;
            font-size: 36px;
            font-weight: 900;
            margin-bottom: 40px;
            color: #3182ce;
            text-shadow: 0 0 20px rgba(49, 130, 206, 0.5);
        }
        
        .filters-panel {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(20px);
        }
        
        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .filter-label {
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
            font-size: 14px;
        }
        
        .filter-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 12px;
            color: white;
            font-size: 14px;
        }
        
        .filter-input:focus {
            outline: none;
            border-color: #3182ce;
            box-shadow: 0 0 0 2px rgba(49, 130, 206, 0.2);
        }
        
        .filter-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }
        
        .filter-btn {
            background: linear-gradient(135deg, #1a365d, #2c5282);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .filter-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(26, 54, 93, 0.4);
        }
        
        .export-btn {
            background: linear-gradient(135deg, #48bb78, #38a169);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            backdrop-filter: blur(20px);
        }
        
        .stat-icon {
            font-size: 32px;
            margin-bottom: 10px;
            display: block;
        }
        
        .stat-value {
            font-size: 28px;
            font-weight: 900;
            color: #3182ce;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .reports-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        
        .report-panel {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(20px);
        }
        
        .panel-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 20px;
            color: white;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .attendance-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .attendance-table th,
        .attendance-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .attendance-table th {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-weight: 600;
        }
        
        .attendance-table td {
            color: rgba(255, 255, 255, 0.9);
        }
        
        .status-present {
            color: #48bb78;
            font-weight: 600;
        }
        
        .status-absent {
            color: #f56565;
            font-weight: 600;
        }
        
        .status-late {
            color: #ed8936;
            font-weight: 600;
        }
        
        .chart-container {
            height: 300px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.5);
            font-size: 18px;
        }
        
        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .summary-item:last-child {
            border-bottom: none;
        }
        
        .summary-label {
            color: rgba(255, 255, 255, 0.8);
        }
        
        .summary-value {
            color: #3182ce;
            font-weight: 700;
        }
        
        @media (max-width: 768px) {
            .reports-grid {
                grid-template-columns: 1fr;
            }
            
            .filters-grid {
                grid-template-columns: 1fr;
            }
            
            .filter-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">🏦</div>
                <div class="logo-text">EG BANK</div>
            </div>
            
            <div class="nav-buttons">
                <a href="attendance.html" class="nav-btn">⏰ الحضور والانصراف</a>
                <a href="home_simple.html" class="nav-btn">🏠 الرئيسية</a>
            </div>
        </div>
    </header>
    
    <div class="container">
        <h1 class="page-title">📊 تقارير الحضور والانصراف</h1>
        
        <!-- Filters Panel -->
        <div class="filters-panel">
            <div class="filters-grid">
                <div class="filter-group">
                    <label class="filter-label">من تاريخ:</label>
                    <input type="date" class="filter-input" id="startDate">
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">إلى تاريخ:</label>
                    <input type="date" class="filter-input" id="endDate">
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">الموظف:</label>
                    <select class="filter-input" id="employeeFilter">
                        <option value="">جميع الموظفين</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">نوع التقرير:</label>
                    <select class="filter-input" id="reportType">
                        <option value="daily">تقرير يومي</option>
                        <option value="weekly">تقرير أسبوعي</option>
                        <option value="monthly">تقرير شهري</option>
                    </select>
                </div>
            </div>
            
            <div class="filter-buttons">
                <button class="filter-btn" onclick="generateReport()">🔍 إنشاء التقرير</button>
                <button class="filter-btn export-btn" onclick="exportReport()">📤 تصدير التقرير</button>
            </div>
        </div>
        
        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <span class="stat-icon">📅</span>
                <div class="stat-value" id="totalDays">0</div>
                <div class="stat-label">إجمالي الأيام</div>
            </div>
            
            <div class="stat-card">
                <span class="stat-icon">✅</span>
                <div class="stat-value" id="presentDays">0</div>
                <div class="stat-label">أيام الحضور</div>
            </div>
            
            <div class="stat-card">
                <span class="stat-icon">⏰</span>
                <div class="stat-value" id="lateDays">0</div>
                <div class="stat-label">أيام التأخير</div>
            </div>
            
            <div class="stat-card">
                <span class="stat-icon">🕐</span>
                <div class="stat-value" id="avgHours">0</div>
                <div class="stat-label">متوسط ساعات العمل</div>
            </div>
        </div>
        
        <!-- Reports Grid -->
        <div class="reports-grid">
            <!-- Attendance Table -->
            <div class="report-panel">
                <h2 class="panel-title">
                    📋 سجل الحضور والانصراف
                </h2>
                
                <div style="overflow-x: auto;">
                    <table class="attendance-table">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>الحضور</th>
                                <th>الانصراف</th>
                                <th>ساعات العمل</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody id="attendanceTableBody">
                            <!-- Data will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- Summary Panel -->
            <div class="report-panel">
                <h2 class="panel-title">
                    📈 ملخص الأداء
                </h2>
                
                <div id="performanceSummary">
                    <div class="summary-item">
                        <span class="summary-label">معدل الحضور:</span>
                        <span class="summary-value" id="attendanceRate">0%</span>
                    </div>
                    
                    <div class="summary-item">
                        <span class="summary-label">معدل التأخير:</span>
                        <span class="summary-value" id="lateRate">0%</span>
                    </div>
                    
                    <div class="summary-item">
                        <span class="summary-label">إجمالي ساعات العمل:</span>
                        <span class="summary-value" id="totalHours">0 ساعة</span>
                    </div>
                    
                    <div class="summary-item">
                        <span class="summary-label">متوسط وقت الحضور:</span>
                        <span class="summary-value" id="avgCheckinTime">--:--</span>
                    </div>
                    
                    <div class="summary-item">
                        <span class="summary-label">متوسط وقت الانصراف:</span>
                        <span class="summary-value" id="avgCheckoutTime">--:--</span>
                    </div>
                </div>
                
                <div class="chart-container" style="margin-top: 20px;">
                    📊 الرسم البياني سيتم إضافته قريباً
                </div>
            </div>
        </div>
    </div>
    
    <script src="app_core.js"></script>
    <script src="attendance_system.js"></script>
    <script>
        // Initialize reports page
        document.addEventListener('DOMContentLoaded', function() {
            initializeReportsPage();
        });
        
        function initializeReportsPage() {
            // Set default dates
            const today = new Date();
            const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            
            document.getElementById('startDate').value = firstDayOfMonth.toISOString().split('T')[0];
            document.getElementById('endDate').value = today.toISOString().split('T')[0];
            
            // Load employees
            loadEmployees();
            
            // Generate initial report
            generateReport();
        }
        
        function loadEmployees() {
            const employees = JSON.parse(localStorage.getItem('systemUsers') || '[]');
            const select = document.getElementById('employeeFilter');
            
            employees.forEach(emp => {
                if (emp.status === 'active') {
                    const option = document.createElement('option');
                    option.value = emp.username;
                    option.textContent = emp.fullName || emp.username;
                    select.appendChild(option);
                }
            });
        }
        
        function generateReport() {
            const startDate = new Date(document.getElementById('startDate').value);
            const endDate = new Date(document.getElementById('endDate').value);
            const employeeFilter = document.getElementById('employeeFilter').value;
            const reportType = document.getElementById('reportType').value;
            
            if (!startDate || !endDate) {
                alert('يرجى تحديد تاريخ البداية والنهاية');
                return;
            }
            
            const attendanceRecords = JSON.parse(localStorage.getItem('attendanceRecords') || '[]');
            
            // Filter records
            let filteredRecords = attendanceRecords.filter(record => {
                const recordDate = new Date(record.date);
                const matchesDate = recordDate >= startDate && recordDate <= endDate;
                const matchesEmployee = !employeeFilter || record.userId === employeeFilter;
                return matchesDate && matchesEmployee;
            });
            
            // Update statistics
            updateStatistics(filteredRecords);
            
            // Update table
            updateAttendanceTable(filteredRecords);
            
            // Update summary
            updatePerformanceSummary(filteredRecords);
        }
        
        function updateStatistics(records) {
            const totalDays = records.length;
            const presentDays = records.filter(r => r.checkinTime).length;
            const lateDays = records.filter(r => r.lateMinutes > 0).length;
            const totalHours = records.reduce((sum, r) => sum + (r.workingHours || 0), 0);
            const avgHours = totalDays > 0 ? (totalHours / totalDays).toFixed(1) : 0;
            
            document.getElementById('totalDays').textContent = totalDays;
            document.getElementById('presentDays').textContent = presentDays;
            document.getElementById('lateDays').textContent = lateDays;
            document.getElementById('avgHours').textContent = avgHours;
        }
        
        function updateAttendanceTable(records) {
            const tbody = document.getElementById('attendanceTableBody');
            tbody.innerHTML = '';
            
            if (records.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5" style="text-align: center; color: rgba(255,255,255,0.5);">لا توجد بيانات للفترة المحددة</td></tr>';
                return;
            }
            
            records.sort((a, b) => new Date(b.date) - new Date(a.date));
            
            records.forEach(record => {
                const row = tbody.insertRow();
                
                const date = new Date(record.date).toLocaleDateString('ar-EG');
                const checkinTime = record.checkinTime ? formatTime(record.checkinTime) : '--:--';
                const checkoutTime = record.checkoutTime ? formatTime(record.checkoutTime) : '--:--';
                const workingHours = record.workingHours ? record.workingHours.toFixed(1) + ' ساعة' : '--';
                
                let status = 'غائب';
                let statusClass = 'status-absent';
                
                if (record.checkinTime) {
                    if (record.lateMinutes > 0) {
                        status = `متأخر (${record.lateMinutes} دقيقة)`;
                        statusClass = 'status-late';
                    } else {
                        status = 'حاضر';
                        statusClass = 'status-present';
                    }
                }
                
                row.innerHTML = `
                    <td>${date}</td>
                    <td>${checkinTime}</td>
                    <td>${checkoutTime}</td>
                    <td>${workingHours}</td>
                    <td class="${statusClass}">${status}</td>
                `;
            });
        }
        
        function updatePerformanceSummary(records) {
            const totalDays = records.length;
            const presentDays = records.filter(r => r.checkinTime).length;
            const lateDays = records.filter(r => r.lateMinutes > 0).length;
            const totalHours = records.reduce((sum, r) => sum + (r.workingHours || 0), 0);
            
            const attendanceRate = totalDays > 0 ? ((presentDays / totalDays) * 100).toFixed(1) : 0;
            const lateRate = presentDays > 0 ? ((lateDays / presentDays) * 100).toFixed(1) : 0;
            
            document.getElementById('attendanceRate').textContent = attendanceRate + '%';
            document.getElementById('lateRate').textContent = lateRate + '%';
            document.getElementById('totalHours').textContent = totalHours.toFixed(1) + ' ساعة';
            
            // Calculate average times
            const checkinTimes = records.filter(r => r.checkinTime).map(r => new Date(r.checkinTime));
            const checkoutTimes = records.filter(r => r.checkoutTime).map(r => new Date(r.checkoutTime));
            
            if (checkinTimes.length > 0) {
                const avgCheckinMs = checkinTimes.reduce((sum, time) => sum + time.getTime(), 0) / checkinTimes.length;
                const avgCheckinTime = new Date(avgCheckinMs);
                document.getElementById('avgCheckinTime').textContent = formatTime(avgCheckinTime.toISOString());
            }
            
            if (checkoutTimes.length > 0) {
                const avgCheckoutMs = checkoutTimes.reduce((sum, time) => sum + time.getTime(), 0) / checkoutTimes.length;
                const avgCheckoutTime = new Date(avgCheckoutMs);
                document.getElementById('avgCheckoutTime').textContent = formatTime(avgCheckoutTime.toISOString());
            }
        }
        
        function formatTime(timeStr) {
            const time = new Date(timeStr);
            return time.toLocaleTimeString('ar-EG', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }
        
        function exportReport() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const employeeFilter = document.getElementById('employeeFilter').value;
            
            const attendanceRecords = JSON.parse(localStorage.getItem('attendanceRecords') || '[]');
            
            // Filter records
            let filteredRecords = attendanceRecords.filter(record => {
                const recordDate = new Date(record.date);
                const matchesDate = recordDate >= new Date(startDate) && recordDate <= new Date(endDate);
                const matchesEmployee = !employeeFilter || record.userId === employeeFilter;
                return matchesDate && matchesEmployee;
            });
            
            // Create CSV content
            const csvHeaders = ['التاريخ', 'الموظف', 'وقت الحضور', 'وقت الانصراف', 'ساعات العمل', 'التأخير (دقيقة)', 'الحالة'];
            const csvRows = filteredRecords.map(record => [
                new Date(record.date).toLocaleDateString('ar-EG'),
                record.userName,
                record.checkinTime ? formatTime(record.checkinTime) : '--:--',
                record.checkoutTime ? formatTime(record.checkoutTime) : '--:--',
                record.workingHours ? record.workingHours.toFixed(1) : '0',
                record.lateMinutes || '0',
                record.checkinTime ? (record.lateMinutes > 0 ? 'متأخر' : 'حاضر') : 'غائب'
            ]);
            
            const csvContent = [csvHeaders, ...csvRows]
                .map(row => row.map(field => `"${field}"`).join(','))
                .join('\n');
            
            // Download CSV
            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `attendance_report_${startDate}_${endDate}.csv`;
            link.click();
            
            if (window.egbank) {
                window.egbank.showNotification('تم تصدير التقرير بنجاح', 'success');
            }
        }
    </script>
</body>
</html>
