<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - Mobile ATM GPS</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="style.css">
    <style>
        #loading-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #2c2c2c;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .loader {
            border: 16px solid #3a3a3a;
            border-top: 16px solid #1a365d;
            border-radius: 50%;
            width: 120px;
            height: 120px;
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Search Section */
        .search-container {
            background: #3a3a3a;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .search-bar {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .search-input {
            flex: 1;
            min-width: 250px;
            padding: 12px;
            border: 1px solid #555555;
            border-radius: 4px;
            background-color: #2c2c2c;
            color: white;
            font-size: 16px;
        }

        .search-btn {
            background-color: #1a365d;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
        }

        .search-btn:hover {
            background-color: #2c5282;
        }

        .filter-options {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid #555555;
            border-radius: 4px;
            background-color: #2c2c2c;
            color: white;
        }

        /* Vehicle Cards */
        .vehicle-card {
            background: #4a4a4a;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            border: 2px solid #555555;
            transition: all 0.3s ease;
        }

        .vehicle-card:hover {
            border-color: #1a365d;
            transform: translateY(-2px);
        }

        .vehicle-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .vehicle-name {
            color: white;
            font-size: 18px;
            font-weight: bold;
        }

        .vehicle-status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background-color: #48bb78;
            color: white;
        }

        .status-inactive {
            background-color: #f56565;
            color: white;
        }

        .vehicle-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .info-item {
            color: #a0a0a0;
            font-size: 14px;
        }

        .info-label {
            color: white;
            font-weight: 500;
        }

        .vehicle-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .action-btn {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
        }

        .btn-edit {
            background-color: #1a365d;
            color: white;
        }

        .btn-edit:hover {
            background-color: #2c5282;
        }

        .btn-delete {
            background-color: #e53e3e;
            color: white;
        }

        .btn-delete:hover {
            background-color: #c53030;
        }

        .btn-view-history {
            background-color: #38a169;
            color: white;
        }

        .btn-view-history:hover {
            background-color: #2f855a;
        }

        /* Database Modal */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
        }

        .modal-content {
            background-color: #3a3a3a;
            margin: 5% auto;
            padding: 30px;
            border-radius: 8px;
            width: 90%;
            max-width: 1200px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: white;
        }

        /* History Table */
        .history-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .history-table th,
        .history-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #555555;
            color: white;
        }

        .history-table th {
            background-color: #1a365d;
            font-weight: 500;
        }

        .history-table tbody tr:nth-child(even) {
            background-color: #4a4a4a;
        }

        .history-table tbody tr:hover {
            background-color: #555555;
        }

        /* Control Panel */
        .control-panel {
            background: linear-gradient(135deg, #1a365d 0%, #2c5282 100%);
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(26, 54, 93, 0.3);
        }

        .panel-section h3 {
            color: white;
            margin-bottom: 20px;
            font-size: 20px;
            text-align: center;
        }

        .control-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .control-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            transform: translateY(-2px);
        }

        .control-btn i {
            font-size: 18px;
        }

        /* Statistics Dashboard */
        .stats-dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .stat-card {
            background: #3a3a3a;
            border-radius: 12px;
            padding: 20px;
            border: 2px solid #555555;
            display: flex;
            align-items: center;
            gap: 15px;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            border-color: #1a365d;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .stat-icon {
            font-size: 32px;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: linear-gradient(135deg, #1a365d, #2c5282);
        }

        .stat-info {
            flex: 1;
        }

        .stat-number {
            font-size: 28px;
            font-weight: bold;
            color: white;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #a0a0a0;
            font-size: 14px;
        }

        /* Map Section */
        .map-section {
            background: #3a3a3a;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            border: 2px solid #555555;
        }

        .map-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .map-btn {
            background: #1a365d;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .map-btn:hover {
            background: #2c5282;
        }

        #mapStyle {
            background: #2c2c2c;
            color: white;
            border: 1px solid #555555;
            padding: 8px 12px;
            border-radius: 6px;
        }

        /* Vehicles Section */
        .vehicles-section {
            background: #3a3a3a;
            border-radius: 12px;
            padding: 20px;
            border: 2px solid #555555;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .section-header h3 {
            color: white;
            margin: 0;
            font-size: 20px;
        }

        .search-controls {
            display: flex;
            gap: 10px;
        }

        .search-controls input,
        .search-controls select {
            background: #2c2c2c;
            color: white;
            border: 1px solid #555555;
            padding: 8px 12px;
            border-radius: 6px;
        }

        .vehicles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 20px;
        }

        /* Vehicle Card */
        .vehicle-card {
            background: #4a4a4a;
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #555555;
            transition: all 0.3s ease;
            position: relative;
        }

        .vehicle-card:hover {
            border-color: #1a365d;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .vehicle-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .vehicle-name {
            color: white;
            font-size: 18px;
            font-weight: bold;
        }

        .vehicle-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-online {
            background: #48bb78;
            color: white;
        }

        .status-offline {
            background: #f56565;
            color: white;
        }

        .status-moving {
            background: #4299e1;
            color: white;
        }

        .status-stopped {
            background: #ed8936;
            color: white;
        }

        .vehicle-info {
            margin-bottom: 15px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            color: #a0a0a0;
            font-size: 14px;
        }

        .info-label {
            color: white;
            font-weight: 500;
        }

        .vehicle-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            flex: 1;
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-track {
            background: #1a365d;
            color: white;
        }

        .btn-track:hover {
            background: #2c5282;
        }

        .btn-details {
            background: #38a169;
            color: white;
        }

        .btn-details:hover {
            background: #2f855a;
        }

        .btn-edit {
            background: #ed8936;
            color: white;
        }

        .btn-edit:hover {
            background: #dd6b20;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: #3a3a3a;
            margin: 5% auto;
            padding: 30px;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            border: 2px solid #1a365d;
        }

        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s;
        }

        .close:hover {
            color: white;
        }

        /* Form Styles */
        .vehicle-form {
            margin-top: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            color: white;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            background: #2c2c2c;
            color: white;
            border: 1px solid #555555;
            padding: 10px;
            border-radius: 6px;
            font-size: 14px;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #1a365d;
            box-shadow: 0 0 0 2px rgba(26, 54, 93, 0.3);
        }

        .form-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .btn-primary {
            background: #1a365d;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: #2c5282;
        }

        .btn-secondary {
            background: #555555;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: #666666;
        }

        /* Alerts Panel */
        .alerts-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 300px;
            background: #3a3a3a;
            border-radius: 8px;
            border: 2px solid #f56565;
            z-index: 999;
            display: none;
        }

        .alerts-header {
            background: #f56565;
            color: white;
            padding: 15px;
            border-radius: 6px 6px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .alerts-header h3 {
            margin: 0;
            font-size: 16px;
        }

        .alerts-header button {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        #alertsList {
            max-height: 300px;
            overflow-y: auto;
            padding: 10px;
        }

        .alert-item {
            background: #4a4a4a;
            padding: 10px;
            margin-bottom: 8px;
            border-radius: 6px;
            border-left: 4px solid #f56565;
        }

        .alert-item h4 {
            color: white;
            margin: 0 0 5px 0;
            font-size: 14px;
        }

        .alert-item p {
            color: #a0a0a0;
            margin: 0;
            font-size: 12px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .control-buttons {
                grid-template-columns: 1fr;
            }

            .stats-dashboard {
                grid-template-columns: repeat(2, 1fr);
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .section-header {
                flex-direction: column;
                align-items: stretch;
            }

            .search-controls {
                flex-direction: column;
            }

            .vehicles-grid {
                grid-template-columns: 1fr;
            }

            .alerts-panel {
                width: 90%;
                right: 5%;
            }
        }
    </style>
</head>
<body>
    <div id="loading-container">
        <div class="loader"></div>
    </div>
    <script>
        window.addEventListener('load', function() {
            document.getElementById('loading-container').style.display = 'none';
        });
    </script>

    <!-- Banking Header -->
    <header class="bank-header">
        <div class="bank-logo">EG BANK</div>
        <nav class="bank-nav">
            <ul>
                <li><a href="index.html" style="color: white;" data-en="Dashboard" data-ar="لوحة التحكم">لوحة التحكم</a></li>
                <li><a href="cameras.html" style="color: white;" data-en="Security" data-ar="الأمان">الأمان</a></li>
                <li><a href="access.html" style="color: white;" data-en="Access Control" data-ar="التحكم في الوصول">التحكم في الوصول</a></li>
                <li><a href="reports.html" style="color: white;" data-en="Reports" data-ar="التقارير">التقارير</a></li>
                <li><a href="settings.html" style="color: white;" data-en="Settings" data-ar="الإعدادات">الإعدادات</a></li>
                <li><a href="gps.html" class="active" style="color: white;" data-en="Mobile ATM GPS" data-ar="تتبع أجهزة الصراف">تتبع أجهزة الصراف</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main Content Area -->
    <div class="bank-container">
        <!-- Sidebar -->
        <aside class="bank-sidebar">
            <h3 style="color: white;">عبد الفتاح محمود</h3>
            <ul>
                <li><a href="index.html" style="color: white;" data-en="Camera Surveillance" data-ar="مراقبة الكاميرات">مراقبة الكاميرات</a></li>
                <li><a href="cameras.html" style="color: white;" data-en="IP Camera Management" data-ar="إدارة كاميرات IP">إدارة كاميرات IP</a></li>
                <li><a href="access.html" style="color: white;" data-en="Access Control" data-ar="التحكم في الوصول">التحكم في الوصول</a></li>
                <li><a href="reports.html" style="color: white;" data-en="Reports" data-ar="التقارير">التقارير</a></li>
                <li class="active" style="color: white;" data-en="Mobile ATM GPS" data-ar="تتبع أجهزة الصراف">تتبع أجهزة الصراف</li>
            </ul>
        </aside>

        <!-- Vehicle Tracking System -->
        <main class="bank-main">
            <h1 style="color: white; text-align: center; margin-bottom: 30px;">🚗 نظام تتبع المركبات المتقدم</h1>

            <!-- Control Panel -->
            <div class="control-panel">
                <div class="panel-section">
                    <h3>لوحة التحكم الرئيسية</h3>
                    <div class="control-buttons">
                        <button class="control-btn" onclick="addNewVehicle()">
                            <i>🚗</i> إضافة مركبة جديدة
                        </button>
                        <button class="control-btn" onclick="showLiveTracking()">
                            <i>📍</i> التتبع المباشر
                        </button>
                        <button class="control-btn" onclick="generateReports()">
                            <i>📊</i> تقارير مفصلة
                        </button>
                        <button class="control-btn" onclick="manageGeofences()">
                            <i>🗺️</i> إدارة المناطق
                        </button>
                    </div>
                </div>
            </div>

            <!-- Statistics Dashboard -->
            <div class="stats-dashboard">
                <div class="stat-card active-vehicles">
                    <div class="stat-icon">🟢</div>
                    <div class="stat-info">
                        <div class="stat-number" id="activeCount">0</div>
                        <div class="stat-label">مركبات نشطة</div>
                    </div>
                </div>
                <div class="stat-card total-vehicles">
                    <div class="stat-icon">🚗</div>
                    <div class="stat-info">
                        <div class="stat-number" id="totalCount">0</div>
                        <div class="stat-label">إجمالي المركبات</div>
                    </div>
                </div>
                <div class="stat-card distance-today">
                    <div class="stat-icon">📏</div>
                    <div class="stat-info">
                        <div class="stat-number" id="distanceToday">0</div>
                        <div class="stat-label">كم اليوم</div>
                    </div>
                </div>
                <div class="stat-card alerts-count">
                    <div class="stat-icon">⚠️</div>
                    <div class="stat-info">
                        <div class="stat-number" id="alertsCount">0</div>
                        <div class="stat-label">تنبيهات</div>
                    </div>
                </div>
            </div>

            <!-- Map Container -->
            <div class="map-section">
                <div class="map-controls">
                    <button class="map-btn" onclick="centerMap()">📍 توسيط الخريطة</button>
                    <button class="map-btn" onclick="toggleSatellite()">🛰️ عرض القمر الصناعي</button>
                    <button class="map-btn" onclick="toggleTraffic()">🚦 حركة المرور</button>
                    <select id="mapStyle" onchange="changeMapStyle()">
                        <option value="streets">شوارع</option>
                        <option value="satellite">قمر صناعي</option>
                        <option value="hybrid">مختلط</option>
                    </select>
                </div>
                <div id="map" style="height: 500px; border-radius: 8px; border: 2px solid #1a365d;"></div>
            </div>

            <!-- Vehicle List -->
            <div class="vehicles-section">
                <div class="section-header">
                    <h3>قائمة المركبات</h3>
                    <div class="search-controls">
                        <input type="text" id="searchVehicle" placeholder="البحث عن مركبة..." onkeyup="searchVehicles()">
                        <select id="filterStatus" onchange="filterVehicles()">
                            <option value="all">جميع الحالات</option>
                            <option value="online">متصل</option>
                            <option value="offline">غير متصل</option>
                            <option value="moving">متحرك</option>
                            <option value="stopped">متوقف</option>
                        </select>
                    </div>
                </div>
                <div id="vehiclesList" class="vehicles-grid">
                    <!-- Vehicles will be populated here -->
                </div>
            </div>
        </main>
    </div>

    <!-- Add Vehicle Modal -->
    <div id="addVehicleModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeAddVehicleModal()">&times;</span>
            <h2 style="color: white; margin-bottom: 20px;">إضافة مركبة جديدة</h2>

            <form id="addVehicleForm" class="vehicle-form">
                <div class="form-row">
                    <div class="form-group">
                        <label>اسم المركبة</label>
                        <input type="text" id="vehicleName" required>
                    </div>
                    <div class="form-group">
                        <label>رقم اللوحة</label>
                        <input type="text" id="plateNumber" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>اسم السائق</label>
                        <input type="text" id="driverName" required>
                    </div>
                    <div class="form-group">
                        <label>رقم الهاتف</label>
                        <input type="tel" id="driverPhone" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>نوع المركبة</label>
                        <select id="vehicleType" required>
                            <option value="">اختر النوع</option>
                            <option value="car">سيارة</option>
                            <option value="truck">شاحنة</option>
                            <option value="motorcycle">دراجة نارية</option>
                            <option value="bus">حافلة</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>رقم الجهاز (IMEI)</label>
                        <input type="text" id="deviceIMEI" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>الشركة/القسم</label>
                        <input type="text" id="company">
                    </div>
                    <div class="form-group">
                        <label>ملاحظات</label>
                        <textarea id="notes" rows="2"></textarea>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn-primary">إضافة المركبة</button>
                    <button type="button" class="btn-secondary" onclick="closeAddVehicleModal()">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Vehicle Details Modal -->
    <div id="vehicleDetailsModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeVehicleDetailsModal()">&times;</span>
            <div id="vehicleDetailsContent">
                <!-- Vehicle details will be populated here -->
            </div>
        </div>
    </div>

    <!-- Alerts Panel -->
    <div id="alertsPanel" class="alerts-panel">
        <div class="alerts-header">
            <h3>التنبيهات النشطة</h3>
            <button onclick="clearAllAlerts()">مسح الكل</button>
        </div>
        <div id="alertsList">
            <!-- Alerts will be populated here -->
        </div>
    </div>

    <!-- Banking Footer -->
    <footer class="bank-footer">
    </footer>

    <!-- Floating Toolbar -->
    <div class="floating-toolbar">
        <div class="toolbar-header">
            لوحة التحكم الرئيسية
        </div>

        <div class="toolbar-section">
            <div class="toolbar-section-title">Control Panel</div>
            <a href="index.html" class="toolbar-item">
                <div class="toolbar-item-icon">🏠</div>
                <div class="toolbar-item-text">لوحة التحكم</div>
            </a>
            <a href="cameras.html" class="toolbar-item">
                <div class="toolbar-item-icon">🔒</div>
                <div class="toolbar-item-text">الأمان</div>
            </a>
            <a href="access.html" class="toolbar-item">
                <div class="toolbar-item-icon">🚪</div>
                <div class="toolbar-item-text">التحكم في الوصول</div>
            </a>
            <a href="reports.html" class="toolbar-item">
                <div class="toolbar-item-icon">📊</div>
                <div class="toolbar-item-text">التقارير</div>
            </a>
            <a href="settings.html" class="toolbar-item">
                <div class="toolbar-item-icon">⚙️</div>
                <div class="toolbar-item-text">الإعدادات</div>
            </a>
            <a href="gps.html" class="toolbar-item active">
                <div class="toolbar-item-icon">📍</div>
                <div class="toolbar-item-text">تتبع GPS</div>
            </a>
        </div>

        <div class="toolbar-section">
            <div class="toolbar-section-title">Abdel Fattah Mahmoud</div>
            <a href="index.html" class="toolbar-item">
                <div class="toolbar-item-icon">📹</div>
                <div class="toolbar-item-text">مراقبة الكاميرات</div>
            </a>
            <a href="cameras.html" class="toolbar-item">
                <div class="toolbar-item-icon">📷</div>
                <div class="toolbar-item-text">إدارة كاميرات IP</div>
            </a>
            <a href="access.html" class="toolbar-item">
                <div class="toolbar-item-icon">🔐</div>
                <div class="toolbar-item-text">التحكم في الوصول</div>
            </a>
            <a href="reports.html" class="toolbar-item">
                <div class="toolbar-item-icon">📈</div>
                <div class="toolbar-item-text">التقارير</div>
            </a>
            <a href="user_management.html" class="toolbar-item">
                <div class="toolbar-item-icon">👥</div>
                <div class="toolbar-item-text">إدارة المستخدمين</div>
            </a>
        </div>

        <div class="toolbar-user">
            <div class="toolbar-user-name">عبد الفتاح محمود</div>
            <div class="toolbar-user-role">مدير النظام</div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="gps.js"></script>
</body>
</html>