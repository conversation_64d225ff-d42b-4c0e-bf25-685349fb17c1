<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - Mobile ATM GPS</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="style.css">
    <style>
        #loading-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #121212;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .loader {
            border: 16px solid #301934; /* Dark purple */
            border-top: 16px solid #4B0082; /* Darker purple */
            border-radius: 50%;
            width: 120px;
            height: 120px;
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        #map {
            height: 500px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #ddd;
        }
        .vehicle-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .route-info {
            display: flex;
            gap: 15px;
            margin-top: 10px;
        }
        .active-route {
            border-left: 4px solid #38a169;
        }
        .inactive-route {
            border-left: 4px solid #e53e3e;
        }
        .danger-zone {
            border: 2px solid red;
            border-radius: 50%;
            opacity: 0.3;
        }
        .arrival-flower {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="pink"/><circle cx="30" cy="30" r="10" fill="white"/><circle cx="70" cy="30" r="10" fill="white"/><circle cx="30" cy="70" r="10" fill="white"/><circle cx="70" cy="70" r="10" fill="white"/></svg>');
            background-size: cover;
            width: 30px;
            height: 30px;
            border-radius: 50%;
        }
        .alarm-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background: red;
            color: white;
            padding: 10px;
            border-radius: 5px;
            display: none;
        }
    </style>
</head>
<body>
    <div id="loading-container">
        <div class="loader"></div>
    </div>
    <script>
        window.addEventListener('load', function() {
            document.getElementById('loading-container').style.display = 'none';
        });
    </script>
    <!-- Banking Header -->
    <header class="bank-header">
        <div class="bank-logo">EG BANK</div>
        <nav class="bank-nav">
            <ul>
                <li><a href="index.html" style="color: white;">Dashboard</a></li>
                <li><a href="cameras.html" style="color: white;">Security</a></li>
                <li><a href="access.html" style="color: white;">Access Control</a></li>
                <li><a href="reports.html" style="color: white;">Reports</a></li>
                <li><a href="settings.html" style="color: white;">Settings</a></li>
                <li><a href="gps.html" class="active" style="color: white;">Mobile ATM GPS</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main Content Area -->
    <div class="bank-container">
        <!-- Sidebar -->
        <aside class="bank-sidebar">
            <h3 style="color: white;">Abdel Fattah Mahmoud</h3>
            <ul>
                <li><a href="index.html" style="color: white;">Camera Surveillance</a></li>
                <li><a href="cameras.html" style="color: white;">IP Camera Management</a></li>
                <li><a href="access.html" style="color: white;">Access Control</a></li>
                <li><a href="reports.html" style="color: white;">Reports</a></li>
                <li><a href="gps.html" class="active" style="color: white;">Mobile ATM GPS</a></li>
            </ul>
        </aside>

        <!-- Mobile ATM GPS Section -->
        <main class="bank-main">
            <h1 style="color: white;">Mobile ATM GPS</h1>

            <div class="form-container">
                <form id="vehicleForm">
                    <div class="form-group">
                        <label for="driverName" style="color: black;">Driver Name:</label>
                        <input type="text" id="driverName" required style="color: black; background-color: white;">
                    </div>

                    <div class="form-group">
                        <label for="driverPhone" style="color: black;">Driver Phone:</label>
                        <input type="tel" id="driverPhone" required style="color: black; background-color: white;">
                    </div>

                    <div class="form-group">
                        <label for="deviceSerial" style="color: black;">Device Serial:</label>
                        <input type="text" id="deviceSerial" required style="color: black; background-color: white;">
                    </div>

                    <div class="form-group">
                        <label for="plateNumber" style="color: black;">Plate Number:</label>
                        <input type="text" id="plateNumber" required style="color: black; background-color: white;">
                    </div>

                    <div class="form-actions">
                        <button type="button" id="addVehicleBtn" style="color: white;">Add Vehicle</button>
                        <button type="button" id="startTrackingBtn" style="color: white;">Start Tracking</button>
                        <button type="button" id="stopTrackingBtn" style="color: white;">Stop Tracking</button>
                    </div>
                </form>

                <div id="map"></div>

                <div class="vehicles-list">
                    <h2 style="color: white;">Tracked Vehicles</h2>
                    <div id="vehiclesContainer">
                        <!-- Vehicle cards will be added here -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Banking Footer -->
    <footer class="bank-footer">
    </footer>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="gps.js"></script>
</body>
</html>