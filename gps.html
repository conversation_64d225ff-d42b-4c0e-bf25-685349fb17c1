<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - Mobile ATM GPS</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="style.css">
    <style>
        #loading-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #2c2c2c;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .loader {
            border: 16px solid #3a3a3a;
            border-top: 16px solid #1a365d;
            border-radius: 50%;
            width: 120px;
            height: 120px;
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Search Section */
        .search-container {
            background: #3a3a3a;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .search-bar {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .search-input {
            flex: 1;
            min-width: 250px;
            padding: 12px;
            border: 1px solid #555555;
            border-radius: 4px;
            background-color: #2c2c2c;
            color: white;
            font-size: 16px;
        }

        .search-btn {
            background-color: #1a365d;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
        }

        .search-btn:hover {
            background-color: #2c5282;
        }

        .filter-options {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid #555555;
            border-radius: 4px;
            background-color: #2c2c2c;
            color: white;
        }

        /* Vehicle Cards */
        .vehicle-card {
            background: #4a4a4a;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            border: 2px solid #555555;
            transition: all 0.3s ease;
        }

        .vehicle-card:hover {
            border-color: #1a365d;
            transform: translateY(-2px);
        }

        .vehicle-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .vehicle-name {
            color: white;
            font-size: 18px;
            font-weight: bold;
        }

        .vehicle-status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background-color: #48bb78;
            color: white;
        }

        .status-inactive {
            background-color: #f56565;
            color: white;
        }

        .vehicle-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .info-item {
            color: #a0a0a0;
            font-size: 14px;
        }

        .info-label {
            color: white;
            font-weight: 500;
        }

        .vehicle-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .action-btn {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
        }

        .btn-edit {
            background-color: #1a365d;
            color: white;
        }

        .btn-edit:hover {
            background-color: #2c5282;
        }

        .btn-delete {
            background-color: #e53e3e;
            color: white;
        }

        .btn-delete:hover {
            background-color: #c53030;
        }

        .btn-view-history {
            background-color: #38a169;
            color: white;
        }

        .btn-view-history:hover {
            background-color: #2f855a;
        }

        /* Database Modal */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
        }

        .modal-content {
            background-color: #3a3a3a;
            margin: 5% auto;
            padding: 30px;
            border-radius: 8px;
            width: 90%;
            max-width: 1200px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: white;
        }

        /* History Table */
        .history-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .history-table th,
        .history-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #555555;
            color: white;
        }

        .history-table th {
            background-color: #1a365d;
            font-weight: 500;
        }

        .history-table tbody tr:nth-child(even) {
            background-color: #4a4a4a;
        }

        .history-table tbody tr:hover {
            background-color: #555555;
        }

        /* Statistics Cards */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: #3a3a3a;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border: 2px solid #555555;
        }

        .stat-number {
            font-size: 28px;
            font-weight: bold;
            color: #1a365d;
            margin-bottom: 5px;
        }

        .stat-label {
            color: white;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div id="loading-container">
        <div class="loader"></div>
    </div>
    <script>
        window.addEventListener('load', function() {
            document.getElementById('loading-container').style.display = 'none';
        });
    </script>

    <!-- Banking Header -->
    <header class="bank-header">
        <div class="bank-logo">EG BANK</div>
        <nav class="bank-nav">
            <ul>
                <li><a href="index.html" style="color: white;" data-en="Dashboard" data-ar="لوحة التحكم">لوحة التحكم</a></li>
                <li><a href="cameras.html" style="color: white;" data-en="Security" data-ar="الأمان">الأمان</a></li>
                <li><a href="access.html" style="color: white;" data-en="Access Control" data-ar="التحكم في الوصول">التحكم في الوصول</a></li>
                <li><a href="reports.html" style="color: white;" data-en="Reports" data-ar="التقارير">التقارير</a></li>
                <li><a href="settings.html" style="color: white;" data-en="Settings" data-ar="الإعدادات">الإعدادات</a></li>
                <li><a href="gps.html" class="active" style="color: white;" data-en="Mobile ATM GPS" data-ar="تتبع أجهزة الصراف">تتبع أجهزة الصراف</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main Content Area -->
    <div class="bank-container">
        <!-- Sidebar -->
        <aside class="bank-sidebar">
            <h3 style="color: white;">عبد الفتاح محمود</h3>
            <ul>
                <li><a href="index.html" style="color: white;" data-en="Camera Surveillance" data-ar="مراقبة الكاميرات">مراقبة الكاميرات</a></li>
                <li><a href="cameras.html" style="color: white;" data-en="IP Camera Management" data-ar="إدارة كاميرات IP">إدارة كاميرات IP</a></li>
                <li><a href="access.html" style="color: white;" data-en="Access Control" data-ar="التحكم في الوصول">التحكم في الوصول</a></li>
                <li><a href="reports.html" style="color: white;" data-en="Reports" data-ar="التقارير">التقارير</a></li>
                <li class="active" style="color: white;" data-en="Mobile ATM GPS" data-ar="تتبع أجهزة الصراف">تتبع أجهزة الصراف</li>
            </ul>
        </aside>

        <!-- Mobile ATM GPS Section -->
        <main class="bank-main">
            <h1 style="color: white;" data-en="Mobile ATM GPS Tracking" data-ar="تتبع أجهزة الصراف المتنقلة">تتبع أجهزة الصراف المتنقلة</h1>

            <!-- Statistics -->
            <div class="stats-container">
                <div class="stat-card">
                    <div class="stat-number" id="totalVehicles">8</div>
                    <div class="stat-label" data-en="Total Vehicles" data-ar="إجمالي المركبات">إجمالي المركبات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="activeVehicles">3</div>
                    <div class="stat-label" data-en="Active" data-ar="نشط">نشط</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="inactiveVehicles">5</div>
                    <div class="stat-label" data-en="Inactive" data-ar="غير نشط">غير نشط</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalDistance">1,247</div>
                    <div class="stat-label" data-en="Total KM Today" data-ar="إجمالي الكيلومترات اليوم">إجمالي الكيلومترات اليوم</div>
                </div>
            </div>

            <!-- Search Section -->
            <div class="search-container">
                <h2 style="color: white; margin-bottom: 20px;" data-en="Search & Filter" data-ar="البحث والتصفية">البحث والتصفية</h2>

                <div class="search-bar">
                    <input type="text" id="searchInput" class="search-input" placeholder="البحث بالاسم، رقم الهاتف، أو رقم اللوحة..." data-en-placeholder="Search by name, phone, or plate number..." data-ar-placeholder="البحث بالاسم، رقم الهاتف، أو رقم اللوحة...">
                    <button class="search-btn" onclick="searchVehicles()" data-en="Search" data-ar="بحث">بحث</button>
                    <button class="search-btn" onclick="clearSearch()" data-en="Clear" data-ar="مسح">مسح</button>
                    <button class="search-btn" onclick="openDatabaseModal()" data-en="View Database" data-ar="عرض قاعدة البيانات">عرض قاعدة البيانات</button>
                </div>

                <div class="filter-options">
                    <select id="statusFilter" class="filter-select" onchange="filterVehicles()">
                        <option value="all" data-en="All Status" data-ar="جميع الحالات">جميع الحالات</option>
                        <option value="active" data-en="Active Only" data-ar="النشط فقط">النشط فقط</option>
                        <option value="inactive" data-en="Inactive Only" data-ar="غير النشط فقط">غير النشط فقط</option>
                    </select>

                    <select id="dateFilter" class="filter-select" onchange="filterVehicles()">
                        <option value="today" data-en="Today" data-ar="اليوم">اليوم</option>
                        <option value="yesterday" data-en="Yesterday" data-ar="أمس">أمس</option>
                        <option value="week" data-en="This Week" data-ar="هذا الأسبوع">هذا الأسبوع</option>
                        <option value="month" data-en="This Month" data-ar="هذا الشهر">هذا الشهر</option>
                    </select>
                </div>
            </div>

            <!-- Vehicles List -->
            <div class="access-container">
                <h2 style="color: white; margin-bottom: 20px;" data-en="Tracked Vehicles" data-ar="المركبات المتتبعة">المركبات المتتبعة</h2>
                <div id="vehiclesContainer">
                    <!-- Vehicle cards will be populated by JavaScript -->
                </div>
            </div>
        </main>
    </div>

    <!-- Database Modal -->
    <div id="databaseModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeDatabaseModal()">&times;</span>
            <h2 style="color: white; margin-bottom: 20px;" data-en="Vehicle Database & History" data-ar="قاعدة بيانات المركبات والتاريخ">قاعدة بيانات المركبات والتاريخ</h2>

            <div class="search-bar" style="margin-bottom: 20px;">
                <input type="text" id="dbSearchInput" class="search-input" placeholder="البحث في قاعدة البيانات..." data-en-placeholder="Search in database..." data-ar-placeholder="البحث في قاعدة البيانات...">
                <button class="search-btn" onclick="searchDatabase()" data-en="Search" data-ar="بحث">بحث</button>
            </div>

            <table class="history-table" id="historyTable">
                <thead>
                    <tr>
                        <th data-en="Vehicle Name" data-ar="اسم المركبة">اسم المركبة</th>
                        <th data-en="Driver" data-ar="السائق">السائق</th>
                        <th data-en="Phone" data-ar="الهاتف">الهاتف</th>
                        <th data-en="Device" data-ar="الجهاز">الجهاز</th>
                        <th data-en="Distance" data-ar="المسافة">المسافة</th>
                        <th data-en="Status" data-ar="الحالة">الحالة</th>
                        <th data-en="Last Update" data-ar="آخر تحديث">آخر تحديث</th>
                        <th data-en="Date" data-ar="التاريخ">التاريخ</th>
                    </tr>
                </thead>
                <tbody id="historyTableBody">
                    <!-- History data will be populated by JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- Banking Footer -->
    <footer class="bank-footer">
    </footer>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="gps.js"></script>
</body>
</html>