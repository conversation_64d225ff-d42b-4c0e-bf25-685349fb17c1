<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - كاميرا اللاب توب</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(26, 54, 93, 0.9);
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(45deg, #1a365d, #2c5282);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .logo-text {
            font-size: 28px;
            font-weight: 900;
            color: white;
        }
        
        .nav-buttons {
            display: flex;
            gap: 15px;
        }
        
        .nav-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
        }
        
        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .page-title {
            text-align: center;
            font-size: 36px;
            font-weight: 900;
            margin-bottom: 10px;
            color: #3182ce;
            text-shadow: 0 0 20px rgba(49, 130, 206, 0.5);
        }
        
        .page-subtitle {
            text-align: center;
            font-size: 18px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 40px;
        }
        
        .camera-container {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(20px);
            text-align: center;
        }
        
        .video-container {
            position: relative;
            display: inline-block;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
            margin-bottom: 30px;
        }
        
        #videoElement {
            width: 100%;
            max-width: 800px;
            height: auto;
            display: block;
            background: #000;
        }
        
        .video-overlay {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
        }
        
        .recording-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #f56565;
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            display: none;
            animation: blink 1s infinite;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.5; }
        }
        
        .camera-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .control-btn {
            background: linear-gradient(135deg, #1a365d, #2c5282);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            font-size: 16px;
        }
        
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(26, 54, 93, 0.4);
        }
        
        .control-btn:disabled {
            background: linear-gradient(135deg, #666, #888);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .start-btn {
            background: linear-gradient(135deg, #48bb78, #38a169);
        }
        
        .stop-btn {
            background: linear-gradient(135deg, #f56565, #e53e3e);
        }
        
        .capture-btn {
            background: linear-gradient(135deg, #ed8936, #dd6b20);
        }
        
        .record-btn {
            background: linear-gradient(135deg, #9f7aea, #805ad5);
        }
        
        .settings-btn {
            background: linear-gradient(135deg, #4299e1, #3182ce);
        }
        
        .camera-status {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .status-indicator {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .status-offline {
            color: #f56565;
        }
        
        .status-online {
            color: #48bb78;
        }
        
        .status-recording {
            color: #ed8936;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .captured-images {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(20px);
        }
        
        .panel-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 20px;
            color: white;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .images-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .image-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .image-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }
        
        .captured-img {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        
        .image-info {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 10px;
        }
        
        .image-actions {
            display: flex;
            gap: 5px;
            justify-content: center;
        }
        
        .action-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 6px 10px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .download-btn {
            background: linear-gradient(135deg, #48bb78, #38a169);
        }
        
        .delete-btn {
            background: linear-gradient(135deg, #f56565, #e53e3e);
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
        }
        
        .modal-content {
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            margin: 5% auto;
            padding: 30px;
            border-radius: 20px;
            width: 90%;
            max-width: 500px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .modal-title {
            font-size: 24px;
            font-weight: 700;
            color: #3182ce;
        }
        
        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }
        
        .form-select {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 12px;
            color: white;
            font-size: 14px;
        }
        
        .form-select option {
            background: #1a1a2e;
            color: white;
        }
        
        .save-btn {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .save-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
        }
        
        .error-message {
            background: rgba(245, 101, 101, 0.1);
            border: 1px solid rgba(245, 101, 101, 0.3);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            color: #f56565;
            margin: 20px 0;
        }
        
        .canvas-container {
            display: none;
        }
        
        @media (max-width: 768px) {
            .camera-controls {
                grid-template-columns: 1fr;
            }
            
            .images-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }
            
            #videoElement {
                max-width: 100%;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">🏦</div>
                <div class="logo-text">EG BANK</div>
            </div>
            
            <div class="nav-buttons">
                <a href="home_simple.html" class="nav-btn">🏠 الرئيسية</a>
                <a href="cameras.html" class="nav-btn">📹 المراقبة</a>
                <a href="security_tasks.html" class="nav-btn">🔧 المهام</a>
                <a href="fire_alarm_system.html" class="nav-btn">🔥 إنذار الحريق</a>
            </div>
        </div>
    </header>
    
    <div class="container">
        <h1 class="page-title">📷 كاميرا اللاب توب</h1>
        <p class="page-subtitle">التقاط الصور والفيديو من كاميرا الجهاز</p>
        
        <!-- Camera Status -->
        <div class="camera-status">
            <div class="status-indicator status-offline" id="cameraStatus">
                📷 الكاميرا متوقفة
            </div>
            <div id="statusMessage">اضغط "تشغيل الكاميرا" للبدء</div>
        </div>
        
        <!-- Camera Container -->
        <div class="camera-container">
            <div class="video-container">
                <video id="videoElement" autoplay muted playsinline>
                    متصفحك لا يدعم عرض الفيديو
                </video>
                <div class="video-overlay" id="videoOverlay">
                    EG BANK Camera
                </div>
                <div class="recording-indicator" id="recordingIndicator">
                    🔴 جاري التسجيل
                </div>
            </div>
            
            <div id="errorMessage" class="error-message" style="display: none;">
                لا يمكن الوصول إلى الكاميرا. تأكد من منح الإذن للموقع.
            </div>
        </div>
        
        <!-- Camera Controls -->
        <div class="camera-controls">
            <button class="control-btn start-btn" id="startBtn" onclick="startCamera()">
                ▶️ تشغيل الكاميرا
            </button>
            <button class="control-btn stop-btn" id="stopBtn" onclick="stopCamera()" disabled>
                ⏹️ إيقاف الكاميرا
            </button>
            <button class="control-btn capture-btn" id="captureBtn" onclick="capturePhoto()" disabled>
                📸 التقاط صورة
            </button>
            <button class="control-btn record-btn" id="recordBtn" onclick="toggleRecording()" disabled>
                🎥 بدء التسجيل
            </button>
            <button class="control-btn settings-btn" onclick="openSettings()">
                ⚙️ الإعدادات
            </button>
            <button class="control-btn" onclick="clearAllImages()">
                🗑️ مسح الكل
            </button>
        </div>
        
        <!-- Captured Images -->
        <div class="captured-images">
            <h2 class="panel-title">📸 الصور الملتقطة</h2>
            <div class="images-grid" id="imagesGrid">
                <div style="text-align: center; color: rgba(255,255,255,0.5); grid-column: 1/-1;">
                    لا توجد صور ملتقطة بعد
                </div>
            </div>
        </div>
    </div>
    
    <!-- Settings Modal -->
    <div id="settingsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">⚙️ إعدادات الكاميرا</h3>
                <button class="close-btn" onclick="closeSettings()">&times;</button>
            </div>
            
            <form id="settingsForm">
                <div class="form-group">
                    <label class="form-label">جودة الفيديو:</label>
                    <select class="form-select" id="videoQuality">
                        <option value="720p">HD (720p)</option>
                        <option value="1080p" selected>Full HD (1080p)</option>
                        <option value="4k">4K (2160p)</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">الكاميرا:</label>
                    <select class="form-select" id="cameraSelect">
                        <option value="">الكاميرا الافتراضية</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">تنسيق الصورة:</label>
                    <select class="form-select" id="imageFormat">
                        <option value="jpeg">JPEG</option>
                        <option value="png">PNG</option>
                        <option value="webp">WebP</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">جودة الصورة:</label>
                    <select class="form-select" id="imageQuality">
                        <option value="0.7">عادية (70%)</option>
                        <option value="0.8">جيدة (80%)</option>
                        <option value="0.9" selected>عالية (90%)</option>
                        <option value="1.0">ممتازة (100%)</option>
                    </select>
                </div>
                
                <button type="submit" class="save-btn">💾 حفظ الإعدادات</button>
            </form>
        </div>
    </div>
    
    <!-- Hidden Canvas for Capturing -->
    <div class="canvas-container">
        <canvas id="captureCanvas"></canvas>
    </div>
    
    <script>
        // Laptop Camera System
        class LaptopCameraSystem {
            constructor() {
                this.stream = null;
                this.mediaRecorder = null;
                this.recordedChunks = [];
                this.isRecording = false;
                this.capturedImages = [];
                this.settings = {
                    videoQuality: '1080p',
                    cameraId: null,
                    imageFormat: 'jpeg',
                    imageQuality: 0.9
                };
                this.init();
            }

            init() {
                console.log('📷 Initializing Laptop Camera System...');

                this.loadSettings();
                this.loadCapturedImages();
                this.setupEventListeners();
                this.updateImagesGrid();
                this.getCameraDevices();

                console.log('✅ Laptop Camera System initialized successfully');
            }

            loadSettings() {
                try {
                    const savedSettings = localStorage.getItem('camera_settings');
                    if (savedSettings) {
                        this.settings = { ...this.settings, ...JSON.parse(savedSettings) };
                    }
                } catch (error) {
                    console.error('Error loading settings:', error);
                }
            }

            saveSettings() {
                try {
                    localStorage.setItem('camera_settings', JSON.stringify(this.settings));
                    console.log('💾 Camera settings saved');
                } catch (error) {
                    console.error('Error saving settings:', error);
                }
            }

            loadCapturedImages() {
                try {
                    const savedImages = localStorage.getItem('captured_images');
                    if (savedImages) {
                        this.capturedImages = JSON.parse(savedImages);
                    }
                } catch (error) {
                    console.error('Error loading captured images:', error);
                    this.capturedImages = [];
                }
            }

            saveCapturedImages() {
                try {
                    localStorage.setItem('captured_images', JSON.stringify(this.capturedImages));
                } catch (error) {
                    console.error('Error saving captured images:', error);
                }
            }

            setupEventListeners() {
                // Settings form
                document.getElementById('settingsForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.updateSettings();
                });

                // Update overlay time
                setInterval(() => {
                    this.updateVideoOverlay();
                }, 1000);
            }

            async getCameraDevices() {
                try {
                    const devices = await navigator.mediaDevices.enumerateDevices();
                    const videoDevices = devices.filter(device => device.kind === 'videoinput');

                    const cameraSelect = document.getElementById('cameraSelect');
                    cameraSelect.innerHTML = '<option value="">الكاميرا الافتراضية</option>';

                    videoDevices.forEach((device, index) => {
                        const option = document.createElement('option');
                        option.value = device.deviceId;
                        option.textContent = device.label || `كاميرا ${index + 1}`;
                        cameraSelect.appendChild(option);
                    });

                    console.log(`📷 Found ${videoDevices.length} camera devices`);
                } catch (error) {
                    console.error('Error getting camera devices:', error);
                }
            }

            getVideoConstraints() {
                const constraints = {
                    video: {
                        facingMode: 'user'
                    },
                    audio: false
                };

                // Set video quality
                switch (this.settings.videoQuality) {
                    case '720p':
                        constraints.video.width = { ideal: 1280 };
                        constraints.video.height = { ideal: 720 };
                        break;
                    case '1080p':
                        constraints.video.width = { ideal: 1920 };
                        constraints.video.height = { ideal: 1080 };
                        break;
                    case '4k':
                        constraints.video.width = { ideal: 3840 };
                        constraints.video.height = { ideal: 2160 };
                        break;
                }

                // Set specific camera if selected
                if (this.settings.cameraId) {
                    constraints.video.deviceId = { exact: this.settings.cameraId };
                }

                return constraints;
            }

            async startCamera() {
                try {
                    this.hideError();
                    this.updateStatus('connecting', 'جاري الاتصال بالكاميرا...');

                    const constraints = this.getVideoConstraints();
                    this.stream = await navigator.mediaDevices.getUserMedia(constraints);

                    const videoElement = document.getElementById('videoElement');
                    videoElement.srcObject = this.stream;

                    this.updateStatus('online', 'الكاميرا متصلة وتعمل');
                    this.updateButtons(true);

                    console.log('📷 Camera started successfully');
                } catch (error) {
                    console.error('Error starting camera:', error);
                    this.showError('لا يمكن الوصول إلى الكاميرا. تأكد من منح الإذن للموقع.');
                    this.updateStatus('offline', 'فشل في تشغيل الكاميرا');
                }
            }

            stopCamera() {
                if (this.stream) {
                    this.stream.getTracks().forEach(track => track.stop());
                    this.stream = null;

                    const videoElement = document.getElementById('videoElement');
                    videoElement.srcObject = null;

                    this.updateStatus('offline', 'تم إيقاف الكاميرا');
                    this.updateButtons(false);

                    // Stop recording if active
                    if (this.isRecording) {
                        this.stopRecording();
                    }

                    console.log('📷 Camera stopped');
                }
            }

            capturePhoto() {
                if (!this.stream) {
                    alert('يجب تشغيل الكاميرا أولاً');
                    return;
                }

                try {
                    const videoElement = document.getElementById('videoElement');
                    const canvas = document.getElementById('captureCanvas');
                    const ctx = canvas.getContext('2d');

                    // Set canvas size to match video
                    canvas.width = videoElement.videoWidth;
                    canvas.height = videoElement.videoHeight;

                    // Draw video frame to canvas
                    ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

                    // Convert to image data
                    const imageData = canvas.toDataURL(`image/${this.settings.imageFormat}`, this.settings.imageQuality);

                    // Save captured image
                    const capturedImage = {
                        id: Date.now(),
                        data: imageData,
                        timestamp: new Date().toLocaleString('ar-EG'),
                        format: this.settings.imageFormat,
                        size: this.formatFileSize(imageData.length * 0.75) // Approximate size
                    };

                    this.capturedImages.push(capturedImage);
                    this.saveCapturedImages();
                    this.updateImagesGrid();

                    // Flash effect
                    this.flashEffect();

                    console.log('📸 Photo captured successfully');
                } catch (error) {
                    console.error('Error capturing photo:', error);
                    alert('حدث خطأ أثناء التقاط الصورة');
                }
            }

            async toggleRecording() {
                if (this.isRecording) {
                    this.stopRecording();
                } else {
                    this.startRecording();
                }
            }

            async startRecording() {
                if (!this.stream) {
                    alert('يجب تشغيل الكاميرا أولاً');
                    return;
                }

                try {
                    // Add audio to stream for recording
                    const audioStream = await navigator.mediaDevices.getUserMedia({ audio: true });
                    const combinedStream = new MediaStream([
                        ...this.stream.getVideoTracks(),
                        ...audioStream.getAudioTracks()
                    ]);

                    this.recordedChunks = [];
                    this.mediaRecorder = new MediaRecorder(combinedStream, {
                        mimeType: 'video/webm;codecs=vp9'
                    });

                    this.mediaRecorder.ondataavailable = (event) => {
                        if (event.data.size > 0) {
                            this.recordedChunks.push(event.data);
                        }
                    };

                    this.mediaRecorder.onstop = () => {
                        this.saveRecording();
                    };

                    this.mediaRecorder.start();
                    this.isRecording = true;

                    this.updateRecordingUI(true);
                    console.log('🎥 Recording started');
                } catch (error) {
                    console.error('Error starting recording:', error);
                    alert('حدث خطأ أثناء بدء التسجيل');
                }
            }

            stopRecording() {
                if (this.mediaRecorder && this.isRecording) {
                    this.mediaRecorder.stop();
                    this.isRecording = false;
                    this.updateRecordingUI(false);
                    console.log('🎥 Recording stopped');
                }
            }

            saveRecording() {
                if (this.recordedChunks.length > 0) {
                    const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
                    const url = URL.createObjectURL(blob);

                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `recording_${new Date().toISOString().split('T')[0]}_${Date.now()}.webm`;
                    a.click();

                    URL.revokeObjectURL(url);
                    alert('تم حفظ التسجيل بنجاح');
                }
            }

            updateStatus(status, message) {
                const statusElement = document.getElementById('cameraStatus');
                const messageElement = document.getElementById('statusMessage');

                statusElement.className = `status-indicator status-${status}`;

                switch (status) {
                    case 'offline':
                        statusElement.textContent = '📷 الكاميرا متوقفة';
                        break;
                    case 'online':
                        statusElement.textContent = '📷 الكاميرا متصلة';
                        break;
                    case 'connecting':
                        statusElement.textContent = '📷 جاري الاتصال...';
                        break;
                    case 'recording':
                        statusElement.textContent = '🎥 جاري التسجيل';
                        break;
                }

                messageElement.textContent = message;
            }

            updateButtons(cameraActive) {
                document.getElementById('startBtn').disabled = cameraActive;
                document.getElementById('stopBtn').disabled = !cameraActive;
                document.getElementById('captureBtn').disabled = !cameraActive;
                document.getElementById('recordBtn').disabled = !cameraActive;
            }

            updateRecordingUI(recording) {
                const recordBtn = document.getElementById('recordBtn');
                const recordingIndicator = document.getElementById('recordingIndicator');

                if (recording) {
                    recordBtn.innerHTML = '⏹️ إيقاف التسجيل';
                    recordingIndicator.style.display = 'block';
                    this.updateStatus('recording', 'جاري تسجيل الفيديو');
                } else {
                    recordBtn.innerHTML = '🎥 بدء التسجيل';
                    recordingIndicator.style.display = 'none';
                    this.updateStatus('online', 'الكاميرا متصلة وتعمل');
                }
            }

            updateVideoOverlay() {
                const overlay = document.getElementById('videoOverlay');
                const now = new Date();
                overlay.textContent = `EG BANK Camera - ${now.toLocaleTimeString('ar-EG')}`;
            }

            flashEffect() {
                const videoContainer = document.querySelector('.video-container');
                videoContainer.style.filter = 'brightness(2)';
                setTimeout(() => {
                    videoContainer.style.filter = 'brightness(1)';
                }, 100);
            }

            showError(message) {
                const errorElement = document.getElementById('errorMessage');
                errorElement.textContent = message;
                errorElement.style.display = 'block';
            }

            hideError() {
                document.getElementById('errorMessage').style.display = 'none';
            }

            updateImagesGrid() {
                const grid = document.getElementById('imagesGrid');

                if (this.capturedImages.length === 0) {
                    grid.innerHTML = '<div style="text-align: center; color: rgba(255,255,255,0.5); grid-column: 1/-1;">لا توجد صور ملتقطة بعد</div>';
                    return;
                }

                grid.innerHTML = '';

                this.capturedImages.forEach((image, index) => {
                    const imageCard = document.createElement('div');
                    imageCard.className = 'image-card';

                    imageCard.innerHTML = `
                        <img src="${image.data}" alt="صورة ${index + 1}" class="captured-img">
                        <div class="image-info">
                            ${image.timestamp}<br>
                            ${image.format.toUpperCase()} - ${image.size}
                        </div>
                        <div class="image-actions">
                            <button class="action-btn download-btn" onclick="downloadImage(${image.id})">
                                📥 تحميل
                            </button>
                            <button class="action-btn delete-btn" onclick="deleteImage(${image.id})">
                                🗑️ حذف
                            </button>
                        </div>
                    `;

                    grid.appendChild(imageCard);
                });
            }

            downloadImage(imageId) {
                const image = this.capturedImages.find(img => img.id === imageId);
                if (!image) return;

                const a = document.createElement('a');
                a.href = image.data;
                a.download = `photo_${image.id}.${image.format}`;
                a.click();
            }

            deleteImage(imageId) {
                if (confirm('هل أنت متأكد من حذف هذه الصورة؟')) {
                    this.capturedImages = this.capturedImages.filter(img => img.id !== imageId);
                    this.saveCapturedImages();
                    this.updateImagesGrid();
                }
            }

            clearAllImages() {
                if (this.capturedImages.length === 0) {
                    alert('لا توجد صور لحذفها');
                    return;
                }

                if (confirm(`هل أنت متأكد من حذف جميع الصور (${this.capturedImages.length} صورة)؟`)) {
                    this.capturedImages = [];
                    this.saveCapturedImages();
                    this.updateImagesGrid();
                    alert('تم حذف جميع الصور');
                }
            }

            openSettings() {
                // Fill current settings
                document.getElementById('videoQuality').value = this.settings.videoQuality;
                document.getElementById('cameraSelect').value = this.settings.cameraId || '';
                document.getElementById('imageFormat').value = this.settings.imageFormat;
                document.getElementById('imageQuality').value = this.settings.imageQuality;

                document.getElementById('settingsModal').style.display = 'block';
            }

            closeSettings() {
                document.getElementById('settingsModal').style.display = 'none';
            }

            updateSettings() {
                this.settings.videoQuality = document.getElementById('videoQuality').value;
                this.settings.cameraId = document.getElementById('cameraSelect').value;
                this.settings.imageFormat = document.getElementById('imageFormat').value;
                this.settings.imageQuality = parseFloat(document.getElementById('imageQuality').value);

                this.saveSettings();
                this.closeSettings();

                // Restart camera with new settings if active
                if (this.stream) {
                    this.stopCamera();
                    setTimeout(() => this.startCamera(), 1000);
                }

                alert('تم حفظ الإعدادات بنجاح');
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }
        }

        // Global functions
        function startCamera() {
            if (window.cameraSystem) {
                window.cameraSystem.startCamera();
            }
        }

        function stopCamera() {
            if (window.cameraSystem) {
                window.cameraSystem.stopCamera();
            }
        }

        function capturePhoto() {
            if (window.cameraSystem) {
                window.cameraSystem.capturePhoto();
            }
        }

        function toggleRecording() {
            if (window.cameraSystem) {
                window.cameraSystem.toggleRecording();
            }
        }

        function openSettings() {
            if (window.cameraSystem) {
                window.cameraSystem.openSettings();
            }
        }

        function closeSettings() {
            if (window.cameraSystem) {
                window.cameraSystem.closeSettings();
            }
        }

        function downloadImage(imageId) {
            if (window.cameraSystem) {
                window.cameraSystem.downloadImage(imageId);
            }
        }

        function deleteImage(imageId) {
            if (window.cameraSystem) {
                window.cameraSystem.deleteImage(imageId);
            }
        }

        function clearAllImages() {
            if (window.cameraSystem) {
                window.cameraSystem.clearAllImages();
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📷 Laptop Camera System loading...');
            window.cameraSystem = new LaptopCameraSystem();
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            const modal = document.getElementById('settingsModal');
            if (event.target === modal) {
                closeSettings();
            }
        });

        // Handle page unload
        window.addEventListener('beforeunload', function() {
            if (window.cameraSystem && window.cameraSystem.stream) {
                window.cameraSystem.stopCamera();
            }
        });

        console.log('📷 Laptop Camera System Script Loaded');
    </script>
</body>
</html>
