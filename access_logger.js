// Advanced Access Logger System
// نظام تسجيل الوصول المتقدم

class AccessLogger {
    constructor() {
        this.dbManager = window.dbManager;
        this.currentSession = null;
        this.init();
    }

    async init() {
        // Wait for database to be ready
        if (this.dbManager && !this.dbManager.isInitialized) {
            setTimeout(() => this.init(), 100);
            return;
        }
        
        this.startSession();
        this.setupEventListeners();
        console.log('Access Logger initialized');
    }

    // Start user session
    async startSession() {
        const username = localStorage.getItem('username');
        if (!username) return;

        this.currentSession = {
            id: 'session_' + Date.now(),
            userId: username,
            startTime: new Date().toISOString(),
            endTime: null,
            actions: [],
            ipAddress: await this.getIPAddress(),
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            language: navigator.language
        };

        await this.logAction('login', 'تسجيل دخول المستخدم', {
            sessionId: this.currentSession.id,
            loginMethod: 'form'
        });
    }

    // End user session
    async endSession() {
        if (!this.currentSession) return;

        this.currentSession.endTime = new Date().toISOString();
        this.currentSession.duration = new Date(this.currentSession.endTime) - new Date(this.currentSession.startTime);

        await this.logAction('logout', 'تسجيل خروج المستخدم', {
            sessionId: this.currentSession.id,
            sessionDuration: this.currentSession.duration
        });

        this.currentSession = null;
    }

    // Log user action
    async logAction(action, description, details = {}) {
        const logEntry = {
            id: 'log_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
            userId: localStorage.getItem('username') || 'anonymous',
            sessionId: this.currentSession?.id || null,
            action: action,
            description: description,
            details: details,
            timestamp: new Date().toISOString(),
            page: window.location.pathname,
            url: window.location.href,
            ipAddress: await this.getIPAddress(),
            userAgent: navigator.userAgent,
            success: true
        };

        try {
            if (this.dbManager) {
                await this.dbManager.add('accessLogs', logEntry);
            } else {
                // Fallback to localStorage
                const logs = JSON.parse(localStorage.getItem('accessLogs') || '[]');
                logs.push(logEntry);
                // Keep only last 1000 logs in localStorage
                if (logs.length > 1000) {
                    logs.splice(0, logs.length - 1000);
                }
                localStorage.setItem('accessLogs', JSON.stringify(logs));
            }

            // Add to current session
            if (this.currentSession) {
                this.currentSession.actions.push({
                    action: action,
                    timestamp: logEntry.timestamp,
                    description: description
                });
            }

            console.log('Action logged:', action, description);
        } catch (error) {
            console.error('Failed to log action:', error);
        }
    }

    // Log security event
    async logSecurityEvent(eventType, severity, description, details = {}) {
        const securityLog = {
            id: 'security_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
            userId: localStorage.getItem('username') || 'system',
            eventType: eventType,
            severity: severity, // low, medium, high, critical
            description: description,
            details: details,
            timestamp: new Date().toISOString(),
            page: window.location.pathname,
            ipAddress: await this.getIPAddress(),
            resolved: false,
            investigationNotes: ''
        };

        try {
            if (this.dbManager) {
                await this.dbManager.add('securityAlerts', securityLog);
            } else {
                const alerts = JSON.parse(localStorage.getItem('securityAlerts') || '[]');
                alerts.push(securityLog);
                localStorage.setItem('securityAlerts', JSON.stringify(alerts));
            }

            // Also log as regular action
            await this.logAction('security_event', `حدث أمني: ${description}`, {
                eventType: eventType,
                severity: severity,
                ...details
            });

            console.log('Security event logged:', eventType, severity);
        } catch (error) {
            console.error('Failed to log security event:', error);
        }
    }

    // Setup event listeners for automatic logging
    setupEventListeners() {
        // Page navigation
        window.addEventListener('beforeunload', () => {
            this.logAction('page_leave', `مغادرة الصفحة: ${document.title}`);
        });

        // Form submissions
        document.addEventListener('submit', (e) => {
            const form = e.target;
            const formId = form.id || 'unknown_form';
            this.logAction('form_submit', `إرسال نموذج: ${formId}`, {
                formId: formId,
                formAction: form.action || window.location.href
            });
        });

        // Button clicks on important elements
        document.addEventListener('click', (e) => {
            const element = e.target;
            
            // Log clicks on important buttons
            if (element.tagName === 'BUTTON' || element.type === 'submit') {
                const buttonText = element.textContent.trim() || element.value || 'unknown_button';
                this.logAction('button_click', `نقر على زر: ${buttonText}`, {
                    buttonId: element.id,
                    buttonClass: element.className,
                    buttonText: buttonText
                });
            }

            // Log navigation clicks
            if (element.tagName === 'A' && element.href) {
                this.logAction('navigation', `الانتقال إلى: ${element.href}`, {
                    linkText: element.textContent.trim(),
                    targetUrl: element.href
                });
            }
        });

        // Failed login attempts
        document.addEventListener('loginFailed', (e) => {
            this.logSecurityEvent('failed_login', 'medium', 'محاولة تسجيل دخول فاشلة', {
                username: e.detail?.username || 'unknown',
                reason: e.detail?.reason || 'invalid_credentials'
            });
        });

        // Successful login
        document.addEventListener('loginSuccess', (e) => {
            this.logAction('login_success', 'تسجيل دخول ناجح', {
                username: e.detail?.username || localStorage.getItem('username')
            });
        });

        // Page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.logAction('page_hidden', 'إخفاء الصفحة');
            } else {
                this.logAction('page_visible', 'إظهار الصفحة');
            }
        });

        // Error logging
        window.addEventListener('error', (e) => {
            this.logSecurityEvent('javascript_error', 'low', 'خطأ في JavaScript', {
                message: e.message,
                filename: e.filename,
                lineno: e.lineno,
                colno: e.colno,
                stack: e.error?.stack
            });
        });
    }

    // Get user's IP address
    async getIPAddress() {
        try {
            // This is a simplified version - in a real application you'd use a proper IP detection service
            return 'localhost'; // Placeholder
        } catch (error) {
            return 'unknown';
        }
    }

    // Get access logs with filters
    async getAccessLogs(filters = {}) {
        try {
            if (this.dbManager) {
                return await this.dbManager.search('accessLogs', filters);
            } else {
                const logs = JSON.parse(localStorage.getItem('accessLogs') || '[]');
                return this.filterLogs(logs, filters);
            }
        } catch (error) {
            console.error('Failed to get access logs:', error);
            return [];
        }
    }

    // Get security alerts
    async getSecurityAlerts(filters = {}) {
        try {
            if (this.dbManager) {
                return await this.dbManager.search('securityAlerts', filters);
            } else {
                const alerts = JSON.parse(localStorage.getItem('securityAlerts') || '[]');
                return this.filterLogs(alerts, filters);
            }
        } catch (error) {
            console.error('Failed to get security alerts:', error);
            return [];
        }
    }

    // Filter logs based on criteria
    filterLogs(logs, filters) {
        return logs.filter(log => {
            if (filters.userId && log.userId !== filters.userId) return false;
            if (filters.action && log.action !== filters.action) return false;
            if (filters.startDate && new Date(log.timestamp) < new Date(filters.startDate)) return false;
            if (filters.endDate && new Date(log.timestamp) > new Date(filters.endDate)) return false;
            if (filters.severity && log.severity !== filters.severity) return false;
            return true;
        });
    }

    // Generate activity report
    async generateActivityReport(startDate, endDate) {
        const logs = await this.getAccessLogs({
            startDate: startDate,
            endDate: endDate
        });

        const report = {
            period: {
                start: startDate,
                end: endDate
            },
            totalActions: logs.length,
            uniqueUsers: [...new Set(logs.map(log => log.userId))].length,
            actionsByType: {},
            actionsByUser: {},
            actionsByHour: new Array(24).fill(0),
            topPages: {},
            securityEvents: 0
        };

        logs.forEach(log => {
            // Count by action type
            report.actionsByType[log.action] = (report.actionsByType[log.action] || 0) + 1;

            // Count by user
            report.actionsByUser[log.userId] = (report.actionsByUser[log.userId] || 0) + 1;

            // Count by hour
            const hour = new Date(log.timestamp).getHours();
            report.actionsByHour[hour]++;

            // Count by page
            report.topPages[log.page] = (report.topPages[log.page] || 0) + 1;

            // Count security events
            if (log.action === 'security_event') {
                report.securityEvents++;
            }
        });

        return report;
    }

    // Export logs to CSV
    async exportLogs(startDate, endDate) {
        const logs = await this.getAccessLogs({
            startDate: startDate,
            endDate: endDate
        });

        const csvHeaders = ['Timestamp', 'User ID', 'Action', 'Description', 'Page', 'IP Address', 'Details'];
        const csvRows = logs.map(log => [
            log.timestamp,
            log.userId,
            log.action,
            log.description,
            log.page,
            log.ipAddress,
            JSON.stringify(log.details)
        ]);

        const csvContent = [csvHeaders, ...csvRows]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `access_logs_${startDate}_${endDate}.csv`;
        link.click();
    }

    // Clean old logs
    async cleanOldLogs(days = 90) {
        const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
        let deletedCount = 0;

        try {
            if (this.dbManager) {
                const logs = await this.dbManager.getAll('accessLogs');
                for (const log of logs) {
                    if (new Date(log.timestamp) < cutoffDate) {
                        await this.dbManager.delete('accessLogs', log.id);
                        deletedCount++;
                    }
                }
            } else {
                const logs = JSON.parse(localStorage.getItem('accessLogs') || '[]');
                const filteredLogs = logs.filter(log => new Date(log.timestamp) >= cutoffDate);
                deletedCount = logs.length - filteredLogs.length;
                localStorage.setItem('accessLogs', JSON.stringify(filteredLogs));
            }

            console.log(`Cleaned ${deletedCount} old log entries`);
            return deletedCount;
        } catch (error) {
            console.error('Failed to clean old logs:', error);
            return 0;
        }
    }
}

// Create global access logger instance
const accessLogger = new AccessLogger();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AccessLogger;
}

// Helper functions for easy access
window.logAction = (action, description, details) => accessLogger.logAction(action, description, details);
window.logSecurityEvent = (eventType, severity, description, details) => accessLogger.logSecurityEvent(eventType, severity, description, details);

console.log('Advanced Access Logger Loaded Successfully');
