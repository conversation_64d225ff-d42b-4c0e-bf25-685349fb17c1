<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - Modern Security Interface</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        /* Animated Background */
        .bg-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            opacity: 0.1;
        }
        
        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
        }
        
        .shape {
            position: absolute;
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            left: 80%;
            animation-delay: 2s;
        }
        
        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            top: 80%;
            left: 20%;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        /* Header */
        .modern-header {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 30px;
        }
        
        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
        }
        
        .logo-text {
            color: white;
            font-size: 28px;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .user-profile {
            display: flex;
            align-items: center;
            gap: 15px;
            background: rgba(255, 255, 255, 0.1);
            padding: 10px 20px;
            border-radius: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }
        
        .user-info {
            color: white;
        }
        
        .user-name {
            font-weight: 600;
            font-size: 14px;
        }
        
        .user-role {
            font-size: 12px;
            opacity: 0.8;
        }
        
        /* Main Container */
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 30px;
        }
        
        /* Welcome Section */
        .welcome-section {
            text-align: center;
            margin-bottom: 50px;
        }
        
        .welcome-title {
            color: white;
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .welcome-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 18px;
            font-weight: 300;
        }
        
        /* Dashboard Grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }
        
        .dashboard-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            transition: all 0.4s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.6s;
        }
        
        .dashboard-card:hover::before {
            left: 100%;
        }
        
        .dashboard-card:hover {
            transform: translateY(-10px);
            border-color: rgba(0, 212, 255, 0.5);
            box-shadow: 0 20px 40px rgba(0, 212, 255, 0.2);
        }
        
        .card-icon {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
        }
        
        .card-title {
            color: white;
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .card-description {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .card-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            color: #00d4ff;
            font-size: 24px;
            font-weight: 700;
        }
        
        .stat-label {
            color: rgba(255, 255, 255, 0.6);
            font-size: 12px;
        }
        
        /* Quick Actions */
        .quick-actions {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 50px;
        }
        
        .actions-title {
            color: white;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 25px;
            text-align: center;
        }
        
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .action-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 15px;
            padding: 20px;
            color: white;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }
        
        .action-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
        }
        
        .action-icon {
            font-size: 24px;
        }
        
        /* Status Bar */
        .status-bar {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #00ff88;
            box-shadow: 0 0 10px #00ff88;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(0, 255, 136, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(0, 255, 136, 0); }
            100% { box-shadow: 0 0 0 0 rgba(0, 255, 136, 0); }
        }
        
        .status-text {
            color: white;
            font-size: 14px;
            font-weight: 500;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .header-content {
                padding: 0 20px;
            }
            
            .main-container {
                padding: 20px 15px;
            }
            
            .welcome-title {
                font-size: 32px;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .actions-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .status-bar {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Animated Background -->
    <div class="bg-animation">
        <div class="floating-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
        </div>
    </div>
    
    <!-- Modern Header -->
    <header class="modern-header">
        <div class="header-content">
            <div class="logo-section">
                <div class="logo-icon">🏦</div>
                <div class="logo-text">EG BANK</div>
            </div>
            
            <div class="user-profile">
                <div class="user-avatar">ع م</div>
                <div class="user-info">
                    <div class="user-name">عبد الفتاح محمود</div>
                    <div class="user-role">مدير النظام</div>
                </div>
            </div>
        </div>
    </header>
    
    <!-- Main Container -->
    <div class="main-container">
        <!-- Welcome Section -->
        <div class="welcome-section">
            <h1 class="welcome-title">نظام الأمان المتقدم</h1>
            <p class="welcome-subtitle">مرحباً بك في لوحة التحكم الذكية لإدارة الأمان والمراقبة</p>
        </div>
        
        <!-- Dashboard Grid -->
        <div class="dashboard-grid">
            <div class="dashboard-card" onclick="openSystem('cameras')">
                <div class="card-icon">📹</div>
                <div class="card-title">نظام المراقبة</div>
                <div class="card-description">مراقبة مباشرة للكاميرات وإدارة أنظمة التسجيل المتقدمة</div>
                <div class="card-stats">
                    <div class="stat-item">
                        <div class="stat-number">24</div>
                        <div class="stat-label">كاميرا</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">18</div>
                        <div class="stat-label">متصل</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">98%</div>
                        <div class="stat-label">جودة</div>
                    </div>
                </div>
            </div>
            
            <div class="dashboard-card" onclick="openSystem('access')">
                <div class="card-icon">🚪</div>
                <div class="card-title">التحكم في الوصول</div>
                <div class="card-description">إدارة الأبواب والصلاحيات مع نظام أمان متطور</div>
                <div class="card-stats">
                    <div class="stat-item">
                        <div class="stat-number">12</div>
                        <div class="stat-label">باب</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">8</div>
                        <div class="stat-label">مفتوح</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">156</div>
                        <div class="stat-label">مستخدم</div>
                    </div>
                </div>
            </div>
            
            <div class="dashboard-card" onclick="openSystem('gps')">
                <div class="card-icon">🏧</div>
                <div class="card-title">تتبع الصرافات</div>
                <div class="card-description">تتبع مباشر لأجهزة الصراف المتنقلة مع GPS متقدم</div>
                <div class="card-stats">
                    <div class="stat-item">
                        <div class="stat-number">8</div>
                        <div class="stat-label">صراف</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">6</div>
                        <div class="stat-label">نشط</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">247</div>
                        <div class="stat-label">كم اليوم</div>
                    </div>
                </div>
            </div>
            
            <div class="dashboard-card" onclick="openSystem('reports')">
                <div class="card-icon">📊</div>
                <div class="card-title">التقارير والتحليلات</div>
                <div class="card-description">تقارير شاملة وتحليلات ذكية للأمان والأداء</div>
                <div class="card-stats">
                    <div class="stat-item">
                        <div class="stat-number">45</div>
                        <div class="stat-label">تقرير</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">12</div>
                        <div class="stat-label">تنبيه</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">99%</div>
                        <div class="stat-label">أمان</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="quick-actions">
            <h2 class="actions-title">الإجراءات السريعة</h2>
            <div class="actions-grid">
                <button class="action-btn" onclick="quickAction('emergency')">
                    <div class="action-icon">🚨</div>
                    <div>إنذار طوارئ</div>
                </button>
                <button class="action-btn" onclick="quickAction('lockdown')">
                    <div class="action-icon">🔒</div>
                    <div>إغلاق شامل</div>
                </button>
                <button class="action-btn" onclick="quickAction('backup')">
                    <div class="action-icon">💾</div>
                    <div>نسخ احتياطي</div>
                </button>
                <button class="action-btn" onclick="quickAction('maintenance')">
                    <div class="action-icon">🔧</div>
                    <div>وضع الصيانة</div>
                </button>
            </div>
        </div>
        
        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-item">
                <div class="status-indicator"></div>
                <div class="status-text">النظام يعمل بشكل طبيعي</div>
            </div>
            <div class="status-item">
                <div class="status-text">آخر تحديث: الآن</div>
            </div>
            <div class="status-item">
                <div class="status-text">الخادم: متصل</div>
            </div>
        </div>
    </div>
    
    <script>
        // System navigation
        function openSystem(system) {
            const routes = {
                cameras: 'cameras.html',
                access: 'access.html',
                gps: 'gps.html',
                reports: 'reports.html'
            };
            
            if (routes[system]) {
                window.location.href = routes[system];
            }
        }
        
        // Quick actions
        function quickAction(action) {
            const actions = {
                emergency: 'تم تفعيل إنذار الطوارئ',
                lockdown: 'تم تفعيل الإغلاق الشامل',
                backup: 'جاري إنشاء نسخة احتياطية...',
                maintenance: 'تم تفعيل وضع الصيانة'
            };
            
            showNotification(actions[action] || 'تم تنفيذ الإجراء');
        }
        
        // Show notification
        function showNotification(message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: linear-gradient(135deg, #00d4ff, #0099cc);
                color: white;
                padding: 15px 25px;
                border-radius: 10px;
                box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
                z-index: 1001;
                font-weight: 500;
                backdrop-filter: blur(20px);
            `;
            
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Modern Security Interface Loaded Successfully');
        });
    </script>
</body>
</html>
