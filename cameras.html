<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - Camera Management</title>
    <link rel="stylesheet" href="style.css">
    <style>
        #loading-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #121212;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .loader {
            border: 16px solid #301934; /* Dark purple */
            border-top: 16px solid #4B0082; /* Darker purple */
            border-radius: 50%;
            width: 120px;
            height: 120px;
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Right Sidebar */
        .right-sidebar {
            position: fixed;
            top: 0;
            right: -350px;
            width: 350px;
            height: 100vh;
            background: linear-gradient(180deg, #1a365d 0%, #2c5282 100%);
            box-shadow: -5px 0 20px rgba(0,0,0,0.3);
            transition: right 0.3s ease;
            z-index: 1000;
            overflow-y: auto;
        }

        .right-sidebar:hover,
        .right-sidebar.show {
            right: 0;
        }

        .sidebar-content {
            padding: 20px;
        }

        .sidebar-section {
            margin-bottom: 30px;
        }

        .section-title {
            color: white;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid rgba(255,255,255,0.2);
        }

        .sidebar-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            margin-bottom: 8px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .sidebar-item:hover {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.3);
            transform: translateX(-5px);
        }

        .item-icon {
            font-size: 20px;
            margin-left: 12px;
            width: 30px;
            text-align: center;
        }

        .item-text {
            color: white;
            font-size: 14px;
            font-weight: 500;
        }

        .user-info {
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            margin-top: 20px;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .user-details {
            flex: 1;
        }

        .user-name {
            color: white;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .user-role {
            color: rgba(255,255,255,0.8);
            font-size: 12px;
        }

        /* Sidebar Trigger Area */
        .sidebar-trigger {
            position: fixed;
            top: 0;
            right: 0;
            width: 20px;
            height: 100vh;
            z-index: 999;
            background: transparent;
        }
    </style>
</head>
<body>
    <div id="loading-container">
        <div class="loader"></div>
    </div>
    <script>
        window.addEventListener('load', function() {
            document.getElementById('loading-container').style.display = 'none';
        });
    </script>
    <!-- Banking Header -->
    <header class="bank-header">
        <div class="bank-logo">EG BANK</div>
        <nav class="bank-nav">
            <ul>
                <li><a href="index.html" style="color: white;">Dashboard</a></li>
                <li><a href="cameras.html" class="active" style="color: white;">Security</a></li>
                <li><a href="access.html" style="color: white;">Access Control</a></li>
                <li><a href="reports.html" style="color: white;">Reports</a></li>
                <li><a href="settings.html" style="color: white;">Settings</a></li>
                <li><a href="gps.html" style="color: white;">GPS Tracking</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main Content Area -->
    <div class="bank-container">
        <!-- Sidebar -->
        <aside class="bank-sidebar">
            <h3 style="color: white;">Abdel Fattah Mahmoud</h3>
            <ul>
                <li><a href="home_simple.html" style="color: white;">الصفحة الرئيسية</a></li>
                <li class="active" style="color: white;">نظام المراقبة</li>
                <li><a href="attendance.html" style="color: white;">الحضور والانصراف</a></li>
                <li><a href="gps.html" style="color: white;">تتبع الصرافات</a></li>
                <li><a href="settings.html" style="color: white;">الإعدادات</a></li>
            </ul>
        </aside>

        <!-- Camera Management Section -->
        <main class="bank-main">
            <h1 style="color: white;">IP Camera Management</h1>

            <div class="form-container">
                <form id="cameraForm">
                    <div class="form-group">
                        <label for="ipNvr" style="color: white;">IP NVR:</label>
                        <input type="text" id="ipNvr" name="ipNvr" required style="color: black; background-color: white;">
                    </div>

                    <div class="form-group">
                        <label for="ipCamera" style="color: white;">IP CAMERA:</label>
                        <input type="text" id="ipCamera" name="ipCamera" required style="color: black; background-color: white;">
                    </div>

                    <div class="form-group">
                        <label for="address" style="color: white;">Address:</label>
                        <input type="text" id="address" name="address" required style="color: black; background-color: white;">
                    </div>

                    <div class="form-group">
                        <label for="port" style="color: white;">Port:</label>
                        <input type="number" id="port" name="port" value="443" required style="color: black; background-color: white;">
                    </div>

                    <div class="form-group">
                        <label for="url" style="color: white;">URL:</label>
                        <input type="url" id="url" name="url" required style="color: black; background-color: white;">
                    </div>

                    <div class="form-actions">
                        <button type="button" id="addBtn" style="color: white;">Add Camera</button>
                        <button type="button" id="editBtn" disabled style="color: white;">Edit</button>
                        <button type="button" id="deleteBtn" disabled style="color: white;">Delete</button>
                        <button type="reset" style="color: white;">Clear</button>
                    </div>
                </form>

                <div class="camera-list">
                    <h2 style="color: white;">Registered Cameras</h2>
                    <table id="cameraTable">
                        <thead>
                            <tr>
                                <th style="color: white;">NVR</th>
                                <th style="color: white;">Camera IP</th>
                                <th style="color: white;">Address</th>
                                <th style="color: white;">Port</th>
                                <th style="color: white;">URL</th>
                                <th style="color: white;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Camera entries will be added here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <!-- Banking Footer -->
    <footer class="bank-footer">
    </footer>

    <!-- Advanced Right Sidebar Toolbar -->
    <div id="rightSidebar" class="right-sidebar">
        <div class="sidebar-content">
            <!-- Control Panel Section -->
            <div class="sidebar-section">
                <div class="section-title">Control Panel</div>
                <div class="sidebar-item" onclick="openControlPanel()">
                    <div class="item-icon">🏠</div>
                    <div class="item-text">لوحة التحكم</div>
                </div>
                <div class="sidebar-item" onclick="openIPCameraConfig()">
                    <div class="item-icon">📹</div>
                    <div class="item-text">IP Camera Configuration</div>
                </div>
                <div class="sidebar-item" onclick="openAccessControl()">
                    <div class="item-icon">🚪</div>
                    <div class="item-text">Access Control</div>
                </div>
                <div class="sidebar-item" onclick="openReports()">
                    <div class="item-icon">📊</div>
                    <div class="item-text">Reports</div>
                </div>
                <div class="sidebar-item" onclick="openSettings()">
                    <div class="item-icon">⚙️</div>
                    <div class="item-text">Settings</div>
                </div>
                <div class="sidebar-item" onclick="openGPS()">
                    <div class="item-icon">📍</div>
                    <div class="item-text">Track Mobile ATMs</div>
                </div>
            </div>

            <!-- User Section -->
            <div class="sidebar-section">
                <div class="section-title">Abdel Fattah Mahmoud</div>
                <div class="sidebar-item" onclick="openAttendanceSystem()">
                    <div class="item-icon">⏰</div>
                    <div class="item-text">نظام الحضور والانصراف</div>
                </div>
                <div class="sidebar-item" onclick="openGPS()">
                    <div class="item-icon">🛰️</div>
                    <div class="item-text">تتبع الصرافات</div>
                </div>

            </div>

            <!-- User Info -->
            <div class="user-info">
                <div class="user-avatar">👤</div>
                <div class="user-details">
                    <div class="user-name">عبد الفتاح محمود</div>
                    <div class="user-role">مدير النظام</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar Trigger Area -->
    <div class="sidebar-trigger" onmouseenter="showSidebar()" onmouseleave="hideSidebar()"></div>

    <!-- Floating Toolbar -->
    <div class="floating-toolbar" style="display: none;">
        <div class="toolbar-header">
            لوحة التحكم الرئيسية
        </div>

        <div class="toolbar-section">
            <div class="toolbar-section-title">Control Panel</div>
            <a href="index.html" class="toolbar-item">
                <div class="toolbar-item-icon">🏠</div>
                <div class="toolbar-item-text">لوحة التحكم</div>
            </a>
            <a href="cameras.html" class="toolbar-item active">
                <div class="toolbar-item-icon">🔒</div>
                <div class="toolbar-item-text">الأمان</div>
            </a>
            <a href="access.html" class="toolbar-item">
                <div class="toolbar-item-icon">🚪</div>
                <div class="toolbar-item-text">التحكم في الوصول</div>
            </a>
            <a href="reports.html" class="toolbar-item">
                <div class="toolbar-item-icon">📊</div>
                <div class="toolbar-item-text">التقارير</div>
            </a>
            <a href="settings.html" class="toolbar-item">
                <div class="toolbar-item-icon">⚙️</div>
                <div class="toolbar-item-text">الإعدادات</div>
            </a>
            <a href="gps.html" class="toolbar-item">
                <div class="toolbar-item-icon">📍</div>
                <div class="toolbar-item-text">تتبع GPS</div>
            </a>
        </div>

        <div class="toolbar-section">
            <div class="toolbar-section-title">Abdel Fattah Mahmoud</div>
            <a href="home_simple.html" class="toolbar-item">
                <div class="toolbar-item-icon">🏠</div>
                <div class="toolbar-item-text">الصفحة الرئيسية</div>
            </a>
            <a href="cameras.html" class="toolbar-item active">
                <div class="toolbar-item-icon">📹</div>
                <div class="toolbar-item-text">نظام المراقبة</div>
            </a>
            <a href="attendance.html" class="toolbar-item">
                <div class="toolbar-item-icon">⏰</div>
                <div class="toolbar-item-text">الحضور والانصراف</div>
            </a>
            <a href="gps.html" class="toolbar-item">
                <div class="toolbar-item-icon">🛰️</div>
                <div class="toolbar-item-text">تتبع الصرافات</div>
            </a>

        </div>

        <div class="toolbar-user">
            <div class="toolbar-user-name">عبد الفتاح محمود</div>
            <div class="toolbar-user-role">مدير النظام</div>
        </div>
    </div>

    <script src="cameras.js"></script>

    <script>
        // Right Sidebar Control
        let sidebarTimeout;

        function showSidebar() {
            clearTimeout(sidebarTimeout);
            document.getElementById('rightSidebar').classList.add('show');
        }

        function hideSidebar() {
            sidebarTimeout = setTimeout(() => {
                document.getElementById('rightSidebar').classList.remove('show');
            }, 300);
        }

        // Add mouse events to sidebar itself
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = document.getElementById('rightSidebar');
            sidebar.addEventListener('mouseenter', showSidebar);
            sidebar.addEventListener('mouseleave', hideSidebar);
        });

        // Navigation Functions
        function openControlPanel() {
            window.location.href = 'index.html';
        }

        function openIPCameraConfig() {
            window.location.href = 'ip_camera_config.html';
        }

        function openAccessControl() {
            window.location.href = 'access.html';
        }

        function openReports() {
            window.location.href = 'reports.html';
        }

        function openSettings() {
            window.location.href = 'settings.html';
        }

        function openGPS() {
            window.location.href = 'gps.html';
        }

        function openCameraMonitoring() {
            window.location.href = 'cameras.html';
        }

        function openIPCameraManagement() {
            window.location.href = 'ip_management.html';
        }

        function openAccessControlUser() {
            window.location.href = 'access_control_user.html';
        }

        function openReportsUser() {
            window.location.href = 'reports_user.html';
        }

        function openAttendanceSystem() {
            window.location.href = 'attendance.html';
        }

        console.log('Right Sidebar System Loaded Successfully');
    </script>
</body>
</html>