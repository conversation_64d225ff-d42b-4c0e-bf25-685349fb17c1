// Access Logs Management JavaScript
// إدارة سجلات الوصول

// Sample access logs data
let accessLogs = [
    {
        id: 1,
        timestamp: '2024-01-15 15:45:30',
        user: 'عبد الفتاح محمود',
        door: 'الباب الرئيسي',
        doorId: 'door_1',
        action: 'فتح الباب',
        status: 'success',
        ipAddress: '*************'
    },
    {
        id: 2,
        timestamp: '2024-01-15 15:42:15',
        user: 'أحمد محمد علي',
        door: 'باب المكاتب',
        doorId: 'door_3',
        action: 'فتح الباب',
        status: 'success',
        ipAddress: '*************'
    },
    {
        id: 3,
        timestamp: '2024-01-15 15:38:22',
        user: 'غير معروف',
        door: 'باب الخزينة',
        doorId: 'door_2',
        action: 'محاولة دخول غير مصرح',
        status: 'failed',
        ipAddress: '*************'
    },
    {
        id: 4,
        timestamp: '2024-01-15 15:35:10',
        user: 'سارة أحمد حسن',
        door: 'باب الاجتماعات',
        doorId: 'door_6',
        action: 'فتح الباب',
        status: 'success',
        ipAddress: '*************'
    },
    {
        id: 5,
        timestamp: '2024-01-15 15:30:45',
        user: 'محمد علي حسن',
        door: 'باب الخدمات',
        doorId: 'door_7',
        action: 'فتح الباب',
        status: 'success',
        ipAddress: '*************'
    },
    {
        id: 6,
        timestamp: '2024-01-15 15:25:33',
        user: 'غير معروف',
        door: 'الباب الرئيسي',
        doorId: 'door_1',
        action: 'محاولة دخول متكررة',
        status: 'failed',
        ipAddress: '*************'
    },
    {
        id: 7,
        timestamp: '2024-01-15 15:20:18',
        user: 'فاطمة محمود',
        door: 'باب الأرشيف',
        doorId: 'door_5',
        action: 'فتح الباب',
        status: 'success',
        ipAddress: '*************'
    },
    {
        id: 8,
        timestamp: '2024-01-15 15:15:55',
        user: 'خالد أحمد',
        door: 'باب الحراسة',
        doorId: 'door_8',
        action: 'فتح الباب',
        status: 'success',
        ipAddress: '*************'
    }
];

// Filtered logs for display
let filteredLogs = [...accessLogs];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    renderLogs();
    updateStatistics();
    
    // Generate more sample data
    generateSampleLogs();
});

// Initialize application
function initializeApp() {
    console.log('Access Logs Management System Initialized');
    console.log('Total Logs:', accessLogs.length);
}

// Generate sample logs for demonstration
function generateSampleLogs() {
    const users = ['عبد الفتاح محمود', 'أحمد محمد علي', 'سارة أحمد حسن', 'محمد علي حسن', 'فاطمة محمود', 'خالد أحمد'];
    const doors = ['الباب الرئيسي', 'باب الخزينة', 'باب المكاتب', 'باب الطوارئ', 'باب الأرشيف', 'باب الاجتماعات'];
    const actions = ['فتح الباب', 'إغلاق الباب', 'محاولة دخول'];
    
    for (let i = 0; i < 50; i++) {
        const date = new Date();
        date.setHours(date.getHours() - Math.floor(Math.random() * 24));
        date.setMinutes(date.getMinutes() - Math.floor(Math.random() * 60));
        
        const log = {
            id: accessLogs.length + 1,
            timestamp: date.toLocaleString('sv-SE').replace('T', ' ').substring(0, 19),
            user: Math.random() > 0.1 ? users[Math.floor(Math.random() * users.length)] : 'غير معروف',
            door: doors[Math.floor(Math.random() * doors.length)],
            doorId: `door_${Math.floor(Math.random() * 8) + 1}`,
            action: actions[Math.floor(Math.random() * actions.length)],
            status: Math.random() > 0.15 ? 'success' : 'failed',
            ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`
        };
        
        accessLogs.push(log);
    }
    
    filteredLogs = [...accessLogs];
    updateStatistics();
}

// Render logs table
function renderLogs() {
    const tableBody = document.getElementById('logsTableBody');
    tableBody.innerHTML = '';
    
    if (filteredLogs.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="6" style="text-align: center; color: #a0a0a0; padding: 40px;">
                    لا توجد سجلات تطابق معايير البحث
                </td>
            </tr>
        `;
        return;
    }
    
    // Sort logs by timestamp (newest first)
    const sortedLogs = filteredLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    
    sortedLogs.forEach(log => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${log.timestamp}</td>
            <td>${log.user}</td>
            <td>${log.door}</td>
            <td>${log.action}</td>
            <td><span class="status-${log.status}">${log.status === 'success' ? 'ناجح' : 'فاشل'}</span></td>
            <td>${log.ipAddress}</td>
        `;
        tableBody.appendChild(row);
    });
}

// Search logs
function searchLogs() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
    
    if (searchTerm === '') {
        filteredLogs = [...accessLogs];
    } else {
        filteredLogs = accessLogs.filter(log => 
            log.user.toLowerCase().includes(searchTerm) ||
            log.door.toLowerCase().includes(searchTerm) ||
            log.action.toLowerCase().includes(searchTerm) ||
            log.ipAddress.includes(searchTerm)
        );
    }
    
    renderLogs();
    updateStatistics();
}

// Filter logs
function filterLogs() {
    const statusFilter = document.getElementById('statusFilter').value;
    const doorFilter = document.getElementById('doorFilter').value;
    const dateFilter = document.getElementById('dateFilter').value;
    
    let filtered = [...accessLogs];
    
    // Apply status filter
    if (statusFilter !== 'all') {
        filtered = filtered.filter(log => log.status === statusFilter);
    }
    
    // Apply door filter
    if (doorFilter !== 'all') {
        filtered = filtered.filter(log => log.doorId === doorFilter);
    }
    
    // Apply date filter (simplified)
    const today = new Date().toISOString().split('T')[0];
    if (dateFilter === 'today') {
        filtered = filtered.filter(log => log.timestamp.startsWith(today));
    }
    
    filteredLogs = filtered;
    renderLogs();
    updateStatistics();
}

// Update statistics
function updateStatistics() {
    const stats = {
        total: filteredLogs.length,
        successful: filteredLogs.filter(log => log.status === 'success').length,
        failed: filteredLogs.filter(log => log.status === 'failed').length,
        today: filteredLogs.filter(log => {
            const today = new Date().toISOString().split('T')[0];
            return log.timestamp.startsWith(today);
        }).length
    };
    
    document.getElementById('totalLogs').textContent = stats.total;
    document.getElementById('successfulAccess').textContent = stats.successful;
    document.getElementById('failedAccess').textContent = stats.failed;
    document.getElementById('todayLogs').textContent = stats.today;
}

// Export logs
function exportLogs() {
    const csvContent = "data:text/csv;charset=utf-8," 
        + "التاريخ والوقت,المستخدم,الباب,العملية,الحالة,عنوان IP\n"
        + filteredLogs.map(log => 
            `${log.timestamp},${log.user},${log.door},${log.action},${log.status === 'success' ? 'ناجح' : 'فاشل'},${log.ipAddress}`
        ).join("\n");
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `access_logs_${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    showNotification('تم تصدير السجلات بنجاح');
}

// Show notification
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    const bgColor = type === 'success' ? '#48bb78' : '#f56565';
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: ${bgColor};
        color: white;
        padding: 15px 25px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 1001;
        font-weight: 500;
        animation: slideDown 0.3s ease;
    `;
    
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideUp 0.3s ease';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideDown {
        from { opacity: 0; transform: translateX(-50%) translateY(-20px); }
        to { opacity: 1; transform: translateX(-50%) translateY(0); }
    }
    
    @keyframes slideUp {
        from { opacity: 1; transform: translateX(-50%) translateY(0); }
        to { opacity: 0; transform: translateX(-50%) translateY(-20px); }
    }
`;
document.head.appendChild(style);

console.log('Access Logs Management JavaScript Loaded Successfully');
