// EG BANK Advanced Attendance System with Database and Voice
// نظام الحضور والانصراف المتقدم مع قاعدة البيانات والتسجيل الصوتي

class AdvancedAttendanceSystem {
    constructor() {
        this.employees = [];
        this.attendanceRecords = [];
        this.selectedEmployeeId = null;
        this.speechSynthesis = window.speechSynthesis;
        this.voices = [];
        this.currentEditingEmployee = null;
        this.init();
    }

    // Initialize the system
    async init() {
        console.log('🚀 Initializing Advanced Attendance System...');
        
        await this.initializeVoices();
        this.initializeDatabase();
        this.loadEmployees();
        this.loadAttendanceRecords();
        this.updateDisplay();
        this.startClock();
        this.setupEventListeners();
        
        console.log('✅ Advanced Attendance System initialized successfully');
    }

    // Initialize voice synthesis
    async initializeVoices() {
        return new Promise((resolve) => {
            const loadVoices = () => {
                this.voices = this.speechSynthesis.getVoices();
                console.log('🔊 Available voices:', this.voices.length);
                resolve();
            };

            if (this.voices.length === 0) {
                this.speechSynthesis.addEventListener('voiceschanged', loadVoices);
                setTimeout(loadVoices, 100); // Fallback
            } else {
                loadVoices();
            }
        });
    }

    // Initialize database with sample data
    initializeDatabase() {
        // Initialize employees if not exists
        if (!localStorage.getItem('attendanceEmployees')) {
            const sampleEmployees = [
                {
                    id: 'EMP001',
                    name: 'عبد الفتاح محمود',
                    employeeId: 'EMP001',
                    department: 'إدارة',
                    position: 'مدير عام',
                    phone: '01234567890',
                    email: '<EMAIL>',
                    status: 'active',
                    createdAt: new Date().toISOString(),
                    lastAttendance: null
                },
                {
                    id: 'EMP002',
                    name: 'أحمد محمد علي',
                    employeeId: 'EMP002',
                    department: 'محاسبة',
                    position: 'محاسب أول',
                    phone: '01234567891',
                    email: '<EMAIL>',
                    status: 'active',
                    createdAt: new Date().toISOString(),
                    lastAttendance: null
                },
                {
                    id: 'EMP003',
                    name: 'فاطمة أحمد',
                    employeeId: 'EMP003',
                    department: 'خدمة عملاء',
                    position: 'موظفة خدمة عملاء',
                    phone: '01234567892',
                    email: '<EMAIL>',
                    status: 'active',
                    createdAt: new Date().toISOString(),
                    lastAttendance: null
                }
            ];
            
            this.saveEmployees(sampleEmployees);
            console.log('📝 Sample employees created');
        }

        // Initialize attendance records if not exists
        if (!localStorage.getItem('attendanceRecordsAdvanced')) {
            localStorage.setItem('attendanceRecordsAdvanced', JSON.stringify([]));
            console.log('📊 Attendance records database initialized');
        }
    }

    // Load employees from database
    loadEmployees() {
        try {
            this.employees = JSON.parse(localStorage.getItem('attendanceEmployees') || '[]');
            this.updateEmployeeSelect();
            this.updateEmployeesTable();
            console.log(`👥 Loaded ${this.employees.length} employees`);
        } catch (error) {
            console.error('Error loading employees:', error);
            this.employees = [];
        }
    }

    // Save employees to database
    saveEmployees(employees = this.employees) {
        try {
            localStorage.setItem('attendanceEmployees', JSON.stringify(employees));
            console.log('💾 Employees saved to database');
        } catch (error) {
            console.error('Error saving employees:', error);
        }
    }

    // Load attendance records from database
    loadAttendanceRecords() {
        try {
            this.attendanceRecords = JSON.parse(localStorage.getItem('attendanceRecordsAdvanced') || '[]');
            this.updateAttendanceLog();
            console.log(`📊 Loaded ${this.attendanceRecords.length} attendance records`);
        } catch (error) {
            console.error('Error loading attendance records:', error);
            this.attendanceRecords = [];
        }
    }

    // Save attendance records to database
    saveAttendanceRecords() {
        try {
            localStorage.setItem('attendanceRecordsAdvanced', JSON.stringify(this.attendanceRecords));
            console.log('💾 Attendance records saved to database');
        } catch (error) {
            console.error('Error saving attendance records:', error);
        }
    }

    // Start the clock
    startClock() {
        this.updateClock();
        setInterval(() => this.updateClock(), 1000);
    }

    // Update clock display
    updateClock() {
        const now = new Date();
        
        const timeString = now.toLocaleTimeString('ar-EG', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        
        const dateString = now.toLocaleDateString('ar-EG', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
        
        document.getElementById('currentTime').textContent = timeString;
        document.getElementById('currentDate').textContent = dateString;
    }

    // Setup event listeners
    setupEventListeners() {
        // Employee selection
        document.getElementById('employeeSelect').addEventListener('change', (e) => {
            this.selectedEmployeeId = e.target.value;
            this.updateSelectedEmployee();
        });

        // Employee form submission
        document.getElementById('employeeForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveEmployee();
        });
    }

    // Update employee select dropdown
    updateEmployeeSelect() {
        const select = document.getElementById('employeeSelect');
        select.innerHTML = '<option value="">اختر الموظف</option>';
        
        this.employees.forEach(employee => {
            if (employee.status === 'active') {
                const option = document.createElement('option');
                option.value = employee.id;
                option.textContent = `${employee.name} (${employee.employeeId})`;
                select.appendChild(option);
            }
        });
    }

    // Update selected employee display
    updateSelectedEmployee() {
        const selectedElement = document.getElementById('selectedEmployee');
        
        if (this.selectedEmployeeId) {
            const employee = this.employees.find(emp => emp.id === this.selectedEmployeeId);
            if (employee) {
                selectedElement.textContent = employee.name;
                this.updateAttendanceButton(employee);
            }
        } else {
            selectedElement.textContent = 'لم يتم الاختيار';
            this.resetAttendanceButton();
        }
    }

    // Update attendance button based on employee status
    updateAttendanceButton(employee) {
        const btn = document.getElementById('attendanceBtn');
        const btnIcon = document.getElementById('btnIcon');
        const btnText = document.getElementById('btnText');
        
        const today = new Date().toDateString();
        const todayRecord = this.attendanceRecords.find(record => 
            record.employeeId === employee.id && 
            new Date(record.date).toDateString() === today
        );

        if (todayRecord && todayRecord.status === 'checked_in') {
            // Employee is checked in, show checkout button
            btn.className = 'attendance-btn btn-checkout';
            btnIcon.textContent = '🔴';
            btnText.textContent = 'تسجيل انصراف';
        } else {
            // Employee is not checked in, show checkin button
            btn.className = 'attendance-btn btn-checkin';
            btnIcon.textContent = '🟢';
            btnText.textContent = 'تسجيل حضور';
        }
        
        btn.disabled = false;
    }

    // Reset attendance button
    resetAttendanceButton() {
        const btn = document.getElementById('attendanceBtn');
        const btnIcon = document.getElementById('btnIcon');
        const btnText = document.getElementById('btnText');
        
        btn.className = 'attendance-btn btn-disabled';
        btnIcon.textContent = '⚪';
        btnText.textContent = 'اختر موظف';
        btn.disabled = true;
    }

    // Toggle attendance (check in/out)
    async toggleAttendance() {
        if (!this.selectedEmployeeId) {
            this.showNotification('يرجى اختيار موظف أولاً', 'error');
            return;
        }

        const employee = this.employees.find(emp => emp.id === this.selectedEmployeeId);
        if (!employee) {
            this.showNotification('الموظف غير موجود', 'error');
            return;
        }

        const now = new Date();
        const today = now.toDateString();
        
        // Find today's record
        const todayRecordIndex = this.attendanceRecords.findIndex(record => 
            record.employeeId === employee.id && 
            new Date(record.date).toDateString() === today
        );

        let action = '';
        let voiceMessage = '';

        if (todayRecordIndex !== -1 && this.attendanceRecords[todayRecordIndex].status === 'checked_in') {
            // Check out
            this.attendanceRecords[todayRecordIndex].checkoutTime = now.toISOString();
            this.attendanceRecords[todayRecordIndex].status = 'checked_out';
            
            // Calculate working hours
            const checkinTime = new Date(this.attendanceRecords[todayRecordIndex].checkinTime);
            const workingMs = now.getTime() - checkinTime.getTime();
            this.attendanceRecords[todayRecordIndex].workingHours = Math.round(workingMs / (1000 * 60 * 60) * 100) / 100;
            
            action = 'checkout';
            voiceMessage = `تم تسجيل انصراف ${employee.name} بنجاح`;
            
        } else {
            // Check in
            const newRecord = {
                id: 'ATT_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                employeeId: employee.id,
                employeeName: employee.name,
                employeeNumber: employee.employeeId,
                department: employee.department,
                date: today,
                checkinTime: now.toISOString(),
                checkoutTime: null,
                status: 'checked_in',
                workingHours: 0,
                notes: '',
                createdAt: now.toISOString()
            };
            
            if (todayRecordIndex !== -1) {
                this.attendanceRecords[todayRecordIndex] = newRecord;
            } else {
                this.attendanceRecords.push(newRecord);
            }
            
            action = 'checkin';
            voiceMessage = `تم تسجيل حضور ${employee.name} بنجاح`;
        }

        // Update employee's last attendance
        employee.lastAttendance = now.toISOString();
        
        // Save to database
        this.saveAttendanceRecords();
        this.saveEmployees();
        
        // Update displays
        this.updateAttendanceButton(employee);
        this.updateEmployeesTable();
        this.updateAttendanceLog();
        
        // Play voice notification
        await this.playVoiceNotification(voiceMessage);
        
        // Show visual notification
        this.showNotification(voiceMessage, 'success');
        
        // Log action
        if (window.egbank) {
            window.egbank.logAction(`attendance_${action}`, voiceMessage, {
                employeeId: employee.id,
                employeeName: employee.name,
                time: now.toISOString()
            });
        }
        
        console.log(`✅ ${action} recorded for ${employee.name}`);
    }

    // Play voice notification
    async playVoiceNotification(message) {
        try {
            // Show voice indicator
            this.showVoiceIndicator(message);
            
            if (this.speechSynthesis && 'speechSynthesis' in window) {
                // Cancel any ongoing speech
                this.speechSynthesis.cancel();
                
                const utterance = new SpeechSynthesisUtterance(message);
                
                // Try to find Arabic voice, fallback to default
                const arabicVoice = this.voices.find(voice => 
                    voice.lang.includes('ar') || voice.name.includes('Arabic')
                );
                
                if (arabicVoice) {
                    utterance.voice = arabicVoice;
                    utterance.lang = 'ar-SA';
                } else {
                    utterance.lang = 'ar-SA';
                }
                
                utterance.rate = 0.9;
                utterance.pitch = 1.0;
                utterance.volume = 0.8;
                
                utterance.onstart = () => {
                    console.log('🔊 Voice notification started');
                };
                
                utterance.onend = () => {
                    console.log('🔊 Voice notification completed');
                };
                
                utterance.onerror = (event) => {
                    console.error('🔊 Voice notification error:', event.error);
                };
                
                this.speechSynthesis.speak(utterance);
            } else {
                console.warn('🔊 Speech synthesis not supported');
            }
        } catch (error) {
            console.error('Error playing voice notification:', error);
        }
    }

    // Show voice indicator
    showVoiceIndicator(message) {
        const indicator = document.getElementById('voiceIndicator');
        const messageElement = document.getElementById('voiceMessage');
        
        messageElement.textContent = `🔊 ${message}`;
        indicator.classList.add('show');
        
        setTimeout(() => {
            indicator.classList.remove('show');
        }, 3000);
    }

    // Update employees table
    updateEmployeesTable() {
        const tbody = document.getElementById('employeesTableBody');
        tbody.innerHTML = '';
        
        this.employees.forEach(employee => {
            const row = tbody.insertRow();
            
            // Get today's attendance status
            const today = new Date().toDateString();
            const todayRecord = this.attendanceRecords.find(record => 
                record.employeeId === employee.id && 
                new Date(record.date).toDateString() === today
            );
            
            let status = 'غائب';
            let statusClass = 'status-absent';
            
            if (todayRecord) {
                if (todayRecord.status === 'checked_in') {
                    status = 'حاضر';
                    statusClass = 'status-present';
                } else if (todayRecord.status === 'checked_out') {
                    status = 'منصرف';
                    statusClass = 'status-present';
                }
            }
            
            const lastAttendance = employee.lastAttendance ? 
                new Date(employee.lastAttendance).toLocaleString('ar-EG') : 'لا يوجد';
            
            row.innerHTML = `
                <td>${employee.name}</td>
                <td>${employee.employeeId}</td>
                <td>${employee.department}</td>
                <td class="${statusClass}">${status}</td>
                <td>${lastAttendance}</td>
                <td>
                    <button class="action-btn edit-btn" onclick="attendanceSystem.editEmployee('${employee.id}')">
                        ✏️ تعديل
                    </button>
                    <button class="action-btn delete-btn" onclick="attendanceSystem.deleteEmployee('${employee.id}')">
                        🗑️ حذف
                    </button>
                </td>
            `;
        });
    }

    // Update attendance log
    updateAttendanceLog() {
        const logContainer = document.getElementById('attendanceLog');
        const today = new Date().toDateString();
        
        const todayRecords = this.attendanceRecords
            .filter(record => new Date(record.date).toDateString() === today)
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        
        if (todayRecords.length === 0) {
            logContainer.innerHTML = '<p style="text-align: center; color: rgba(255,255,255,0.5); padding: 20px;">لا توجد سجلات حضور اليوم</p>';
            return;
        }
        
        logContainer.innerHTML = '';
        
        todayRecords.forEach(record => {
            const logItem = document.createElement('div');
            logItem.className = 'log-item';
            
            const actionText = record.status === 'checked_in' ? 'تسجيل حضور' : 'تسجيل انصراف';
            const actionTime = record.status === 'checked_in' ? 
                record.checkinTime : record.checkoutTime;
            
            logItem.innerHTML = `
                <div>
                    <div class="log-employee">${record.employeeName}</div>
                    <div class="log-action">${actionText}</div>
                </div>
                <div class="log-time">${new Date(actionTime).toLocaleString('ar-EG')}</div>
            `;
            
            logContainer.appendChild(logItem);
        });
    }

    // Update display
    updateDisplay() {
        this.updateEmployeeSelect();
        this.updateEmployeesTable();
        this.updateAttendanceLog();
        this.resetAttendanceButton();
    }

    // Show notification
    showNotification(message, type = 'info') {
        if (window.egbank) {
            window.egbank.showNotification(message, type);
        } else {
            alert(message);
        }
    }

    // Open add employee modal
    openAddEmployeeModal() {
        this.currentEditingEmployee = null;
        document.getElementById('modalTitle').textContent = 'إضافة موظف جديد';
        document.getElementById('employeeForm').reset();
        document.getElementById('employeeModal').style.display = 'block';
    }

    // Close employee modal
    closeEmployeeModal() {
        document.getElementById('employeeModal').style.display = 'none';
        this.currentEditingEmployee = null;
    }

    // Edit employee
    editEmployee(employeeId) {
        const employee = this.employees.find(emp => emp.id === employeeId);
        if (!employee) return;
        
        this.currentEditingEmployee = employee;
        document.getElementById('modalTitle').textContent = 'تعديل بيانات الموظف';
        
        // Fill form with employee data
        document.getElementById('employeeName').value = employee.name;
        document.getElementById('employeeId').value = employee.employeeId;
        document.getElementById('employeeDepartment').value = employee.department;
        document.getElementById('employeePosition').value = employee.position || '';
        document.getElementById('employeePhone').value = employee.phone || '';
        document.getElementById('employeeEmail').value = employee.email || '';
        
        document.getElementById('employeeModal').style.display = 'block';
    }

    // Delete employee
    async deleteEmployee(employeeId) {
        const employee = this.employees.find(emp => emp.id === employeeId);
        if (!employee) return;
        
        if (confirm(`هل أنت متأكد من حذف الموظف: ${employee.name}؟`)) {
            // Remove employee
            this.employees = this.employees.filter(emp => emp.id !== employeeId);
            
            // Remove employee's attendance records
            this.attendanceRecords = this.attendanceRecords.filter(record => record.employeeId !== employeeId);
            
            // Save to database
            this.saveEmployees();
            this.saveAttendanceRecords();
            
            // Update displays
            this.updateDisplay();
            
            this.showNotification(`تم حذف الموظف ${employee.name} بنجاح`, 'success');
            
            // Log action
            if (window.egbank) {
                window.egbank.logAction('employee_deleted', `حذف الموظف: ${employee.name}`, {
                    employeeId: employee.id,
                    employeeName: employee.name
                });
            }
        }
    }

    // Save employee (add or edit)
    saveEmployee() {
        const name = document.getElementById('employeeName').value.trim();
        const employeeId = document.getElementById('employeeId').value.trim();
        const department = document.getElementById('employeeDepartment').value;
        const position = document.getElementById('employeePosition').value.trim();
        const phone = document.getElementById('employeePhone').value.trim();
        const email = document.getElementById('employeeEmail').value.trim();
        
        if (!name || !employeeId || !department) {
            this.showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
            return;
        }
        
        // Check if employee ID already exists (for new employees)
        if (!this.currentEditingEmployee) {
            const existingEmployee = this.employees.find(emp => emp.employeeId === employeeId);
            if (existingEmployee) {
                this.showNotification('الرقم الوظيفي موجود بالفعل', 'error');
                return;
            }
        }
        
        if (this.currentEditingEmployee) {
            // Edit existing employee
            this.currentEditingEmployee.name = name;
            this.currentEditingEmployee.employeeId = employeeId;
            this.currentEditingEmployee.department = department;
            this.currentEditingEmployee.position = position;
            this.currentEditingEmployee.phone = phone;
            this.currentEditingEmployee.email = email;
            this.currentEditingEmployee.updatedAt = new Date().toISOString();
            
            this.showNotification(`تم تحديث بيانات الموظف ${name} بنجاح`, 'success');
            
            // Log action
            if (window.egbank) {
                window.egbank.logAction('employee_updated', `تحديث بيانات الموظف: ${name}`, {
                    employeeId: this.currentEditingEmployee.id,
                    employeeName: name
                });
            }
        } else {
            // Add new employee
            const newEmployee = {
                id: 'EMP_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                name,
                employeeId,
                department,
                position,
                phone,
                email,
                status: 'active',
                createdAt: new Date().toISOString(),
                lastAttendance: null
            };
            
            this.employees.push(newEmployee);
            this.showNotification(`تم إضافة الموظف ${name} بنجاح`, 'success');
            
            // Log action
            if (window.egbank) {
                window.egbank.logAction('employee_added', `إضافة موظف جديد: ${name}`, {
                    employeeId: newEmployee.id,
                    employeeName: name
                });
            }
        }
        
        // Save to database
        this.saveEmployees();
        
        // Update displays
        this.updateDisplay();
        
        // Close modal
        this.closeEmployeeModal();
    }
}

// Global functions
function toggleAttendance() {
    if (window.attendanceSystem) {
        window.attendanceSystem.toggleAttendance();
    }
}

function openAddEmployeeModal() {
    if (window.attendanceSystem) {
        window.attendanceSystem.openAddEmployeeModal();
    }
}

function closeEmployeeModal() {
    if (window.attendanceSystem) {
        window.attendanceSystem.closeEmployeeModal();
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Advanced Attendance System loading...');
    
    // Wait for EG BANK system if available
    if (window.EGBankApp) {
        document.addEventListener('egbankReady', function() {
            window.attendanceSystem = new AdvancedAttendanceSystem();
        });
    } else {
        // Initialize directly if EG BANK system not available
        window.attendanceSystem = new AdvancedAttendanceSystem();
    }
});

// Close modal when clicking outside
window.addEventListener('click', function(event) {
    const modal = document.getElementById('employeeModal');
    if (event.target === modal) {
        closeEmployeeModal();
    }
});

console.log('🏦 Advanced Attendance System Script Loaded');

// Export data to CSV
function exportToCSV() {
    if (window.attendanceSystem) {
        const records = window.attendanceSystem.attendanceRecords;
        const employees = window.attendanceSystem.employees;

        // Create CSV content
        const csvHeaders = ['التاريخ', 'الموظف', 'الرقم الوظيفي', 'القسم', 'وقت الحضور', 'وقت الانصراف', 'ساعات العمل', 'الحالة'];
        const csvRows = records.map(record => [
            new Date(record.date).toLocaleDateString('ar-EG'),
            record.employeeName,
            record.employeeNumber,
            record.department,
            record.checkinTime ? new Date(record.checkinTime).toLocaleTimeString('ar-EG') : '--:--',
            record.checkoutTime ? new Date(record.checkoutTime).toLocaleTimeString('ar-EG') : '--:--',
            record.workingHours ? record.workingHours.toFixed(1) : '0',
            record.status === 'checked_in' ? 'حاضر' : (record.checkoutTime ? 'منصرف' : 'غائب')
        ]);

        const csvContent = [csvHeaders, ...csvRows]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');

        // Download CSV
        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `attendance_data_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();

        if (window.egbank) {
            window.egbank.showNotification('تم تصدير البيانات بنجاح', 'success');
        }
    }
}

// Backup database
function backupDatabase() {
    if (window.attendanceSystem) {
        const data = {
            employees: window.attendanceSystem.employees,
            attendanceRecords: window.attendanceSystem.attendanceRecords,
            exportDate: new Date().toISOString(),
            version: '1.0'
        };

        const jsonContent = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonContent], { type: 'application/json' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `egbank_attendance_backup_${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        if (window.egbank) {
            window.egbank.showNotification('تم إنشاء نسخة احتياطية بنجاح', 'success');
        }
    }
}
