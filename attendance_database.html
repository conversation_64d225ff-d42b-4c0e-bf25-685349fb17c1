<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - قاعدة بيانات الحضور والانصراف</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(26, 54, 93, 0.9);
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
        }
        
        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(45deg, #1a365d, #2c5282);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .logo-text {
            font-size: 28px;
            font-weight: 900;
            color: white;
        }
        
        .nav-buttons {
            display: flex;
            gap: 15px;
        }
        
        .nav-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
        }
        
        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .page-title {
            text-align: center;
            font-size: 36px;
            font-weight: 900;
            margin-bottom: 40px;
            color: #3182ce;
            text-shadow: 0 0 20px rgba(49, 130, 206, 0.5);
        }
        
        .database-controls {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(20px);
        }
        
        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .control-btn {
            background: linear-gradient(135deg, #1a365d, #2c5282);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            font-size: 14px;
        }
        
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(26, 54, 93, 0.4);
        }
        
        .export-btn {
            background: linear-gradient(135deg, #48bb78, #38a169);
        }
        
        .backup-btn {
            background: linear-gradient(135deg, #ed8936, #dd6b20);
        }
        
        .clear-btn {
            background: linear-gradient(135deg, #f56565, #e53e3e);
        }
        
        .database-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            backdrop-filter: blur(20px);
        }
        
        .stat-icon {
            font-size: 32px;
            margin-bottom: 10px;
            display: block;
        }
        
        .stat-value {
            font-size: 28px;
            font-weight: 900;
            color: #3182ce;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .database-tables {
            display: grid;
            grid-template-columns: 1fr;
            gap: 30px;
        }
        
        .table-panel {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(20px);
        }
        
        .panel-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 20px;
            color: white;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .data-table th {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-weight: 600;
        }
        
        .data-table td {
            color: rgba(255, 255, 255, 0.9);
        }
        
        .status-present {
            color: #48bb78;
            font-weight: 600;
        }
        
        .status-absent {
            color: #f56565;
            font-weight: 600;
        }
        
        .status-active {
            color: #48bb78;
            font-weight: 600;
        }
        
        .status-inactive {
            color: #f56565;
            font-weight: 600;
        }
        
        .search-box {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 12px;
            color: white;
            font-size: 14px;
            margin-bottom: 20px;
        }
        
        .search-box:focus {
            outline: none;
            border-color: #3182ce;
            box-shadow: 0 0 0 2px rgba(49, 130, 206, 0.2);
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
        }
        
        .page-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .page-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .page-btn.active {
            background: #3182ce;
            border-color: #3182ce;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }
        
        .modal-content {
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            margin: 5% auto;
            padding: 30px;
            border-radius: 20px;
            width: 90%;
            max-width: 600px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .modal-title {
            font-size: 24px;
            font-weight: 700;
            color: #3182ce;
        }
        
        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
        }
        
        @media (max-width: 768px) {
            .controls-grid {
                grid-template-columns: 1fr;
            }
            
            .database-stats {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .data-table {
                font-size: 12px;
            }
            
            .data-table th,
            .data-table td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">🏦</div>
                <div class="logo-text">EG BANK</div>
            </div>
            
            <div class="nav-buttons">
                <a href="attendance_advanced.html" class="nav-btn">⏰ الحضور والانصراف</a>
                <a href="attendance_reports.html" class="nav-btn">📊 التقارير</a>
                <a href="home_simple.html" class="nav-btn">🏠 الرئيسية</a>
            </div>
        </div>
    </header>
    
    <div class="container">
        <h1 class="page-title">🗄️ قاعدة بيانات الحضور والانصراف</h1>
        
        <!-- Database Controls -->
        <div class="database-controls">
            <div class="controls-grid">
                <button class="control-btn export-btn" onclick="exportToCSV()">
                    📤 تصدير البيانات
                </button>
                <button class="control-btn backup-btn" onclick="backupDatabase()">
                    💾 نسخة احتياطية
                </button>
                <button class="control-btn" onclick="refreshData()">
                    🔄 تحديث البيانات
                </button>
                <button class="control-btn clear-btn" onclick="clearOldData()">
                    🗑️ مسح البيانات القديمة
                </button>
            </div>
        </div>
        
        <!-- Database Statistics -->
        <div class="database-stats">
            <div class="stat-card">
                <span class="stat-icon">👥</span>
                <div class="stat-value" id="totalEmployees">0</div>
                <div class="stat-label">إجمالي الموظفين</div>
            </div>
            
            <div class="stat-card">
                <span class="stat-icon">📊</span>
                <div class="stat-value" id="totalRecords">0</div>
                <div class="stat-label">إجمالي السجلات</div>
            </div>
            
            <div class="stat-card">
                <span class="stat-icon">📅</span>
                <div class="stat-value" id="todayRecords">0</div>
                <div class="stat-label">سجلات اليوم</div>
            </div>
            
            <div class="stat-card">
                <span class="stat-icon">💾</span>
                <div class="stat-value" id="databaseSize">0</div>
                <div class="stat-label">حجم قاعدة البيانات (KB)</div>
            </div>
        </div>
        
        <!-- Employees Table -->
        <div class="table-panel">
            <h2 class="panel-title">
                👥 جدول الموظفين
            </h2>
            
            <input type="text" class="search-box" id="employeeSearch" placeholder="🔍 البحث في الموظفين...">
            
            <div style="overflow-x: auto;">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>الرقم</th>
                            <th>الاسم</th>
                            <th>الرقم الوظيفي</th>
                            <th>القسم</th>
                            <th>المنصب</th>
                            <th>الهاتف</th>
                            <th>البريد الإلكتروني</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                        </tr>
                    </thead>
                    <tbody id="employeesTableBody">
                        <!-- Data will be populated here -->
                    </tbody>
                </table>
            </div>
            
            <div class="pagination" id="employeesPagination">
                <!-- Pagination will be populated here -->
            </div>
        </div>
        
        <!-- Attendance Records Table -->
        <div class="table-panel">
            <h2 class="panel-title">
                📊 جدول سجلات الحضور والانصراف
            </h2>
            
            <input type="text" class="search-box" id="recordsSearch" placeholder="🔍 البحث في السجلات...">
            
            <div style="overflow-x: auto;">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>الرقم</th>
                            <th>الموظف</th>
                            <th>الرقم الوظيفي</th>
                            <th>القسم</th>
                            <th>التاريخ</th>
                            <th>وقت الحضور</th>
                            <th>وقت الانصراف</th>
                            <th>ساعات العمل</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody id="recordsTableBody">
                        <!-- Data will be populated here -->
                    </tbody>
                </table>
            </div>
            
            <div class="pagination" id="recordsPagination">
                <!-- Pagination will be populated here -->
            </div>
        </div>
    </div>
    
    <!-- Record Details Modal -->
    <div id="recordModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">تفاصيل السجل</h3>
                <button class="close-btn" onclick="closeRecordModal()">&times;</button>
            </div>
            
            <div id="recordDetails">
                <!-- Record details will be populated here -->
            </div>
        </div>
    </div>
    
    <script src="app_core.js"></script>
    <script src="attendance_advanced.js"></script>
    <script src="attendance_database.js"></script>
</body>
</html>
