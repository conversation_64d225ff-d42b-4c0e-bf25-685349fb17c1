<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Centralized Database Management</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <h1>Centralized Database Management</h1>
</body>
</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - Data Base</title>
    <link rel="stylesheet" href="style.css">
    <style>
        #loading-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #121212;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .loader {
            border: 16px solid #301934; /* Dark purple */
            border-top: 16px solid #4B0082; /* Darker purple */
            border-radius: 50%;
            width: 120px;
            height: 120px;
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        body {
            font-family: Arial, sans-serif;
            background-color: #121212;
            color: rgb(1, 57, 80);
            margin: 0;
            padding: 0;
        }

        .bank-header {
            background-color: #301934;
            color: white;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .bank-logo {
            font-size: 1.5em;
            font-weight: bold;
        }

        .bank-nav ul {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
        }

        .bank-nav li {
            margin-left: 20px;
        }

        .bank-nav a {
            color: white;
            text-decoration: none;
        }

        .bank-container {
            display: flex;
            padding: 20px;
        }

        .bank-sidebar {
            width: 200px;
            background-color: #212121;
            padding: 20px;
        }

        .bank-sidebar h3 {
            margin-top: 0;
        }

        .bank-sidebar ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .bank-sidebar li {
            padding: 10px 0;
        }

        .bank-sidebar a {
            color: white;
            text-decoration: none;
        }

        .bank-main {
            flex: 1;
            padding: 20px;
        }

        .bank-footer {
            text-align: center;
            padding: 10px;
            background-color: #212121;
        }

        /* Data Table Styles */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background-color: #212121;
            border-radius: 5px;
            overflow: hidden; /* Hide borders on the sides */
        }

        .data-table th, .data-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #333;
            color: white;
        }

        .data-table th {
            background-color: #301934;
            font-weight: bold;
        }

        .data-table tbody tr:hover {
            background-color: #333;
        }

        .access-level {
            padding: 5px 10px;
            border-radius: 5px;
            color: white;
        }

        .access-level.admin {
            background-color: #28a745; /* Green */
        }

        .access-level.manager {
            background-color: #17a2b8; /* Teal */
        }

        .access-level.user {
            background-color: #6c757d; /* Gray */
        }
    </style>
</head>
<body>
    <div id="loading-container">
        <div class="loader"></div>
    </div>
    <script>
        window.addEventListener('load', function() {
            document.getElementById('loading-container').style.display = 'none';
        });
    </script>
    <!-- Banking Header -->
    <header class="bank-header">
        <div class="bank-logo">EG BANK</div>
        <nav class="bank-nav">
            <ul>
                <li><a href="index.html" style="color: white;">Dashboard</a></li>
                <li><a href="cameras.html" style="color: white;">Security</a></li>
                <li><a href="access.html" style="color: white;">Access Control</a></li>
                <li><a href="reports.html" style="color: white;">Reports</a></li>

                <li><a href="gps.html" style="color: white;">GPS Tracking</a></li>
                <li><a href="database.html" style="color: white;">Data Base</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main Content Area -->
    <div class="bank-container">
        <!-- Sidebar -->
        <aside class="bank-sidebar">
            <h3 style="color: white;">Abdel Fattah Mahmoud</h3>
            <ul>
                <li><a href="index.html" style="color: white;">Camera Surveillance</a></li>
                <li><a href="cameras.html" style="color: white;">IP Camera Management</a></li>
                <li><a href="access.html" style="color: white;">Access Control</a></li>
                <li><a href="gps.html" style="color: white;">GPS Tracking</a></li>
                <li><a href="database.html" style="color: white;">Data Base</a></li>
            </ul>
        </aside>

        <!-- Main Content -->
        <main class="bank-main">
            <h1 style="color: white;">Data Base</h1>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Age</th>
                        <th>Phone Number</th>
                        <th>Email</th>
                        <th>Access Level</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>Jon Snow</td>
                        <td>35</td>
                        <td>(665)121-5454</td>
                        <td><EMAIL></td>
                        <td><span class="access-level admin">admin</span></td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>Cersei Lannister</td>
                        <td>42</td>
                        <td>(421)314-2288</td>
                        <td><EMAIL></td>
                        <td><span class="access-level manager">manager</span></td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>Jaime Lannister</td>
                        <td>45</td>
                        <td>(422)982-6739</td>
                        <td><EMAIL></td>
                        <td><span class="access-level user">user</span></td>
                    </tr>
                    <tr>
                        <td>4</td>
                        <td>Anya Stark</td>
                        <td>16</td>
                        <td>(921)425-6742</td>
                        <td><EMAIL></td>
                        <td><span class="access-level admin">admin</span></td>
                    </tr>
                    <tr>
                        <td>5</td>
                        <td>Daenerys Targaryen</td>
                        <td>31</td>
                        <td>(421)445-1189</td>
                        <td><EMAIL></td>
                        <td><span class="access-level user">user</span></td>
                    </tr>
                    <tr>
                        <td>6</td>
                        <td>Ever Melisandre</td>
                        <td>150</td>
                        <td>(232)545-6483</td>
                        <td><EMAIL></td>
                        <td><span class="access-level manager">manager</span></td>
                    </tr>
                    <tr>
                        <td>7</td>
                        <td>Ferrara Clifford</td>
                        <td>44</td>
                        <td>(543)124-0123</td>
                        <td><EMAIL></td>
                        <td><span class="access-level user">user</span></td>
                    </tr>
                    <tr>
                        <td>8</td>
                        <td>Rossini Frances</td>
                        <td>36</td>
                        <td>(222)444-5555</td>
                        <td><EMAIL></td>
                        <td><span class="access-level user">user</span></td>
                    </tr>
                    <tr>
                        <td>9</td>
                        <td>Harvey Roxie</td>
                        <td>65</td>
                        <td>(444)555-6239</td>
                        <td><EMAIL></td>
                        <td><span class="access-level admin">admin</span></td>
                    </tr>
                </tbody>
            </table>
        </main>
    </div>

    <!-- Banking Footer -->
    <footer class="bank-footer">
    </footer>
</body>
</html>