// EG BANK Attendance System
// نظام الحضور والانصراف المتكامل

class AttendanceSystem {
    constructor() {
        this.currentUser = null;
        this.workingHours = {
            start: '09:00',
            end: '17:00',
            breakStart: '12:00',
            breakEnd: '13:00'
        };
        this.isCheckedIn = false;
        this.todayRecord = null;
        this.init();
    }

    // Initialize the system
    init() {
        this.loadCurrentUser();
        this.loadTodayRecord();
        this.updateDisplay();
        this.startClock();
        this.loadSummaryData();
        this.loadRecentActivity();
        
        console.log('Attendance System initialized');
    }

    // Load current user
    loadCurrentUser() {
        if (window.egbank && window.egbank.getCurrentUser()) {
            this.currentUser = window.egbank.getCurrentUser();
        } else {
            // Fallback to localStorage
            const username = localStorage.getItem('username');
            if (username) {
                this.currentUser = { username: username, fullName: username };
            }
        }
        
        if (this.currentUser) {
            document.getElementById('currentEmployee').textContent = this.currentUser.fullName || this.currentUser.username;
        }
    }

    // Load today's attendance record
    loadTodayRecord() {
        const today = new Date().toDateString();
        const attendanceRecords = JSON.parse(localStorage.getItem('attendanceRecords') || '[]');
        
        this.todayRecord = attendanceRecords.find(record => 
            record.userId === (this.currentUser?.username || 'unknown') && 
            new Date(record.date).toDateString() === today
        );

        if (this.todayRecord) {
            this.isCheckedIn = this.todayRecord.status === 'checked_in';
        }
    }

    // Start the clock
    startClock() {
        this.updateClock();
        setInterval(() => this.updateClock(), 1000);
    }

    // Update clock display
    updateClock() {
        const now = new Date();
        
        // Update time
        const timeString = now.toLocaleTimeString('ar-EG', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        document.getElementById('currentTime').textContent = timeString;
        
        // Update date
        const dateString = now.toLocaleDateString('ar-EG', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
        document.getElementById('currentDate').textContent = dateString;
        
        // Update working hours if checked in
        if (this.isCheckedIn && this.todayRecord) {
            this.updateWorkingHours();
        }
    }

    // Update display based on current state
    updateDisplay() {
        const btn = document.getElementById('attendanceBtn');
        const btnIcon = document.getElementById('btnIcon');
        const btnText = document.getElementById('btnText');
        
        if (this.isCheckedIn) {
            btn.className = 'attendance-btn btn-checkout';
            btnIcon.textContent = '🔴';
            btnText.textContent = 'تسجيل انصراف';
        } else {
            btn.className = 'attendance-btn btn-checkin';
            btnIcon.textContent = '🟢';
            btnText.textContent = 'تسجيل حضور';
        }
        
        this.updateStatusPanel();
    }

    // Update status panel
    updateStatusPanel() {
        if (this.todayRecord) {
            document.getElementById('checkinTime').textContent = 
                this.todayRecord.checkinTime ? this.formatTime(this.todayRecord.checkinTime) : '--:--';
            
            document.getElementById('checkoutTime').textContent = 
                this.todayRecord.checkoutTime ? this.formatTime(this.todayRecord.checkoutTime) : '--:--';
            
            document.getElementById('currentStatus').textContent = 
                this.isCheckedIn ? 'حاضر' : (this.todayRecord.checkoutTime ? 'منصرف' : 'غائب');
            
            // Calculate late time
            if (this.todayRecord.checkinTime) {
                const lateMinutes = this.calculateLateTime(this.todayRecord.checkinTime);
                document.getElementById('lateTime').textContent = 
                    lateMinutes > 0 ? `${lateMinutes} دقيقة` : '0 دقيقة';
            }
        } else {
            document.getElementById('checkinTime').textContent = '--:--';
            document.getElementById('checkoutTime').textContent = '--:--';
            document.getElementById('currentStatus').textContent = 'لم يسجل حضور';
            document.getElementById('lateTime').textContent = '0 دقيقة';
        }
    }

    // Toggle attendance (check in/out)
    toggleAttendance() {
        if (!this.currentUser) {
            this.showNotification('يرجى تسجيل الدخول أولاً', 'error');
            return;
        }

        const now = new Date();
        const today = now.toDateString();
        
        if (this.isCheckedIn) {
            // Check out
            this.checkOut(now);
        } else {
            // Check in
            this.checkIn(now, today);
        }
        
        this.updateDisplay();
        this.saveAttendanceRecord();
        this.addToRecentActivity();
        this.loadSummaryData();
    }

    // Check in
    checkIn(now, today) {
        if (!this.todayRecord) {
            this.todayRecord = {
                id: 'att_' + Date.now(),
                userId: this.currentUser.username,
                userName: this.currentUser.fullName || this.currentUser.username,
                date: today,
                checkinTime: now.toISOString(),
                checkoutTime: null,
                status: 'checked_in',
                workingHours: 0,
                lateMinutes: this.calculateLateTime(now.toISOString()),
                notes: ''
            };
        } else {
            this.todayRecord.checkinTime = now.toISOString();
            this.todayRecord.status = 'checked_in';
            this.todayRecord.lateMinutes = this.calculateLateTime(now.toISOString());
        }
        
        this.isCheckedIn = true;
        this.showNotification('تم تسجيل الحضور بنجاح', 'success');
        
        // Log action
        if (window.egbank) {
            window.egbank.logAction('attendance_checkin', 'تسجيل حضور', {
                time: now.toISOString(),
                lateMinutes: this.todayRecord.lateMinutes
            });
        }
    }

    // Check out
    checkOut(now) {
        if (this.todayRecord) {
            this.todayRecord.checkoutTime = now.toISOString();
            this.todayRecord.status = 'checked_out';
            
            // Calculate working hours
            const checkinTime = new Date(this.todayRecord.checkinTime);
            const workingMs = now.getTime() - checkinTime.getTime();
            this.todayRecord.workingHours = Math.round(workingMs / (1000 * 60 * 60) * 100) / 100;
        }
        
        this.isCheckedIn = false;
        this.showNotification('تم تسجيل الانصراف بنجاح', 'success');
        
        // Log action
        if (window.egbank) {
            window.egbank.logAction('attendance_checkout', 'تسجيل انصراف', {
                time: now.toISOString(),
                workingHours: this.todayRecord?.workingHours || 0
            });
        }
    }

    // Save attendance record
    saveAttendanceRecord() {
        if (!this.todayRecord) return;
        
        const attendanceRecords = JSON.parse(localStorage.getItem('attendanceRecords') || '[]');
        const existingIndex = attendanceRecords.findIndex(record => record.id === this.todayRecord.id);
        
        if (existingIndex !== -1) {
            attendanceRecords[existingIndex] = this.todayRecord;
        } else {
            attendanceRecords.push(this.todayRecord);
        }
        
        localStorage.setItem('attendanceRecords', JSON.stringify(attendanceRecords));
    }

    // Calculate late time in minutes
    calculateLateTime(checkinTimeStr) {
        const checkinTime = new Date(checkinTimeStr);
        const workStartTime = new Date(checkinTime);
        const [startHour, startMinute] = this.workingHours.start.split(':');
        workStartTime.setHours(parseInt(startHour), parseInt(startMinute), 0, 0);
        
        if (checkinTime > workStartTime) {
            return Math.round((checkinTime.getTime() - workStartTime.getTime()) / (1000 * 60));
        }
        
        return 0;
    }

    // Update working hours display
    updateWorkingHours() {
        if (!this.todayRecord || !this.todayRecord.checkinTime) return;
        
        const checkinTime = new Date(this.todayRecord.checkinTime);
        const now = new Date();
        const workingMs = now.getTime() - checkinTime.getTime();
        const workingHours = workingMs / (1000 * 60 * 60);
        
        const hours = Math.floor(workingHours);
        const minutes = Math.floor((workingHours - hours) * 60);
        
        document.getElementById('workingHours').textContent = `${hours}:${minutes.toString().padStart(2, '0')}`;
    }

    // Load summary data
    loadSummaryData() {
        const today = new Date().toDateString();
        const attendanceRecords = JSON.parse(localStorage.getItem('attendanceRecords') || '[]');
        const employees = JSON.parse(localStorage.getItem('systemUsers') || '[]');
        
        const todayRecords = attendanceRecords.filter(record => 
            new Date(record.date).toDateString() === today
        );
        
        const totalEmployees = employees.filter(emp => emp.status === 'active').length;
        const presentEmployees = todayRecords.filter(record => record.checkinTime).length;
        const absentEmployees = totalEmployees - presentEmployees;
        const lateEmployees = todayRecords.filter(record => record.lateMinutes > 0).length;
        
        document.getElementById('totalEmployees').textContent = totalEmployees;
        document.getElementById('presentEmployees').textContent = presentEmployees;
        document.getElementById('absentEmployees').textContent = absentEmployees;
        document.getElementById('lateEmployees').textContent = lateEmployees;
    }

    // Load recent activity
    loadRecentActivity() {
        const attendanceRecords = JSON.parse(localStorage.getItem('attendanceRecords') || '[]');
        const recentRecords = attendanceRecords
            .sort((a, b) => new Date(b.checkinTime || b.checkoutTime) - new Date(a.checkinTime || a.checkoutTime))
            .slice(0, 5);
        
        const activityContainer = document.getElementById('recentActivity');
        activityContainer.innerHTML = '';
        
        if (recentRecords.length === 0) {
            activityContainer.innerHTML = '<div style="text-align: center; color: rgba(255,255,255,0.5); padding: 20px;">لا توجد أنشطة حديثة</div>';
            return;
        }
        
        recentRecords.forEach(record => {
            const activityItem = document.createElement('div');
            activityItem.className = 'activity-item';
            
            const isCheckin = record.status === 'checked_in' || (record.checkinTime && !record.checkoutTime);
            const time = isCheckin ? record.checkinTime : record.checkoutTime;
            
            activityItem.innerHTML = `
                <div class="activity-icon ${isCheckin ? 'icon-checkin' : 'icon-checkout'}">
                    ${isCheckin ? '🟢' : '🔴'}
                </div>
                <div class="activity-details">
                    <div class="activity-type">${record.userName} - ${isCheckin ? 'تسجيل حضور' : 'تسجيل انصراف'}</div>
                    <div class="activity-time">${this.formatDateTime(time)}</div>
                </div>
            `;
            
            activityContainer.appendChild(activityItem);
        });
    }

    // Add to recent activity
    addToRecentActivity() {
        // Refresh the recent activity display
        setTimeout(() => this.loadRecentActivity(), 100);
    }

    // Format time
    formatTime(timeStr) {
        const time = new Date(timeStr);
        return time.toLocaleTimeString('ar-EG', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    // Format date and time
    formatDateTime(timeStr) {
        const time = new Date(timeStr);
        return time.toLocaleString('ar-EG', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    // Show notification
    showNotification(message, type = 'info') {
        if (window.egbank) {
            window.egbank.showNotification(message, type);
        } else {
            // Fallback notification
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#48bb78' : type === 'error' ? '#f56565' : '#4299e1'};
                color: white;
                padding: 15px 25px;
                border-radius: 8px;
                z-index: 10000;
                font-weight: 500;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 3000);
        }
    }

    // Get attendance statistics
    getStatistics(startDate, endDate) {
        const attendanceRecords = JSON.parse(localStorage.getItem('attendanceRecords') || '[]');
        const filteredRecords = attendanceRecords.filter(record => {
            const recordDate = new Date(record.date);
            return recordDate >= startDate && recordDate <= endDate;
        });
        
        return {
            totalDays: filteredRecords.length,
            presentDays: filteredRecords.filter(r => r.checkinTime).length,
            lateDays: filteredRecords.filter(r => r.lateMinutes > 0).length,
            totalWorkingHours: filteredRecords.reduce((sum, r) => sum + (r.workingHours || 0), 0),
            averageWorkingHours: filteredRecords.length > 0 ? 
                filteredRecords.reduce((sum, r) => sum + (r.workingHours || 0), 0) / filteredRecords.length : 0
        };
    }
}

// Global function for button click
function toggleAttendance() {
    if (window.attendanceSystem) {
        window.attendanceSystem.toggleAttendance();
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Wait for EG BANK system if available
    if (window.EGBankApp) {
        document.addEventListener('egbankReady', function() {
            window.attendanceSystem = new AttendanceSystem();
        });
    } else {
        // Initialize directly if EG BANK system not available
        window.attendanceSystem = new AttendanceSystem();
    }
});

console.log('Attendance System Loaded Successfully');
