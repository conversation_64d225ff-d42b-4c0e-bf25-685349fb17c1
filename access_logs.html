<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - Access Logs</title>
    <link rel="stylesheet" href="style.css">
    <style>
        #loading-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #2c2c2c;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .loader {
            border: 16px solid #3a3a3a;
            border-top: 16px solid #1a365d;
            border-radius: 50%;
            width: 120px;
            height: 120px;
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .logs-table {
            background: #3a3a3a;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .logs-table table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .logs-table th,
        .logs-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #555555;
            color: white;
        }
        
        .logs-table th {
            background-color: #1a365d;
            font-weight: 500;
        }
        
        .logs-table tbody tr:nth-child(even) {
            background-color: #4a4a4a;
        }
        
        .logs-table tbody tr:hover {
            background-color: #555555;
        }
        
        .status-success {
            color: #48bb78;
            font-weight: bold;
        }
        
        .status-failed {
            color: #f56565;
            font-weight: bold;
        }
        
        .filter-section {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .filter-select {
            padding: 8px 12px;
            border: 1px solid #555555;
            border-radius: 4px;
            background-color: #2c2c2c;
            color: white;
        }
    </style>
</head>
<body>
    <div id="loading-container">
        <div class="loader"></div>
    </div>
    
    <script>
        window.addEventListener('load', function() {
            document.getElementById('loading-container').style.display = 'none';
        });
    </script>
    
    <!-- Banking Header -->
    <header class="bank-header">
        <div class="bank-logo">EG BANK</div>
        <nav class="bank-nav">
            <ul>
                <li><a href="index.html" style="color: white;">لوحة التحكم</a></li>
                <li><a href="cameras.html" style="color: white;">الأمان</a></li>
                <li><a href="access.html" class="active" style="color: white;">التحكم في الوصول</a></li>
                <li><a href="reports.html" style="color: white;">التقارير</a></li>
                <li><a href="settings.html" style="color: white;">الإعدادات</a></li>
                <li><a href="gps.html" style="color: white;">تتبع GPS</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main Content Area -->
    <div class="bank-container">
        <!-- Sidebar -->
        <aside class="bank-sidebar">
            <h3 style="color: white;">عبد الفتاح محمود</h3>
            <ul>
                <li><a href="index.html" style="color: white;">مراقبة الكاميرات</a></li>
                <li><a href="cameras.html" style="color: white;">إدارة كاميرات IP</a></li>
                <li class="active" style="color: white;">سجلات الوصول</li>
                <li><a href="reports.html" style="color: white;">التقارير</a></li>
                <li><a href="user_management.html" style="color: white;">إدارة المستخدمين</a></li>
            </ul>
        </aside>

        <!-- Access Logs Section -->
        <main class="bank-main">
            <div style="display: flex; align-items: center; margin-bottom: 30px;">
                <button onclick="window.history.back()" style="background: #1a365d; color: white; border: none; padding: 10px 15px; border-radius: 4px; margin-left: 15px; cursor: pointer;">← رجوع</button>
                <h1 style="color: white; margin: 0;">سجلات الوصول والدخول</h1>
            </div>
            
            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalLogs">156</div>
                    <div style="color: white;">إجمالي السجلات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="successfulAccess">142</div>
                    <div style="color: white;">دخول ناجح</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="failedAccess">14</div>
                    <div style="color: white;">محاولات فاشلة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="todayLogs">24</div>
                    <div style="color: white;">سجلات اليوم</div>
                </div>
            </div>
            
            <!-- Search and Filter -->
            <div class="search-container">
                <div class="search-bar">
                    <input type="text" id="searchInput" class="search-input" placeholder="البحث في السجلات...">
                    <button class="search-btn" onclick="searchLogs()">بحث</button>
                    <button class="search-btn" onclick="exportLogs()">تصدير السجلات</button>
                </div>
                
                <div class="filter-section">
                    <select id="statusFilter" class="filter-select" onchange="filterLogs()">
                        <option value="all">جميع الحالات</option>
                        <option value="success">ناجح فقط</option>
                        <option value="failed">فاشل فقط</option>
                    </select>
                    
                    <select id="doorFilter" class="filter-select" onchange="filterLogs()">
                        <option value="all">جميع الأبواب</option>
                        <option value="door_1">الباب الرئيسي</option>
                        <option value="door_2">باب الخزينة</option>
                        <option value="door_3">باب المكاتب</option>
                        <option value="door_4">باب الطوارئ</option>
                    </select>
                    
                    <select id="dateFilter" class="filter-select" onchange="filterLogs()">
                        <option value="today">اليوم</option>
                        <option value="yesterday">أمس</option>
                        <option value="week">هذا الأسبوع</option>
                        <option value="month">هذا الشهر</option>
                    </select>
                </div>
            </div>
            
            <!-- Access Logs Table -->
            <div class="logs-table">
                <h3 style="color: white; margin-bottom: 15px;">سجل الوصول التفصيلي</h3>
                <table id="logsTable">
                    <thead>
                        <tr>
                            <th>التاريخ والوقت</th>
                            <th>المستخدم</th>
                            <th>الباب</th>
                            <th>العملية</th>
                            <th>الحالة</th>
                            <th>عنوان IP</th>
                        </tr>
                    </thead>
                    <tbody id="logsTableBody">
                        <!-- Logs will be populated by JavaScript -->
                    </tbody>
                </table>
            </div>
        </main>
    </div>

    <!-- Banking Footer -->
    <footer class="bank-footer">
        <p>© 2024 البنك المصري - سجلات الوصول</p>
    </footer>

    <!-- Floating Toolbar -->
    <div class="floating-toolbar">
        <div class="toolbar-header">
            لوحة التحكم الرئيسية
        </div>
        
        <div class="toolbar-section">
            <div class="toolbar-section-title">Control Panel</div>
            <a href="index.html" class="toolbar-item">
                <div class="toolbar-item-icon">🏠</div>
                <div class="toolbar-item-text">لوحة التحكم</div>
            </a>
            <a href="cameras.html" class="toolbar-item">
                <div class="toolbar-item-icon">🔒</div>
                <div class="toolbar-item-text">الأمان</div>
            </a>
            <a href="access.html" class="toolbar-item active">
                <div class="toolbar-item-icon">🚪</div>
                <div class="toolbar-item-text">التحكم في الوصول</div>
            </a>
            <a href="reports.html" class="toolbar-item">
                <div class="toolbar-item-icon">📊</div>
                <div class="toolbar-item-text">التقارير</div>
            </a>
            <a href="settings.html" class="toolbar-item">
                <div class="toolbar-item-icon">⚙️</div>
                <div class="toolbar-item-text">الإعدادات</div>
            </a>
            <a href="gps.html" class="toolbar-item">
                <div class="toolbar-item-icon">📍</div>
                <div class="toolbar-item-text">تتبع GPS</div>
            </a>
        </div>
        
        <div class="toolbar-section">
            <div class="toolbar-section-title">Abdel Fattah Mahmoud</div>
            <a href="index.html" class="toolbar-item">
                <div class="toolbar-item-icon">📹</div>
                <div class="toolbar-item-text">مراقبة الكاميرات</div>
            </a>
            <a href="cameras.html" class="toolbar-item">
                <div class="toolbar-item-icon">📷</div>
                <div class="toolbar-item-text">إدارة كاميرات IP</div>
            </a>
            <a href="access.html" class="toolbar-item active">
                <div class="toolbar-item-icon">🔐</div>
                <div class="toolbar-item-text">التحكم في الوصول</div>
            </a>
            <a href="reports.html" class="toolbar-item">
                <div class="toolbar-item-icon">📈</div>
                <div class="toolbar-item-text">التقارير</div>
            </a>
            <a href="user_management.html" class="toolbar-item">
                <div class="toolbar-item-icon">👥</div>
                <div class="toolbar-item-text">إدارة المستخدمين</div>
            </a>
        </div>
        
        <div class="toolbar-user">
            <div class="toolbar-user-name">عبد الفتاح محمود</div>
            <div class="toolbar-user-role">مدير النظام</div>
        </div>
    </div>

    <script src="access_logs.js"></script>
</body>
</html>
