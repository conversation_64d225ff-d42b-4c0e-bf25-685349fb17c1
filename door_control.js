// Door Control System JavaScript
// نظام التحكم في الأبواب

// Current language state
let currentLanguage = 'ar';

// User permissions and authentication
const currentUser = {
    id: 1,
    name: 'عبد الفتاح محمود',
    permissions: ['door_1', 'door_2', 'door_3', 'door_4', 'door_5', 'door_6'], // User has access to these doors
    role: 'admin'
};

// Door data simulation (would normally come from EntraPass API)
let doors = [
    {
        id: 'door_1',
        name: { ar: 'الباب الرئيسي', en: 'Main Entrance' },
        location: { ar: 'المدخل الرئيسي', en: 'Main Lobby' },
        status: 'closed',
        lastAccess: '2024-01-15 14:30:25',
        accessCount: 45
    },
    {
        id: 'door_2',
        name: { ar: 'باب الخزينة', en: 'Vault Door' },
        location: { ar: 'منطقة الخزينة', en: 'Vault Area' },
        status: 'locked',
        lastAccess: '2024-01-15 09:15:10',
        accessCount: 12
    },
    {
        id: 'door_3',
        name: { ar: 'باب المكاتب', en: 'Office Door' },
        location: { ar: 'الطابق الثاني', en: 'Second Floor' },
        status: 'open',
        lastAccess: '2024-01-15 15:45:30',
        accessCount: 78
    },
    {
        id: 'door_4',
        name: { ar: 'باب الطوارئ', en: 'Emergency Exit' },
        location: { ar: 'المخرج الخلفي', en: 'Back Exit' },
        status: 'closed',
        lastAccess: '2024-01-15 08:20:15',
        accessCount: 5
    },
    {
        id: 'door_5',
        name: { ar: 'باب الأرشيف', en: 'Archive Door' },
        location: { ar: 'قسم الأرشيف', en: 'Archive Department' },
        status: 'open',
        lastAccess: '2024-01-15 16:10:45',
        accessCount: 23
    },
    {
        id: 'door_6',
        name: { ar: 'باب الاجتماعات', en: 'Meeting Room' },
        location: { ar: 'غرفة الاجتماعات', en: 'Conference Room' },
        status: 'open',
        lastAccess: '2024-01-15 13:25:20',
        accessCount: 34
    },
    {
        id: 'door_7',
        name: { ar: 'باب الخدمات', en: 'Service Door' },
        location: { ar: 'منطقة الخدمات', en: 'Service Area' },
        status: 'closed',
        lastAccess: '2024-01-15 11:40:30',
        accessCount: 18
    },
    {
        id: 'door_8',
        name: { ar: 'باب الحراسة', en: 'Security Door' },
        location: { ar: 'غرفة الحراسة', en: 'Security Room' },
        status: 'closed',
        lastAccess: '2024-01-15 12:55:10',
        accessCount: 67
    }
];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    renderDoors();
    updateStatistics();
    
    // Simulate real-time updates
    setInterval(simulateRealTimeUpdates, 30000); // Update every 30 seconds
});

// Initialize application
function initializeApp() {
    console.log('Door Control System Initialized');
    console.log('Current User:', currentUser.name);
    console.log('User Permissions:', currentUser.permissions);
}

// Render doors in the grid
function renderDoors() {
    const doorGrid = document.getElementById('doorGrid');
    doorGrid.innerHTML = '';
    
    doors.forEach(door => {
        const hasAccess = currentUser.permissions.includes(door.id);
        const doorCard = createDoorCard(door, hasAccess);
        doorGrid.appendChild(doorCard);
    });
}

// Create individual door card
function createDoorCard(door, hasAccess) {
    const card = document.createElement('div');
    card.className = 'door-card';
    
    const statusClass = `status-${door.status}`;
    const statusText = {
        ar: {
            open: 'مفتوح',
            closed: 'مغلق',
            locked: 'مقفل يدوياً'
        },
        en: {
            open: 'Open',
            closed: 'Closed',
            locked: 'Manually Locked'
        }
    };
    
    card.innerHTML = `
        <h3 style="color: white; margin-bottom: 10px;">${door.name[currentLanguage]}</h3>
        <p style="color: #a0a0a0; margin-bottom: 15px;">${door.location[currentLanguage]}</p>
        
        <div class="door-status">
            <span class="status-indicator ${statusClass}"></span>
            <span style="color: white;">${statusText[currentLanguage][door.status]}</span>
        </div>
        
        <div style="color: #a0a0a0; font-size: 12px; margin-bottom: 15px;">
            <div>
                ${currentLanguage === 'ar' ? 'آخر دخول: ' + door.lastAccess : 'Last Access: ' + door.lastAccess}
            </div>
            <div>
                ${currentLanguage === 'ar' ? 'عدد مرات الدخول: ' + door.accessCount : 'Access Count: ' + door.accessCount}
            </div>
        </div>
        
        <div class="door-controls">
            <button class="door-btn btn-open" 
                    onclick="toggleDoor('${door.id}')" 
                    ${!hasAccess || door.status === 'locked' ? 'disabled' : ''}>
                ${door.status === 'open' ? 
                    (currentLanguage === 'ar' ? 'إغلاق الباب' : 'Close Door') : 
                    (currentLanguage === 'ar' ? 'فتح الباب' : 'Open Door')}
            </button>
        </div>
        
        ${!hasAccess ? `<div style="color: #f56565; font-size: 12px; margin-top: 10px; text-align: center;">
            ${currentLanguage === 'ar' ? 'ليس لديك صلاحية للوصول' : 'No Access Permission'}
        </div>` : ''}
    `;
    
    return card;
}

// Toggle door status (open/close)
function toggleDoor(doorId) {
    const door = doors.find(d => d.id === doorId);
    if (!door) return;
    
    // Check user permissions
    if (!currentUser.permissions.includes(doorId)) {
        showNotification('ليس لديك صلاحية للوصول إلى هذا الباب!', 'error');
        return;
    }
    
    // Cannot toggle locked doors
    if (door.status === 'locked') {
        showNotification('هذا الباب مقفل يدوياً ولا يمكن التحكم فيه عن بُعد!', 'error');
        return;
    }
    
    // Update door status
    door.status = door.status === 'open' ? 'closed' : 'open';
    door.lastAccess = new Date().toLocaleString('sv-SE').replace('T', ' ').substring(0, 19);
    door.accessCount++;
    
    // Re-render doors and update statistics
    renderDoors();
    updateStatistics();
    
    // Show success message
    showNotification(
        `تم ${door.status === 'open' ? 'فتح' : 'إغلاق'} ${door.name[currentLanguage]} بنجاح`,
        'success'
    );
}

// Update statistics
function updateStatistics() {
    const stats = {
        total: doors.length,
        open: doors.filter(d => d.status === 'open').length,
        closed: doors.filter(d => d.status === 'closed').length,
        locked: doors.filter(d => d.status === 'locked').length
    };
    
    document.getElementById('totalDoors').textContent = stats.total;
    document.getElementById('openDoors').textContent = stats.open;
    document.getElementById('closedDoors').textContent = stats.closed;
    document.getElementById('lockedDoors').textContent = stats.locked;
}

// Show notification
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    const bgColor = type === 'success' ? '#48bb78' : '#f56565';
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: ${bgColor};
        color: white;
        padding: 15px 25px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 1001;
        font-weight: 500;
        animation: slideDown 0.3s ease;
    `;
    
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideUp 0.3s ease';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Simulate real-time updates
function simulateRealTimeUpdates() {
    const randomDoor = doors[Math.floor(Math.random() * doors.length)];
    
    if (Math.random() > 0.8) { // 20% chance of status change
        const oldStatus = randomDoor.status;
        
        if (randomDoor.status === 'open') {
            randomDoor.status = 'closed';
        } else if (randomDoor.status === 'closed' && Math.random() > 0.5) {
            randomDoor.status = 'open';
        }
        
        if (oldStatus !== randomDoor.status) {
            randomDoor.lastAccess = new Date().toLocaleString('sv-SE').replace('T', ' ').substring(0, 19);
            
            // Update UI
            renderDoors();
            updateStatistics();
            
            console.log(`Real-time update: ${randomDoor.name.ar} is now ${randomDoor.status}`);
        }
    }
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideDown {
        from { opacity: 0; transform: translateX(-50%) translateY(-20px); }
        to { opacity: 1; transform: translateX(-50%) translateY(0); }
    }
    
    @keyframes slideUp {
        from { opacity: 1; transform: translateX(-50%) translateY(0); }
        to { opacity: 0; transform: translateX(-50%) translateY(-20px); }
    }
`;
document.head.appendChild(style);

console.log('Door Control System JavaScript Loaded Successfully');
