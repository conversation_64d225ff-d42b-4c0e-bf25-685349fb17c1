<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - نظام الحضور والانصراف</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(26, 54, 93, 0.9);
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(45deg, #1a365d, #2c5282);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .logo-text {
            font-size: 28px;
            font-weight: 900;
            color: white;
        }
        
        .nav-buttons {
            display: flex;
            gap: 15px;
        }
        
        .nav-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
        }
        
        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .page-title {
            text-align: center;
            font-size: 36px;
            font-weight: 900;
            margin-bottom: 40px;
            color: #3182ce;
            text-shadow: 0 0 20px rgba(49, 130, 206, 0.5);
        }
        
        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .attendance-panel {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            backdrop-filter: blur(20px);
        }
        
        .panel-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 20px;
            color: white;
        }
        
        .current-time {
            font-size: 48px;
            font-weight: 900;
            color: #3182ce;
            margin-bottom: 10px;
            font-family: 'Orbitron', monospace;
            text-shadow: 0 0 15px rgba(49, 130, 206, 0.5);
        }
        
        .current-date {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 30px;
        }
        
        .attendance-btn {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            border: none;
            font-size: 24px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .btn-checkin {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
        }
        
        .btn-checkout {
            background: linear-gradient(135deg, #f56565, #e53e3e);
            color: white;
        }
        
        .btn-disabled {
            background: linear-gradient(135deg, #666, #888);
            color: #ccc;
            cursor: not-allowed;
        }
        
        .attendance-btn:hover:not(.btn-disabled) {
            transform: scale(1.05);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
        }
        
        .status-panel {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(20px);
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-label {
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }
        
        .status-value {
            color: #3182ce;
            font-weight: 700;
            font-size: 18px;
        }
        
        .today-summary {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(20px);
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .summary-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .summary-icon {
            font-size: 32px;
            margin-bottom: 10px;
            display: block;
        }
        
        .summary-value {
            font-size: 24px;
            font-weight: 900;
            color: #3182ce;
            margin-bottom: 5px;
        }
        
        .summary-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .recent-activity {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(20px);
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }
        
        .icon-checkin {
            background: linear-gradient(135deg, #48bb78, #38a169);
        }
        
        .icon-checkout {
            background: linear-gradient(135deg, #f56565, #e53e3e);
        }
        
        .activity-details {
            flex: 1;
        }
        
        .activity-type {
            font-weight: 600;
            color: white;
            margin-bottom: 5px;
        }
        
        .activity-time {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .action-card {
            background: rgba(26, 54, 93, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: white;
        }
        
        .action-card:hover {
            transform: translateY(-5px);
            border-color: #2c5282;
            box-shadow: 0 15px 30px rgba(26, 54, 93, 0.4);
        }
        
        .action-icon {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }
        
        .action-title {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .action-description {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
            }
            
            .header-content {
                flex-direction: column;
                gap: 15px;
            }
            
            .current-time {
                font-size: 36px;
            }
            
            .attendance-btn {
                width: 150px;
                height: 150px;
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">🏦</div>
                <div class="logo-text">EG BANK</div>
            </div>
            
            <div class="nav-buttons">
                <a href="home_simple.html" class="nav-btn">🏠 الرئيسية</a>
                <a href="attendance_reports.html" class="nav-btn">📊 التقارير</a>
                <a href="attendance_settings.html" class="nav-btn">⚙️ الإعدادات</a>
            </div>
        </div>
    </header>
    
    <div class="container">
        <h1 class="page-title">⏰ نظام الحضور والانصراف</h1>
        
        <div class="main-grid">
            <!-- Attendance Panel -->
            <div class="attendance-panel">
                <h2 class="panel-title">تسجيل الحضور والانصراف</h2>
                
                <div class="current-time" id="currentTime">00:00:00</div>
                <div class="current-date" id="currentDate">الأحد، 1 يناير 2024</div>
                
                <button class="attendance-btn btn-checkin" id="attendanceBtn" onclick="toggleAttendance()">
                    <span id="btnIcon">🟢</span>
                    <span id="btnText">تسجيل حضور</span>
                </button>
                
                <div style="margin-top: 20px;">
                    <div style="font-size: 14px; color: rgba(255,255,255,0.7);">الموظف الحالي:</div>
                    <div style="font-size: 18px; font-weight: 600; color: white;" id="currentEmployee">غير محدد</div>
                </div>
            </div>
            
            <!-- Status Panel -->
            <div class="status-panel">
                <h2 class="panel-title">حالة اليوم</h2>
                
                <div class="status-item">
                    <span class="status-label">وقت الحضور:</span>
                    <span class="status-value" id="checkinTime">--:--</span>
                </div>
                
                <div class="status-item">
                    <span class="status-label">وقت الانصراف:</span>
                    <span class="status-value" id="checkoutTime">--:--</span>
                </div>
                
                <div class="status-item">
                    <span class="status-label">ساعات العمل:</span>
                    <span class="status-value" id="workingHours">0:00</span>
                </div>
                
                <div class="status-item">
                    <span class="status-label">الحالة:</span>
                    <span class="status-value" id="currentStatus">غير محدد</span>
                </div>
                
                <div class="status-item">
                    <span class="status-label">التأخير:</span>
                    <span class="status-value" id="lateTime">0 دقيقة</span>
                </div>
            </div>
        </div>
        
        <!-- Today Summary -->
        <div class="today-summary">
            <h2 class="panel-title">ملخص اليوم</h2>
            <div class="summary-grid">
                <div class="summary-card">
                    <span class="summary-icon">👥</span>
                    <div class="summary-value" id="totalEmployees">0</div>
                    <div class="summary-label">إجمالي الموظفين</div>
                </div>
                
                <div class="summary-card">
                    <span class="summary-icon">✅</span>
                    <div class="summary-value" id="presentEmployees">0</div>
                    <div class="summary-label">الحاضرين اليوم</div>
                </div>
                
                <div class="summary-card">
                    <span class="summary-icon">❌</span>
                    <div class="summary-value" id="absentEmployees">0</div>
                    <div class="summary-label">الغائبين اليوم</div>
                </div>
                
                <div class="summary-card">
                    <span class="summary-icon">⏰</span>
                    <div class="summary-value" id="lateEmployees">0</div>
                    <div class="summary-label">المتأخرين</div>
                </div>
            </div>
        </div>
        
        <!-- Recent Activity -->
        <div class="recent-activity">
            <h2 class="panel-title">النشاط الأخير</h2>
            <div id="recentActivity">
                <!-- Activity items will be populated here -->
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="quick-actions">
            <a href="attendance_reports.html" class="action-card">
                <span class="action-icon">📊</span>
                <div class="action-title">التقارير</div>
                <div class="action-description">عرض التقارير اليومية والشهرية</div>
            </a>
            
            <a href="attendance_employees.html" class="action-card">
                <span class="action-icon">👥</span>
                <div class="action-title">إدارة الموظفين</div>
                <div class="action-description">إضافة وتعديل بيانات الموظفين</div>
            </a>
            
            <a href="attendance_settings.html" class="action-card">
                <span class="action-icon">⚙️</span>
                <div class="action-title">الإعدادات</div>
                <div class="action-description">تحديد أوقات العمل والإعدادات</div>
            </a>
            
            <a href="attendance_history.html" class="action-card">
                <span class="action-icon">📅</span>
                <div class="action-title">السجل التاريخي</div>
                <div class="action-description">عرض سجل الحضور والانصراف</div>
            </a>
        </div>
    </div>
    
    <script src="app_core.js"></script>
    <script src="attendance_system.js"></script>
</body>
</html>
