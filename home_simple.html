<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - الصفحة الرئيسية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(26, 54, 93, 0.9);
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(45deg, #1a365d, #2c5282);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .logo-text {
            font-size: 28px;
            font-weight: 900;
            color: white;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logout-btn {
            background: #e53e3e;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
        }
        
        .logout-btn:hover {
            background: #c53030;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .welcome-section {
            text-align: center;
            margin-bottom: 50px;
        }
        
        .welcome-title {
            font-size: 42px;
            font-weight: 900;
            margin-bottom: 15px;
            color: #3182ce;
        }
        
        .welcome-subtitle {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 20px;
        }
        
        .current-time {
            font-size: 20px;
            color: #4299e1;
            font-weight: 600;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 50px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            border-color: #1a365d;
            box-shadow: 0 15px 30px rgba(26, 54, 93, 0.3);
        }
        
        .stat-icon {
            font-size: 40px;
            margin-bottom: 15px;
            display: block;
        }
        
        .stat-value {
            font-size: 32px;
            font-weight: 900;
            color: #3182ce;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .actions-section {
            margin-bottom: 50px;
        }
        
        .section-title {
            font-size: 28px;
            font-weight: 700;
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }
        
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
        }
        
        .action-card {
            background: rgba(26, 54, 93, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .action-card:hover {
            transform: translateY(-5px);
            border-color: #2c5282;
            box-shadow: 0 15px 30px rgba(26, 54, 93, 0.4);
        }
        
        .action-icon {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }
        
        .action-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 10px;
            color: white;
        }
        
        .action-description {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.5;
        }
        
        .status-section {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
        }
        
        .status-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .status-title {
            font-size: 20px;
            font-weight: 700;
            color: white;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #48bb78;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .status-text {
            color: #48bb78;
            font-weight: 600;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }
        
        .status-name {
            color: rgba(255, 255, 255, 0.8);
        }
        
        .status-value {
            color: #3182ce;
            font-weight: 600;
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }
            
            .container {
                padding: 20px 15px;
            }
            
            .welcome-title {
                font-size: 28px;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .actions-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">🏦</div>
                <div class="logo-text">EG BANK</div>
            </div>
            
            <div class="user-info">
                <div>
                    <div style="font-weight: 600;" id="userName">المستخدم</div>
                    <div style="font-size: 14px; color: rgba(255,255,255,0.7);" id="userRole">مدير النظام</div>
                </div>
                <button class="logout-btn" onclick="logout()">🚪 خروج</button>
            </div>
        </div>
    </header>
    
    <div class="container">
        <div class="welcome-section">
            <h1 class="welcome-title">مرحباً بك في نظام الأمان المتقدم</h1>
            <p class="welcome-subtitle">إدارة شاملة لأنظمة الأمان والمراقبة للبنك المصري</p>
            <div class="current-time" id="currentTime"></div>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <span class="stat-icon">📹</span>
                <div class="stat-value" id="cameras">24</div>
                <div class="stat-label">كاميرات نشطة</div>
            </div>
            
            <div class="stat-card">
                <span class="stat-icon">🚪</span>
                <div class="stat-value" id="doors">12</div>
                <div class="stat-label">نقاط الوصول</div>
            </div>
            
            <div class="stat-card">
                <span class="stat-icon">🏧</span>
                <div class="stat-value" id="atms">8</div>
                <div class="stat-label">أجهزة الصراف</div>
            </div>
            
            <div class="stat-card">
                <span class="stat-icon">👥</span>
                <div class="stat-value" id="users">156</div>
                <div class="stat-label">المستخدمين النشطين</div>
            </div>
        </div>
        
        <div class="actions-section">
            <h2 class="section-title">الإجراءات السريعة</h2>
            <div class="actions-grid">
                <div class="action-card" onclick="goToPage('cameras.html')">
                    <span class="action-icon">📹</span>
                    <h3 class="action-title">نظام المراقبة</h3>
                    <p class="action-description">مراقبة مباشرة لجميع الكاميرات وإدارة التسجيلات</p>
                </div>
                
                <div class="action-card" onclick="goToPage('access.html')">
                    <span class="action-icon">🔐</span>
                    <h3 class="action-title">التحكم في الوصول</h3>
                    <p class="action-description">إدارة الأبواب والصلاحيات ومراقبة الدخول والخروج</p>
                </div>
                
                <div class="action-card" onclick="goToPage('gps.html')">
                    <span class="action-icon">🛰️</span>
                    <h3 class="action-title">تتبع الصرافات</h3>
                    <p class="action-description">مراقبة مواقع أجهزة الصراف المتنقلة في الوقت الفعلي</p>
                </div>
                
                <div class="action-card" onclick="goToPage('reports.html')">
                    <span class="action-icon">📊</span>
                    <h3 class="action-title">التقارير والتحليلات</h3>
                    <p class="action-description">تقارير شاملة وتحليلات متقدمة للأنشطة الأمنية</p>
                </div>
                
                <div class="action-card" onclick="goToPage('settings.html')">
                    <span class="action-icon">⚙️</span>
                    <h3 class="action-title">الإعدادات</h3>
                    <p class="action-description">إدارة المستخدمين وإعدادات النظام والنسخ الاحتياطي</p>
                </div>
                
                <div class="action-card" onclick="goToPage('recordings_management.html')">
                    <span class="action-icon">💾</span>
                    <h3 class="action-title">إدارة التسجيلات</h3>
                    <p class="action-description">عرض وإدارة جميع تسجيلات المراقبة المحفوظة</p>
                </div>
            </div>
        </div>
        
        <div class="status-section">
            <div class="status-header">
                <h3 class="status-title">حالة النظام</h3>
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span class="status-text">جميع الأنظمة تعمل بشكل طبيعي</span>
                </div>
            </div>
            
            <div class="status-grid">
                <div class="status-item">
                    <span class="status-name">خادم قاعدة البيانات</span>
                    <span class="status-value">متصل</span>
                </div>
                
                <div class="status-item">
                    <span class="status-name">شبكة الكاميرات</span>
                    <span class="status-value">نشطة</span>
                </div>
                
                <div class="status-item">
                    <span class="status-name">نظام التنبيهات</span>
                    <span class="status-value">يعمل</span>
                </div>
                
                <div class="status-item">
                    <span class="status-name">النسخ الاحتياطي</span>
                    <span class="status-value">محدث</span>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Check authentication
        function checkAuth() {
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            if (!isLoggedIn || isLoggedIn !== 'true') {
                window.location.href = 'login.html';
                return false;
            }
            return true;
        }
        
        // Load user info
        function loadUserInfo() {
            const username = localStorage.getItem('username') || 'المستخدم';
            document.getElementById('userName').textContent = username;
        }
        
        // Update time
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-EG');
            document.getElementById('currentTime').textContent = timeString;
        }
        
        // Update statistics
        function updateStats() {
            const stats = {
                cameras: Math.floor(Math.random() * 3) + 22,
                doors: 12,
                atms: Math.floor(Math.random() * 2) + 7,
                users: Math.floor(Math.random() * 10) + 150
            };
            
            Object.keys(stats).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.textContent = stats[key];
                }
            });
        }
        
        // Navigate to page
        function goToPage(url) {
            console.log('Navigating to:', url);
            window.location.href = url;
        }
        
        // Logout
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('username');
                localStorage.removeItem('loginTime');
                window.location.href = 'login.html';
            }
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Home page loading...');
            
            if (!checkAuth()) {
                return;
            }
            
            loadUserInfo();
            updateTime();
            updateStats();
            
            // Update time every second
            setInterval(updateTime, 1000);
            
            // Update stats every 30 seconds
            setInterval(updateStats, 30000);
            
            console.log('Home page loaded successfully');
        });
    </script>
</body>
</html>
