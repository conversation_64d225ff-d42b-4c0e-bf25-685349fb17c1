<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - الصفحة الرئيسية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(26, 54, 93, 0.9);
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(45deg, #1a365d, #2c5282);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .logo-text {
            font-size: 28px;
            font-weight: 900;
            color: white;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logout-btn {
            background: #e53e3e;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
        }
        
        .logout-btn:hover {
            background: #c53030;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .welcome-section {
            text-align: center;
            margin-bottom: 50px;
        }
        
        .welcome-title {
            font-size: 42px;
            font-weight: 900;
            margin-bottom: 15px;
            color: #3182ce;
        }
        
        .welcome-subtitle {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 20px;
        }
        
        .current-time {
            font-size: 20px;
            color: #4299e1;
            font-weight: 600;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 50px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            border-color: #1a365d;
            box-shadow: 0 15px 30px rgba(26, 54, 93, 0.3);
        }
        
        .stat-icon {
            font-size: 40px;
            margin-bottom: 15px;
            display: block;
        }
        
        .stat-value {
            font-size: 32px;
            font-weight: 900;
            color: #3182ce;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .actions-section {
            margin-bottom: 50px;
        }
        
        .section-title {
            font-size: 28px;
            font-weight: 700;
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }
        
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
        }
        
        .action-card {
            background: rgba(26, 54, 93, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .action-card:hover {
            transform: translateY(-5px);
            border-color: #2c5282;
            box-shadow: 0 15px 30px rgba(26, 54, 93, 0.4);
        }
        
        .action-icon {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }
        
        .action-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 10px;
            color: white;
        }
        
        .action-description {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.5;
        }
        
        .status-section {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
        }
        
        .status-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .status-title {
            font-size: 20px;
            font-weight: 700;
            color: white;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #48bb78;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .status-text {
            color: #48bb78;
            font-weight: 600;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }
        
        .status-name {
            color: rgba(255, 255, 255, 0.8);
        }
        
        .status-value {
            color: #3182ce;
            font-weight: 600;
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }
            
            .container {
                padding: 20px 15px;
            }
            
            .welcome-title {
                font-size: 28px;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .actions-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">🏦</div>
                <div class="logo-text">EG BANK</div>
            </div>
            
            <div class="user-info">
                <div>
                    <div style="font-weight: 600;" id="userName">المستخدم</div>
                    <div style="font-size: 14px; color: rgba(255,255,255,0.7);" id="userRole">مدير النظام</div>
                </div>
                <button class="logout-btn" onclick="logout()">🚪 خروج</button>
            </div>
        </div>
    </header>
    
    <div class="container">
        <div class="welcome-section">
            <h1 class="welcome-title">Welcome EG BANK</h1>
            <p class="welcome-subtitle">System Control</p>
            <div class="current-time" id="currentTime"></div>
        </div>
        
        <div class="stats-grid">


            <div class="stat-card">
                <span class="stat-icon">📊</span>
                <div class="stat-value" id="reports">0</div>
                <div class="stat-label">التقارير الشهرية</div>
            </div>
        </div>
        
        <div class="actions-section">
            <h2 class="section-title">الإجراءات السريعة</h2>
            <div class="actions-grid">
                <div class="action-card" onclick="goToPage('cameras.html')">
                    <span class="action-icon">📹</span>
                    <h3 class="action-title">نظام المراقبة</h3>
                    <p class="action-description">مراقبة مباشرة لجميع الكاميرات وإدارة التسجيلات</p>
                </div>

                <div class="action-card" onclick="goToPage('smartvoice_attendance.html')">
                    <span class="action-icon">🎤</span>
                    <h3 class="action-title">SmartVoice Attendance</h3>
                    <p class="action-description">نظام الحضور والانصراف بالتعرف على الصوت</p>
                </div>

                <div class="action-card" onclick="goToPage('security_tasks.html')">
                    <span class="action-icon">🔧</span>
                    <h3 class="action-title">تسجيل الأعمال اليومية</h3>
                    <p class="action-description">إدارة مهام الأنظمة الأمنية والصيانة مع التقارير</p>
                </div>

                <div class="action-card" onclick="goToPage('fire_alarm_system.html')">
                    <span class="action-icon">🔥</span>
                    <h3 class="action-title">نظام إنذار الحريق</h3>
                    <p class="action-description">نظام متقدم لاكتشاف الحريق والإنذار المبكر</p>
                </div>

                <div class="action-card" onclick="goToPage('laptop_camera.html')">
                    <span class="action-icon">📷</span>
                    <h3 class="action-title">كاميرا اللاب توب</h3>
                    <p class="action-description">التقاط الصور والفيديو من كاميرا الجهاز</p>
                </div>

                <div class="action-card" onclick="goToPage('hikvision_systems.html')">
                    <span class="action-icon">🎯</span>
                    <h3 class="action-title">أنظمة هيك فيجن</h3>
                    <p class="action-description">حلول أمنية متكاملة ومتقدمة للمراقبة والحماية</p>
                </div>
                

                
                <div class="action-card" onclick="goToPage('gps.html')">
                    <span class="action-icon">🛰️</span>
                    <h3 class="action-title">تتبع الصرافات</h3>
                    <p class="action-description">مراقبة مواقع أجهزة الصراف المتنقلة في الوقت الفعلي</p>
                </div>
                
                <div class="action-card" onclick="goToPage('reports.html')">
                    <span class="action-icon">📊</span>
                    <h3 class="action-title">التقارير والتحليلات</h3>
                    <p class="action-description">تقارير شاملة وتحليلات متقدمة للأنشطة الأمنية</p>
                </div>
                

                <div class="action-card" onclick="goToPage('recordings_management.html')">
                    <span class="action-icon">💾</span>
                    <h3 class="action-title">إدارة التسجيلات</h3>
                    <p class="action-description">عرض وإدارة جميع تسجيلات المراقبة المحفوظة</p>
                </div>
            </div>
        </div>
        
        <div class="status-section">
            <div class="status-header">
                <h3 class="status-title">حالة النظام</h3>
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span class="status-text">جميع الأنظمة تعمل بشكل طبيعي</span>
                </div>
            </div>
            
            <div class="status-grid">
                <div class="status-item">
                    <span class="status-name">خادم قاعدة البيانات</span>
                    <span class="status-value">متصل</span>
                </div>
                
                <div class="status-item">
                    <span class="status-name">شبكة الكاميرات</span>
                    <span class="status-value">نشطة</span>
                </div>
                
                <div class="status-item">
                    <span class="status-name">نظام التنبيهات</span>
                    <span class="status-value">يعمل</span>
                </div>
                
                <div class="status-item">
                    <span class="status-name">النسخ الاحتياطي</span>
                    <span class="status-value">محدث</span>
                </div>
            </div>
        </div>
    </div>
    
    <script src="app_core.js"></script>
    <script>
        // Wait for EG BANK system to be ready
        document.addEventListener('egbankReady', function() {
            console.log('EG BANK system ready, initializing home page');
            initializeHomePage();
        });

        // Initialize home page
        function initializeHomePage() {
            if (!window.egbank.isAuthenticated()) {
                console.log('User not authenticated, redirecting to login');
                window.location.href = 'login.html';
                return;
            }

            loadUserInfo();
            updateTime();
            updateStats();

            // Update time every second
            setInterval(updateTime, 1000);

            // Update stats every 30 seconds
            setInterval(updateStats, 30000);

            // Log page access
            window.egbank.logAction('page_access', 'دخول إلى الصفحة الرئيسية');

            console.log('Home page initialized successfully');
        }
        
        // Load user info
        function loadUserInfo() {
            try {
                const currentUser = window.egbank.getCurrentUser();
                const userNameElement = document.getElementById('userName');
                const userRoleElement = document.getElementById('userRole');

                if (currentUser) {
                    if (userNameElement) {
                        userNameElement.textContent = currentUser.fullName || currentUser.username;
                    }
                    if (userRoleElement) {
                        userRoleElement.textContent = getRoleText(currentUser.role);
                    }
                } else {
                    if (userNameElement) userNameElement.textContent = 'المستخدم';
                    if (userRoleElement) userRoleElement.textContent = 'مستخدم';
                }
            } catch (error) {
                console.error('Error loading user info:', error);
            }
        }

        // Get role text in Arabic
        function getRoleText(role) {
            const roleMap = {
                admin: 'مدير النظام',
                operator: 'مشغل',
                viewer: 'مشاهد',
                security: 'أمان'
            };
            return roleMap[role] || 'مستخدم';
        }

        // Update time
        function updateTime() {
            try {
                const now = new Date();
                const timeString = now.toLocaleString('ar-EG', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });

                const timeElement = document.getElementById('currentTime');
                if (timeElement) {
                    timeElement.textContent = timeString;
                }
            } catch (error) {
                console.error('Error updating time:', error);
                const timeElement = document.getElementById('currentTime');
                if (timeElement) {
                    timeElement.textContent = new Date().toLocaleString();
                }
            }
        }

        // Update statistics
        function updateStats() {
            try {
                const systemStats = window.egbank.getStats();

                const stats = {
                    reports: Math.floor(new Date().getMonth()) + 1
                };

                Object.keys(stats).forEach(key => {
                    const element = document.getElementById(key);
                    if (element) {
                        animateCounter(element, stats[key]);
                    }
                });
            } catch (error) {
                console.error('Error updating stats:', error);
                // Fallback to static values
                const fallbackStats = {
                    reports: 0
                };

                Object.keys(fallbackStats).forEach(key => {
                    const element = document.getElementById(key);
                    if (element) {
                        element.textContent = fallbackStats[key];
                    }
                });
            }
        }

        // Animate counter
        function animateCounter(element, targetValue) {
            const currentValue = parseInt(element.textContent) || 0;
            if (currentValue === targetValue) return;

            const increment = targetValue > currentValue ? 1 : -1;
            const steps = Math.abs(targetValue - currentValue);
            const stepDuration = Math.min(1000 / steps, 100);

            let current = currentValue;
            const timer = setInterval(() => {
                current += increment;
                element.textContent = current;

                if (current === targetValue) {
                    clearInterval(timer);
                }
            }, stepDuration);
        }
        
        // Navigate to page
        function goToPage(url) {
            try {
                console.log('Navigating to:', url);
                window.egbank.logAction('navigation', `التنقل إلى: ${url}`);
                window.location.href = url;
            } catch (error) {
                console.error('Navigation error:', error);
                window.location.href = url;
            }
        }

        // Logout
        function logout() {
            try {
                if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                    window.egbank.logout();
                }
            } catch (error) {
                console.error('Logout error:', error);
                // Fallback logout
                localStorage.clear();
                window.location.href = 'login.html';
            }
        }

        // Show welcome notification
        function showWelcomeNotification() {
            try {
                const currentUser = window.egbank.getCurrentUser();
                if (currentUser) {
                    setTimeout(() => {
                        window.egbank.showNotification(
                            `مرحباً ${currentUser.fullName || currentUser.username}! تم تسجيل دخولك بنجاح`,
                            'success',
                            4000
                        );
                    }, 1000);
                }
            } catch (error) {
                console.error('Error showing welcome notification:', error);
            }
        }

        // Initialize page when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Home page DOM loaded...');

            // If EG BANK system is already ready, initialize immediately
            if (window.EGBankApp && window.EGBankApp.isInitialized) {
                initializeHomePage();
                showWelcomeNotification();
            }
        });
    </script>
</body>
</html>
