<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - System Control</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        
        .splash-container {
            text-align: center;
            max-width: 600px;
            padding: 40px;
            animation: fadeIn 1s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .logo-section {
            margin-bottom: 40px;
        }
        
        .main-logo {
            width: 120px;
            height: 120px;
            background: linear-gradient(45deg, #1a365d, #2c5282);
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 64px;
            margin: 0 auto 20px;
            box-shadow: 0 20px 40px rgba(26, 54, 93, 0.4);
            animation: logoFloat 3s ease-in-out infinite;
        }
        
        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .main-title {
            font-size: 48px;
            font-weight: 900;
            margin-bottom: 15px;
            color: #3182ce;
            text-shadow: 0 0 20px rgba(49, 130, 206, 0.5);
        }
        
        .subtitle {
            font-size: 20px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 40px;
        }
        
        .loading-section {
            margin-bottom: 40px;
        }
        
        .loading-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .loading-progress {
            height: 100%;
            background: linear-gradient(90deg, #1a365d, #2c5282, #3182ce);
            border-radius: 3px;
            width: 0%;
            animation: loadingProgress 3s ease-in-out forwards;
        }
        
        @keyframes loadingProgress {
            0% { width: 0%; }
            100% { width: 100%; }
        }
        
        .loading-text {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 10px;
        }
        
        .loading-percentage {
            font-size: 24px;
            font-weight: 700;
            color: #3182ce;
        }
        
        .features-list {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }
        
        .feature-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }
        
        .feature-icon {
            font-size: 24px;
        }
        
        .feature-text {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .continue-btn {
            background: linear-gradient(135deg, #1a365d, #2c5282);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 10px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            opacity: 0;
            animation: fadeInButton 0.5s ease 3.5s forwards;
        }
        
        .continue-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(26, 54, 93, 0.4);
        }
        
        @keyframes fadeInButton {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .version-info {
            position: fixed;
            bottom: 20px;
            right: 20px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.5);
        }
        
        @media (max-width: 768px) {
            .splash-container {
                padding: 20px;
            }
            
            .main-title {
                font-size: 32px;
            }
            
            .features-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="splash-container">
        <div class="logo-section">
            <div class="main-logo">🏦</div>
            <h1 class="main-title">EG BANK</h1>
            <p class="subtitle">System Control</p>
        </div>
        
        <div class="loading-section">
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
            <div class="loading-text" id="loadingText">جاري تحميل النظام...</div>
            <div class="loading-percentage" id="loadingPercentage">0%</div>
        </div>
        
        <div class="features-list">
            <div class="feature-item">
                <span class="feature-icon">📹</span>
                <span class="feature-text">نظام مراقبة متطور</span>
            </div>
            <div class="feature-item">
                <span class="feature-icon">🔐</span>
                <span class="feature-text">تحكم أمني شامل</span>
            </div>
            <div class="feature-item">
                <span class="feature-icon">🛰️</span>
                <span class="feature-text">تتبع GPS دقيق</span>
            </div>
            <div class="feature-item">
                <span class="feature-icon">📊</span>
                <span class="feature-text">تقارير تحليلية</span>
            </div>
        </div>
        
        <button class="continue-btn" onclick="enterSystem()">
            دخول النظام
        </button>
    </div>
    
    <div class="version-info">
        الإصدار 2.0 - 2024
    </div>
    
    <!-- Load EG BANK Core System -->
    <script src="app_core.js"></script>
    
    <script>
        console.log('🏦 EG BANK System Starting...');
        
        // Loading animation
        let progress = 0;
        const loadingTexts = [
            'جاري تحميل النظام...',
            'تحميل قاعدة البيانات...',
            'تهيئة أنظمة الأمان...',
            'فحص الاتصالات...',
            'تحميل واجهة المستخدم...',
            'النظام جاهز!'
        ];
        
        function updateLoading() {
            try {
                const loadingText = document.getElementById('loadingText');
                const loadingPercentage = document.getElementById('loadingPercentage');
                
                if (!loadingText || !loadingPercentage) {
                    console.error('Loading elements not found');
                    return;
                }
                
                progress += Math.random() * 20 + 5;
                if (progress > 100) progress = 100;
                
                loadingPercentage.textContent = Math.floor(progress) + '%';
                
                const textIndex = Math.floor((progress / 100) * (loadingTexts.length - 1));
                loadingText.textContent = loadingTexts[textIndex];
                
                if (progress < 100) {
                    setTimeout(updateLoading, 300 + Math.random() * 500);
                } else {
                    console.log('✅ Loading complete');
                }
            } catch (error) {
                console.error('Loading update error:', error);
            }
        }
        
        // Enter system function
        function enterSystem() {
            try {
                console.log('🚀 Entering system...');
                
                // Check if EG BANK system is available and user is authenticated
                if (window.egbank && window.egbank.isAuthenticated()) {
                    console.log('✅ User already authenticated, going to home');
                    window.location.href = 'home_simple.html';
                } else {
                    console.log('🔑 User not authenticated, going to login');
                    window.location.href = 'login.html';
                }
            } catch (error) {
                console.error('❌ Enter system error:', error);
                // Fallback check
                const isLoggedIn = localStorage.getItem('isLoggedIn');
                if (isLoggedIn === 'true') {
                    window.location.href = 'home_simple.html';
                } else {
                    window.location.href = 'login.html';
                }
            }
        }
        
        // Wait for EG BANK system to be ready
        document.addEventListener('egbankReady', function() {
            console.log('🎯 EG BANK system ready');
            
            // Auto-check authentication after system is ready
            setTimeout(() => {
                if (window.egbank.isAuthenticated()) {
                    console.log('🔄 Auto-redirecting authenticated user');
                    window.location.href = 'home_simple.html';
                }
            }, 1000);
        });
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 DOM loaded, starting initialization...');
            
            try {
                // Start loading animation
                setTimeout(updateLoading, 500);
                
                // Auto-redirect after loading completes
                setTimeout(() => {
                    const continueBtn = document.querySelector('.continue-btn');
                    if (continueBtn) {
                        continueBtn.style.animation = 'pulse 1s infinite';
                        continueBtn.style.boxShadow = '0 0 20px rgba(26, 54, 93, 0.6)';
                        
                        // Add pulse animation
                        const style = document.createElement('style');
                        style.textContent = `
                            @keyframes pulse {
                                0%, 100% { transform: scale(1); }
                                50% { transform: scale(1.05); }
                            }
                        `;
                        document.head.appendChild(style);
                    }
                }, 4000);
                
                console.log('✅ Initialization complete');
            } catch (error) {
                console.error('❌ Initialization error:', error);
            }
        });
        
        console.log('🏦 EG BANK System Splash Screen Loaded');
    </script>
</body>
</html>
