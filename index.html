<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK Surveillance</title>
    <link rel="stylesheet" href="style.css">
    <style>
        #loading-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #121212;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .loader {
            border: 16px solid #301934; /* Dark purple */
            border-top: 16px solid #4B0082; /* Darker purple */
            border-radius: 50%;
            width: 120px;
            height: 120px;
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div id="loading-container">
        <div class="loader"></div>
    </div>
    <script>
        window.addEventListener('load', function() {
            document.getElementById('loading-container').style.display = 'none';
        });
    </script>
    <!-- Banking Header -->
    <header class="bank-header">
        <div class="bank-logo">EG BANK</div>
        <nav class="bank-nav">
            <ul>
                <li><a href="index.html">Dashboard</a></li>
                <li><a href="cameras.html">Security</a></li>
                <li><a href="access.html">Access Control</a></li>
                <li><a href="reports.html">Reports</a></li>
                <li><a href="settings.html">Settings</a></li>
                <li><a href="gps.html">GPS Tracking</a></li>
                <li><a href="login.html">Login</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main Content Area -->
    <div class="bank-container">
        <!-- Sidebar -->
        <aside class="bank-sidebar">
            <h3>Abdel Fattah Mahmoud</h3>
            <ul>
                <li class="active"><a href="index.html" style="color: white;">Camera Surveillance</a></li>
                <li><a href="cameras.html" style="color: white;">IP Camera Management</a></li>
                <li style="color: white;">Access Logs</li>
                <li style="color: white;">Alarm System</li>
            </ul>
        </aside>

        <!-- Camera Section -->
        <main class="bank-main">
            <h1 style="color: white;">Branch Surveillance System</h1>
            <div class="camera-container">
                <video id="video" autoplay playsinline></video>
                <div class="controls">
                    <button id="startBtn" style="color: white;">Activate Camera</button>
                    <button id="recordBtn" disabled style="color: white;">Begin Recording</button>
                    <button id="stopBtn" disabled style="color: white;">Stop Recording</button>
                    <button id="downloadBtn" disabled style="color: white;">Download Footage</button>
                </div>
                <div id="recordings"></div>
            </div>
        </main>
    </div>

    <!-- Banking Footer -->
    <footer class="bank-footer">
    </footer>

    <script src="app.js"></script>
    <script>
        document.getElementById('closeBtn').addEventListener('click', function() {
            window.location.href = 'login.html';
        });
    </script>
</body>
</html>