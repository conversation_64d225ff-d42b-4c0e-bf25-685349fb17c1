<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - جدول الحضور والانصراف</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(26, 54, 93, 0.9);
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(45deg, #1a365d, #2c5282);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .logo-text {
            font-size: 28px;
            font-weight: 900;
            color: white;
        }
        
        .nav-buttons {
            display: flex;
            gap: 15px;
        }
        
        .nav-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
        }
        
        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .page-title {
            text-align: center;
            font-size: 36px;
            font-weight: 900;
            margin-bottom: 40px;
            color: #3182ce;
            text-shadow: 0 0 20px rgba(49, 130, 206, 0.5);
        }
        
        .controls-panel {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(20px);
        }
        
        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .control-label {
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
            font-size: 14px;
        }
        
        .control-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 12px;
            color: white;
            font-size: 14px;
        }
        
        .control-input:focus {
            outline: none;
            border-color: #3182ce;
            box-shadow: 0 0 0 2px rgba(49, 130, 206, 0.2);
        }
        
        .control-input option {
            background: #1a1a2e;
            color: white;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .action-btn {
            background: linear-gradient(135deg, #1a365d, #2c5282);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(26, 54, 93, 0.4);
        }
        
        .add-btn {
            background: linear-gradient(135deg, #48bb78, #38a169);
        }
        
        .export-btn {
            background: linear-gradient(135deg, #ed8936, #dd6b20);
        }
        
        .print-btn {
            background: linear-gradient(135deg, #9f7aea, #805ad5);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            backdrop-filter: blur(20px);
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }
        
        .stat-icon {
            font-size: 32px;
            margin-bottom: 10px;
            display: block;
        }
        
        .stat-value {
            font-size: 28px;
            font-weight: 900;
            color: #3182ce;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .table-container {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(20px);
            overflow: hidden;
        }
        
        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .table-title {
            font-size: 24px;
            font-weight: 700;
            color: white;
        }
        
        .search-box {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 10px 15px;
            color: white;
            font-size: 14px;
            width: 300px;
        }
        
        .search-box:focus {
            outline: none;
            border-color: #3182ce;
            box-shadow: 0 0 0 2px rgba(49, 130, 206, 0.2);
        }
        
        .attendance-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: rgba(255, 255, 255, 0.02);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .attendance-table th,
        .attendance-table td {
            padding: 15px 12px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .attendance-table th {
            background: linear-gradient(135deg, #1a365d, #2c5282);
            color: white;
            font-weight: 600;
            font-size: 14px;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .attendance-table td {
            color: rgba(255, 255, 255, 0.9);
            font-size: 13px;
        }
        
        .attendance-table tbody tr {
            transition: all 0.3s ease;
        }
        
        .attendance-table tbody tr:hover {
            background: rgba(255, 255, 255, 0.05);
            transform: scale(1.01);
        }
        
        .status-present {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 600;
        }
        
        .status-absent {
            background: linear-gradient(135deg, #f56565, #e53e3e);
            color: white;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 600;
        }
        
        .status-late {
            background: linear-gradient(135deg, #ed8936, #dd6b20);
            color: white;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 600;
        }
        
        .status-checkout {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 600;
        }
        
        .action-cell {
            display: flex;
            gap: 5px;
            justify-content: center;
            align-items: center;
        }
        
        .table-action-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 6px 10px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.3s ease;
        }
        
        .table-action-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.05);
        }
        
        .edit-btn {
            background: linear-gradient(135deg, #f6e05e, #d69e2e);
            color: black;
        }
        
        .delete-btn {
            background: linear-gradient(135deg, #f56565, #e53e3e);
        }
        
        .view-btn {
            background: linear-gradient(135deg, #4299e1, #3182ce);
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
        }
        
        .page-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        
        .page-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .page-btn.active {
            background: #3182ce;
            border-color: #3182ce;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
        }
        
        .modal-content {
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            margin: 3% auto;
            padding: 30px;
            border-radius: 20px;
            width: 90%;
            max-width: 600px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            max-height: 90vh;
            overflow-y: auto;
            animation: modalSlideIn 0.3s ease-out;
        }
        
        @keyframes modalSlideIn {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .modal-title {
            font-size: 24px;
            font-weight: 700;
            color: #3182ce;
        }
        
        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 28px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .close-btn:hover {
            color: #f56565;
            transform: scale(1.1);
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 12px;
            color: white;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #3182ce;
            box-shadow: 0 0 0 2px rgba(49, 130, 206, 0.2);
            background: rgba(255, 255, 255, 0.15);
        }
        
        .save-btn {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
            font-size: 16px;
        }
        
        .save-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
        }
        
        @media (max-width: 768px) {
            .controls-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .search-box {
                width: 100%;
                margin-bottom: 15px;
            }
            
            .table-header {
                flex-direction: column;
                gap: 15px;
            }
            
            .attendance-table {
                font-size: 11px;
            }
            
            .attendance-table th,
            .attendance-table td {
                padding: 8px 6px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">🏦</div>
                <div class="logo-text">EG BANK</div>
            </div>
            
            <div class="nav-buttons">
                <a href="attendance_advanced.html" class="nav-btn">⏰ نظام الحضور</a>
                <a href="attendance_reports.html" class="nav-btn">📊 التقارير</a>
                <a href="home_simple.html" class="nav-btn">🏠 الرئيسية</a>
            </div>
        </div>
    </header>
    
    <div class="container">
        <h1 class="page-title">📋 جدول الحضور والانصراف</h1>
        
        <!-- Controls Panel -->
        <div class="controls-panel">
            <div class="controls-grid">
                <div class="control-group">
                    <label class="control-label">من تاريخ:</label>
                    <input type="date" class="control-input" id="startDate">
                </div>
                
                <div class="control-group">
                    <label class="control-label">إلى تاريخ:</label>
                    <input type="date" class="control-input" id="endDate">
                </div>
                
                <div class="control-group">
                    <label class="control-label">القسم:</label>
                    <select class="control-input" id="departmentFilter">
                        <option value="">جميع الأقسام</option>
                        <option value="إدارة">إدارة</option>
                        <option value="محاسبة">محاسبة</option>
                        <option value="خدمة عملاء">خدمة عملاء</option>
                        <option value="أمان">أمان</option>
                        <option value="تقنية معلومات">تقنية معلومات</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label class="control-label">الحالة:</label>
                    <select class="control-input" id="statusFilter">
                        <option value="">جميع الحالات</option>
                        <option value="present">حاضر</option>
                        <option value="absent">غائب</option>
                        <option value="late">متأخر</option>
                        <option value="checkout">منصرف</option>
                    </select>
                </div>
            </div>
            
            <div class="action-buttons">
                <button class="action-btn add-btn" onclick="openAddRecordModal()">
                    ➕ إضافة سجل
                </button>
                <button class="action-btn" onclick="filterTable()">
                    🔍 تطبيق الفلتر
                </button>
                <button class="action-btn export-btn" onclick="exportTable()">
                    📤 تصدير Excel
                </button>
                <button class="action-btn print-btn" onclick="printTable()">
                    🖨️ طباعة
                </button>
                <button class="action-btn" onclick="refreshTable()">
                    🔄 تحديث
                </button>
            </div>
        </div>
        
        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <span class="stat-icon">👥</span>
                <div class="stat-value" id="totalEmployees">0</div>
                <div class="stat-label">إجمالي الموظفين</div>
            </div>
            
            <div class="stat-card">
                <span class="stat-icon">✅</span>
                <div class="stat-value" id="presentToday">0</div>
                <div class="stat-label">الحاضرين اليوم</div>
            </div>
            
            <div class="stat-card">
                <span class="stat-icon">❌</span>
                <div class="stat-value" id="absentToday">0</div>
                <div class="stat-label">الغائبين اليوم</div>
            </div>
            
            <div class="stat-card">
                <span class="stat-icon">⏰</span>
                <div class="stat-value" id="lateToday">0</div>
                <div class="stat-label">المتأخرين اليوم</div>
            </div>
        </div>
        
        <!-- Table Container -->
        <div class="table-container">
            <div class="table-header">
                <h2 class="table-title">📊 سجلات الحضور والانصراف</h2>
                <input type="text" class="search-box" id="searchBox" placeholder="🔍 البحث في السجلات...">
            </div>
            
            <div style="overflow-x: auto;">
                <table class="attendance-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>الموظف</th>
                            <th>الرقم الوظيفي</th>
                            <th>القسم</th>
                            <th>التاريخ</th>
                            <th>وقت الحضور</th>
                            <th>وقت الانصراف</th>
                            <th>ساعات العمل</th>
                            <th>التأخير</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="attendanceTableBody">
                        <!-- Data will be populated here -->
                    </tbody>
                </table>
            </div>
            
            <div class="pagination" id="pagination">
                <!-- Pagination will be populated here -->
            </div>
        </div>
    </div>
    
    <!-- Add/Edit Record Modal -->
    <div id="recordModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">إضافة سجل حضور</h3>
                <button class="close-btn" onclick="closeRecordModal()">&times;</button>
            </div>
            
            <form id="recordForm">
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">الموظف:</label>
                        <select class="form-input" id="employeeSelect" required>
                            <option value="">اختر الموظف</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">التاريخ:</label>
                        <input type="date" class="form-input" id="recordDate" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">وقت الحضور:</label>
                        <input type="time" class="form-input" id="checkinTime">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">وقت الانصراف:</label>
                        <input type="time" class="form-input" id="checkoutTime">
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">ملاحظات:</label>
                    <textarea class="form-input" id="recordNotes" rows="3" placeholder="أي ملاحظات إضافية..."></textarea>
                </div>
                
                <button type="submit" class="save-btn">💾 حفظ السجل</button>
            </form>
        </div>
    </div>
    
    <script src="app_core.js"></script>
    <script src="attendance_table.js"></script>
</body>
</html>
