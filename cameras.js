// TODO: Add database connection and CRUD operations here
// Camera Management Script

document.addEventListener('DOMContentLoaded', () => {
    const cameraForm = document.getElementById('cameraForm');
    const addBtn = document.getElementById('addBtn');
    const editBtn = document.getElementById('editBtn');
    const deleteBtn = document.getElementById('deleteBtn');
    const cameraTable = document.getElementById('cameraTable').getElementsByTagName('tbody')[0];
    
    let cameras = JSON.parse(localStorage.getItem('cameras')) || [];
    let editingIndex = -1;
    
    // Load existing cameras
    renderCameras();
    
    // Add new camera
    addBtn.addEventListener('click', () => {
        const formData = new FormData(cameraForm);
        const camera = Object.fromEntries(formData.entries());
        
        if (editingIndex === -1) {
            cameras.push(camera);
        } else {
            cameras[editingIndex] = camera;
            editingIndex = -1;
            editBtn.disabled = true;
            deleteBtn.disabled = true;
            addBtn.textContent = 'Add Camera';
        }
        
        saveCameras();
        renderCameras();
        cameraForm.reset();
    });
    
    // Edit camera
    editBtn.addEventListener('click', () => {
        if (editingIndex !== -1) {
            const formData = new FormData(cameraForm);
            const camera = Object.fromEntries(formData.entries());
            
            cameras[editingIndex] = camera;
            saveCameras();
            renderCameras();
            cameraForm.reset();
            
            editingIndex = -1;
            editBtn.disabled = true;
            deleteBtn.disabled = true;
            addBtn.textContent = 'Add Camera';
        }
    });
    
    // Delete camera
    deleteBtn.addEventListener('click', () => {
        if (editingIndex !== -1) {
            cameras.splice(editingIndex, 1);
            saveCameras();
            renderCameras();
            cameraForm.reset();
            
            editingIndex = -1;
            editBtn.disabled = true;
            deleteBtn.disabled = true;
            addBtn.textContent = 'Add Camera';
        }
    });
    
    // Render cameras table
    function renderCameras() {
        cameraTable.innerHTML = '';
        cameras.forEach((camera, index) => {
            const row = cameraTable.insertRow();
            row.innerHTML = `
                <td>${camera.ipNvr}</td>
                <td>${camera.ipCamera}</td>
                <td>${camera.address}</td>
                <td>${camera.port}</td>
                <td>${camera.url}</td>
                <td>
                    <button class="edit" data-index="${index}">Edit</button>
                </td>
            `;
        });
        
        // Add event listeners to edit buttons
        document.querySelectorAll('.edit').forEach(button => {
            button.addEventListener('click', (e) => {
                const index = parseInt(e.target.dataset.index);
                const camera = cameras[index];
                
                // Fill form
                document.getElementById('ipNvr').value = camera.ipNvr;
                document.getElementById('ipCamera').value = camera.ipCamera;
                document.getElementById('address').value = camera.address;
                document.getElementById('port').value = camera.port;
                document.getElementById('url').value = camera.url;
                
                editingIndex = index;
                editBtn.disabled = false;
                deleteBtn.disabled = false;
                addBtn.textContent = 'Update';
            });
        });
    }
    
    function saveCameras() {
        localStorage.setItem('cameras', JSON.stringify(cameras));
    }
});