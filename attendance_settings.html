<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - إعدادات نظام الحضور والانصراف</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(26, 54, 93, 0.9);
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(45deg, #1a365d, #2c5282);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .logo-text {
            font-size: 28px;
            font-weight: 900;
            color: white;
        }
        
        .nav-buttons {
            display: flex;
            gap: 15px;
        }
        
        .nav-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
        }
        
        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .page-title {
            text-align: center;
            font-size: 36px;
            font-weight: 900;
            margin-bottom: 40px;
            color: #3182ce;
            text-shadow: 0 0 20px rgba(49, 130, 206, 0.5);
        }
        
        .settings-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        
        .settings-panel {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(20px);
        }
        
        .panel-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 25px;
            color: white;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 12px;
            color: white;
            font-size: 14px;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #3182ce;
            box-shadow: 0 0 0 2px rgba(49, 130, 206, 0.2);
        }
        
        .form-checkbox {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .checkbox {
            width: 18px;
            height: 18px;
            accent-color: #3182ce;
        }
        
        .save-btn {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .save-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
        }
        
        .time-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .days-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }
        
        .day-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .employees-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .employee-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            margin-bottom: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .employee-info {
            flex: 1;
        }
        
        .employee-name {
            font-weight: 600;
            color: white;
            margin-bottom: 5px;
        }
        
        .employee-role {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }
        
        .employee-actions {
            display: flex;
            gap: 10px;
        }
        
        .action-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .status-active {
            color: #48bb78;
        }
        
        .status-inactive {
            color: #f56565;
        }
        
        .notification-settings {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .notification-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .notification-item:last-child {
            border-bottom: none;
        }
        
        .toggle-switch {
            position: relative;
            width: 50px;
            height: 24px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .toggle-switch.active {
            background: #3182ce;
        }
        
        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
        }
        
        .toggle-switch.active .toggle-slider {
            transform: translateX(26px);
        }
        
        @media (max-width: 768px) {
            .settings-grid {
                grid-template-columns: 1fr;
            }
            
            .time-grid {
                grid-template-columns: 1fr;
            }
            
            .days-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">🏦</div>
                <div class="logo-text">EG BANK</div>
            </div>
            
            <div class="nav-buttons">
                <a href="attendance.html" class="nav-btn">⏰ الحضور والانصراف</a>
                <a href="home_simple.html" class="nav-btn">🏠 الرئيسية</a>
            </div>
        </div>
    </header>
    
    <div class="container">
        <h1 class="page-title">⚙️ إعدادات نظام الحضور والانصراف</h1>
        
        <div class="settings-grid">
            <!-- Working Hours Settings -->
            <div class="settings-panel">
                <h2 class="panel-title">
                    🕐 إعدادات أوقات العمل
                </h2>
                
                <form id="workingHoursForm">
                    <div class="time-grid">
                        <div class="form-group">
                            <label class="form-label">بداية العمل:</label>
                            <input type="time" class="form-input" id="workStartTime" value="09:00">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">نهاية العمل:</label>
                            <input type="time" class="form-input" id="workEndTime" value="17:00">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">بداية الاستراحة:</label>
                            <input type="time" class="form-input" id="breakStartTime" value="12:00">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">نهاية الاستراحة:</label>
                            <input type="time" class="form-input" id="breakEndTime" value="13:00">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">أيام العمل:</label>
                        <div class="days-grid">
                            <div class="day-item">
                                <input type="checkbox" class="checkbox" id="sunday" checked>
                                <label for="sunday">الأحد</label>
                            </div>
                            <div class="day-item">
                                <input type="checkbox" class="checkbox" id="monday" checked>
                                <label for="monday">الاثنين</label>
                            </div>
                            <div class="day-item">
                                <input type="checkbox" class="checkbox" id="tuesday" checked>
                                <label for="tuesday">الثلاثاء</label>
                            </div>
                            <div class="day-item">
                                <input type="checkbox" class="checkbox" id="wednesday" checked>
                                <label for="wednesday">الأربعاء</label>
                            </div>
                            <div class="day-item">
                                <input type="checkbox" class="checkbox" id="thursday" checked>
                                <label for="thursday">الخميس</label>
                            </div>
                            <div class="day-item">
                                <input type="checkbox" class="checkbox" id="friday">
                                <label for="friday">الجمعة</label>
                            </div>
                            <div class="day-item">
                                <input type="checkbox" class="checkbox" id="saturday">
                                <label for="saturday">السبت</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">فترة السماح للتأخير (بالدقائق):</label>
                        <input type="number" class="form-input" id="lateGracePeriod" value="15" min="0" max="60">
                    </div>
                    
                    <button type="submit" class="save-btn">💾 حفظ إعدادات أوقات العمل</button>
                </form>
            </div>
            
            <!-- Employee Management -->
            <div class="settings-panel">
                <h2 class="panel-title">
                    👥 إدارة الموظفين
                </h2>
                
                <div class="employees-list" id="employeesList">
                    <!-- Employees will be loaded here -->
                </div>
                
                <button class="save-btn" onclick="addNewEmployee()" style="margin-top: 20px;">
                    ➕ إضافة موظف جديد
                </button>
            </div>
            
            <!-- Notification Settings -->
            <div class="settings-panel">
                <h2 class="panel-title">
                    🔔 إعدادات التنبيهات
                </h2>
                
                <div class="notification-settings">
                    <div class="notification-item">
                        <span>تنبيه عند التأخير</span>
                        <div class="toggle-switch active" onclick="toggleNotification(this)">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>
                    
                    <div class="notification-item">
                        <span>تنبيه عند الغياب</span>
                        <div class="toggle-switch active" onclick="toggleNotification(this)">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>
                    
                    <div class="notification-item">
                        <span>تقرير يومي تلقائي</span>
                        <div class="toggle-switch" onclick="toggleNotification(this)">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>
                    
                    <div class="notification-item">
                        <span>تقرير أسبوعي</span>
                        <div class="toggle-switch" onclick="toggleNotification(this)">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>
                </div>
                
                <button class="save-btn" onclick="saveNotificationSettings()" style="margin-top: 20px;">
                    💾 حفظ إعدادات التنبيهات
                </button>
            </div>
            
            <!-- System Settings -->
            <div class="settings-panel">
                <h2 class="panel-title">
                    🔧 إعدادات النظام
                </h2>
                
                <form id="systemSettingsForm">
                    <div class="form-group">
                        <label class="form-label">اسم الشركة:</label>
                        <input type="text" class="form-input" id="companyName" value="البنك المصري - EG BANK">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">المنطقة الزمنية:</label>
                        <select class="form-input" id="timezone">
                            <option value="Africa/Cairo">القاهرة (GMT+2)</option>
                            <option value="Asia/Riyadh">الرياض (GMT+3)</option>
                            <option value="Asia/Dubai">دبي (GMT+4)</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">تنسيق التاريخ:</label>
                        <select class="form-input" id="dateFormat">
                            <option value="dd/mm/yyyy">يوم/شهر/سنة</option>
                            <option value="mm/dd/yyyy">شهر/يوم/سنة</option>
                            <option value="yyyy-mm-dd">سنة-شهر-يوم</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">تنسيق الوقت:</label>
                        <select class="form-input" id="timeFormat">
                            <option value="24">24 ساعة</option>
                            <option value="12">12 ساعة</option>
                        </select>
                    </div>
                    
                    <div class="form-checkbox">
                        <input type="checkbox" class="checkbox" id="autoBackup" checked>
                        <label for="autoBackup">نسخ احتياطي تلقائي</label>
                    </div>
                    
                    <div class="form-checkbox">
                        <input type="checkbox" class="checkbox" id="enableGPS">
                        <label for="enableGPS">تفعيل تتبع الموقع</label>
                    </div>
                    
                    <button type="submit" class="save-btn">💾 حفظ إعدادات النظام</button>
                </form>
            </div>
        </div>
    </div>
    
    <script src="app_core.js"></script>
    <script src="attendance_system.js"></script>
    <script>
        // Initialize settings page
        document.addEventListener('DOMContentLoaded', function() {
            initializeSettingsPage();
        });

        function initializeSettingsPage() {
            loadWorkingHoursSettings();
            loadEmployees();
            loadNotificationSettings();
            loadSystemSettings();
            setupEventListeners();
        }

        function setupEventListeners() {
            // Working hours form
            document.getElementById('workingHoursForm').addEventListener('submit', function(e) {
                e.preventDefault();
                saveWorkingHoursSettings();
            });

            // System settings form
            document.getElementById('systemSettingsForm').addEventListener('submit', function(e) {
                e.preventDefault();
                saveSystemSettings();
            });
        }

        function loadWorkingHoursSettings() {
            const settings = JSON.parse(localStorage.getItem('attendanceSettings') || '{}');

            if (settings.workingHours) {
                document.getElementById('workStartTime').value = settings.workingHours.start || '09:00';
                document.getElementById('workEndTime').value = settings.workingHours.end || '17:00';
                document.getElementById('breakStartTime').value = settings.workingHours.breakStart || '12:00';
                document.getElementById('breakEndTime').value = settings.workingHours.breakEnd || '13:00';
                document.getElementById('lateGracePeriod').value = settings.workingHours.lateGracePeriod || 15;
            }

            if (settings.workingDays) {
                const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
                days.forEach(day => {
                    const checkbox = document.getElementById(day);
                    if (checkbox) {
                        checkbox.checked = settings.workingDays.includes(day);
                    }
                });
            }
        }

        function saveWorkingHoursSettings() {
            const settings = JSON.parse(localStorage.getItem('attendanceSettings') || '{}');

            settings.workingHours = {
                start: document.getElementById('workStartTime').value,
                end: document.getElementById('workEndTime').value,
                breakStart: document.getElementById('breakStartTime').value,
                breakEnd: document.getElementById('breakEndTime').value,
                lateGracePeriod: parseInt(document.getElementById('lateGracePeriod').value)
            };

            const workingDays = [];
            const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
            days.forEach(day => {
                const checkbox = document.getElementById(day);
                if (checkbox && checkbox.checked) {
                    workingDays.push(day);
                }
            });
            settings.workingDays = workingDays;

            localStorage.setItem('attendanceSettings', JSON.stringify(settings));

            if (window.egbank) {
                window.egbank.showNotification('تم حفظ إعدادات أوقات العمل بنجاح', 'success');
                window.egbank.logAction('settings_update', 'تحديث إعدادات أوقات العمل');
            }
        }

        function loadEmployees() {
            const employees = JSON.parse(localStorage.getItem('systemUsers') || '[]');
            const employeesList = document.getElementById('employeesList');

            employeesList.innerHTML = '';

            employees.forEach(employee => {
                const employeeItem = document.createElement('div');
                employeeItem.className = 'employee-item';

                employeeItem.innerHTML = `
                    <div class="employee-info">
                        <div class="employee-name">${employee.fullName || employee.username}</div>
                        <div class="employee-role">${getRoleText(employee.role)} - ${employee.status === 'active' ? 'نشط' : 'غير نشط'}</div>
                    </div>
                    <div class="employee-actions">
                        <button class="action-btn" onclick="editEmployee('${employee.id}')">تعديل</button>
                        <button class="action-btn" onclick="toggleEmployeeStatus('${employee.id}')" style="background: ${employee.status === 'active' ? '#f56565' : '#48bb78'};">
                            ${employee.status === 'active' ? 'إيقاف' : 'تفعيل'}
                        </button>
                    </div>
                `;

                employeesList.appendChild(employeeItem);
            });
        }

        function getRoleText(role) {
            const roleMap = {
                admin: 'مدير النظام',
                operator: 'مشغل',
                viewer: 'مشاهد',
                security: 'أمان'
            };
            return roleMap[role] || role;
        }

        function addNewEmployee() {
            const name = prompt('اسم الموظف الجديد:');
            const username = prompt('اسم المستخدم:');
            const role = prompt('الدور (admin/operator/viewer):') || 'operator';

            if (name && username) {
                const employees = JSON.parse(localStorage.getItem('systemUsers') || '[]');

                if (employees.find(emp => emp.username === username)) {
                    alert('اسم المستخدم موجود بالفعل');
                    return;
                }

                const newEmployee = {
                    id: 'emp_' + Date.now(),
                    username: username,
                    fullName: name,
                    password: 'SHeh#1981',
                    role: role,
                    permissions: ['attendance'],
                    status: 'active',
                    createdAt: new Date().toISOString()
                };

                employees.push(newEmployee);
                localStorage.setItem('systemUsers', JSON.stringify(employees));

                loadEmployees();

                if (window.egbank) {
                    window.egbank.showNotification('تم إضافة الموظف بنجاح', 'success');
                    window.egbank.logAction('employee_added', `إضافة موظف جديد: ${name}`);
                }
            }
        }

        function editEmployee(employeeId) {
            const employees = JSON.parse(localStorage.getItem('systemUsers') || '[]');
            const employee = employees.find(emp => emp.id === employeeId);

            if (employee) {
                const newName = prompt('الاسم الجديد:', employee.fullName);
                const newRole = prompt('الدور الجديد:', employee.role);

                if (newName) {
                    employee.fullName = newName;
                }
                if (newRole) {
                    employee.role = newRole;
                }

                localStorage.setItem('systemUsers', JSON.stringify(employees));
                loadEmployees();

                if (window.egbank) {
                    window.egbank.showNotification('تم تحديث بيانات الموظف', 'success');
                }
            }
        }

        function toggleEmployeeStatus(employeeId) {
            const employees = JSON.parse(localStorage.getItem('systemUsers') || '[]');
            const employee = employees.find(emp => emp.id === employeeId);

            if (employee) {
                employee.status = employee.status === 'active' ? 'inactive' : 'active';
                localStorage.setItem('systemUsers', JSON.stringify(employees));
                loadEmployees();

                if (window.egbank) {
                    const statusText = employee.status === 'active' ? 'تم تفعيل' : 'تم إيقاف';
                    window.egbank.showNotification(`${statusText} الموظف ${employee.fullName}`, 'success');
                }
            }
        }

        function toggleNotification(element) {
            element.classList.toggle('active');
        }

        function saveNotificationSettings() {
            const settings = JSON.parse(localStorage.getItem('attendanceSettings') || '{}');

            const notifications = {};
            const toggles = document.querySelectorAll('.toggle-switch');

            toggles.forEach((toggle, index) => {
                const labels = ['lateAlert', 'absentAlert', 'dailyReport', 'weeklyReport'];
                notifications[labels[index]] = toggle.classList.contains('active');
            });

            settings.notifications = notifications;
            localStorage.setItem('attendanceSettings', JSON.stringify(settings));

            if (window.egbank) {
                window.egbank.showNotification('تم حفظ إعدادات التنبيهات', 'success');
            }
        }

        function loadNotificationSettings() {
            const settings = JSON.parse(localStorage.getItem('attendanceSettings') || '{}');

            if (settings.notifications) {
                const toggles = document.querySelectorAll('.toggle-switch');
                const labels = ['lateAlert', 'absentAlert', 'dailyReport', 'weeklyReport'];

                toggles.forEach((toggle, index) => {
                    if (settings.notifications[labels[index]]) {
                        toggle.classList.add('active');
                    } else {
                        toggle.classList.remove('active');
                    }
                });
            }
        }

        function loadSystemSettings() {
            const settings = JSON.parse(localStorage.getItem('attendanceSettings') || '{}');

            if (settings.system) {
                document.getElementById('companyName').value = settings.system.companyName || 'البنك المصري - EG BANK';
                document.getElementById('timezone').value = settings.system.timezone || 'Africa/Cairo';
                document.getElementById('dateFormat').value = settings.system.dateFormat || 'dd/mm/yyyy';
                document.getElementById('timeFormat').value = settings.system.timeFormat || '24';
                document.getElementById('autoBackup').checked = settings.system.autoBackup !== false;
                document.getElementById('enableGPS').checked = settings.system.enableGPS === true;
            }
        }

        function saveSystemSettings() {
            const settings = JSON.parse(localStorage.getItem('attendanceSettings') || '{}');

            settings.system = {
                companyName: document.getElementById('companyName').value,
                timezone: document.getElementById('timezone').value,
                dateFormat: document.getElementById('dateFormat').value,
                timeFormat: document.getElementById('timeFormat').value,
                autoBackup: document.getElementById('autoBackup').checked,
                enableGPS: document.getElementById('enableGPS').checked
            };

            localStorage.setItem('attendanceSettings', JSON.stringify(settings));

            if (window.egbank) {
                window.egbank.showNotification('تم حفظ إعدادات النظام بنجاح', 'success');
                window.egbank.logAction('system_settings_update', 'تحديث إعدادات النظام');
            }
        }
    </script>
</body>
</html>
