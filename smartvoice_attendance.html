<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - SmartVoice Attendance</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(26, 54, 93, 0.9);
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(45deg, #1a365d, #2c5282);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .logo-text {
            font-size: 28px;
            font-weight: 900;
            color: white;
        }
        
        .nav-buttons {
            display: flex;
            gap: 15px;
        }
        
        .nav-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
        }
        
        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .page-title {
            text-align: center;
            font-size: 36px;
            font-weight: 900;
            margin-bottom: 10px;
            color: #3182ce;
            text-shadow: 0 0 20px rgba(49, 130, 206, 0.5);
        }
        
        .page-subtitle {
            text-align: center;
            font-size: 18px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 40px;
        }
        
        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .panel {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(20px);
        }
        
        .panel-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 20px;
            color: white;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .voice-panel {
            text-align: center;
            background: linear-gradient(135deg, rgba(49, 130, 206, 0.1), rgba(26, 54, 93, 0.1));
            border: 2px solid rgba(49, 130, 206, 0.3);
        }
        
        .voice-button {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            border: none;
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            font-size: 24px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 10px;
            box-shadow: 0 10px 30px rgba(72, 187, 120, 0.3);
        }
        
        .voice-button:hover {
            transform: scale(1.05);
            box-shadow: 0 15px 40px rgba(72, 187, 120, 0.4);
        }
        
        .voice-button.recording {
            background: linear-gradient(135deg, #f56565, #e53e3e);
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        .employee-select {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 12px;
            color: white;
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .employee-select option {
            background: #1a1a2e;
            color: white;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: rgba(255, 255, 255, 0.02);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 14px;
        }
        
        .data-table th {
            background: linear-gradient(135deg, #1a365d, #2c5282);
            color: white;
            font-weight: 600;
        }
        
        .data-table td {
            color: rgba(255, 255, 255, 0.9);
        }
        
        .data-table tbody tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }
        
        .status-active {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 600;
        }
        
        .status-inactive {
            background: linear-gradient(135deg, #f56565, #e53e3e);
            color: white;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 600;
        }
        
        .action-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            margin: 0 2px;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .edit-btn {
            background: linear-gradient(135deg, #f6e05e, #d69e2e);
            color: black;
        }
        
        .delete-btn {
            background: linear-gradient(135deg, #f56565, #e53e3e);
        }
        
        .add-btn {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }
        
        .add-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
        }
        
        .search-section {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(20px);
        }
        
        .search-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 12px;
            color: white;
            font-size: 14px;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #3182ce;
            box-shadow: 0 0 0 2px rgba(49, 130, 206, 0.2);
        }
        
        .search-result {
            background: rgba(49, 130, 206, 0.1);
            border: 1px solid rgba(49, 130, 206, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
        }
        
        .modal-content {
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            margin: 5% auto;
            padding: 30px;
            border-radius: 20px;
            width: 90%;
            max-width: 500px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .modal-title {
            font-size: 24px;
            font-weight: 700;
            color: #3182ce;
        }
        
        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 12px;
            color: white;
            font-size: 14px;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #3182ce;
            box-shadow: 0 0 0 2px rgba(49, 130, 206, 0.2);
        }
        
        .save-btn {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .save-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
        }
        
        .print-btn {
            background: linear-gradient(135deg, #9f7aea, #805ad5);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .print-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(159, 122, 234, 0.4);
        }
        
        .voice-status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
            text-align: center;
        }
        
        .voice-indicator {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 20px 40px;
            border-radius: 15px;
            font-size: 18px;
            font-weight: 600;
            z-index: 2000;
            display: none;
            text-align: center;
        }
        
        .voice-indicator.show {
            display: block;
            animation: fadeInOut 3s ease-in-out;
        }
        
        @keyframes fadeInOut {
            0%, 100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
            50% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        }
        
        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
            }
            
            .search-grid {
                grid-template-columns: 1fr;
            }
            
            .voice-button {
                width: 150px;
                height: 150px;
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">🏦</div>
                <div class="logo-text">EG BANK</div>
            </div>
            
            <div class="nav-buttons">
                <a href="home_simple.html" class="nav-btn">🏠 الرئيسية</a>
                <a href="cameras.html" class="nav-btn">📹 المراقبة</a>
                <a href="gps.html" class="nav-btn">🛰️ تتبع الصرافات</a>
                <button class="print-btn" onclick="printReport()">🖨️ طباعة التقرير</button>
            </div>
        </div>
    </header>
    
    <div class="container">
        <h1 class="page-title">🎤 SmartVoice Attendance</h1>
        <p class="page-subtitle">نظام الحضور والانصراف بالتعرف على الصوت</p>
        
        <div class="main-grid">
            <!-- Voice Recognition Panel -->
            <div class="panel voice-panel">
                <h2 class="panel-title">🎤 تسجيل بصمة الصوت</h2>
                
                <select class="employee-select" id="employeeSelect">
                    <option value="">اختر الموظف</option>
                </select>
                
                <button class="voice-button" id="voiceButton" onclick="toggleVoiceRecording()">
                    <span id="voiceIcon">🎤</span>
                    <span id="voiceText">اضغط للتسجيل</span>
                </button>
                
                <div class="voice-status" id="voiceStatus">
                    جاهز لتسجيل بصمة الصوت
                </div>
            </div>
            
            <!-- Quick Actions Panel -->
            <div class="panel">
                <h2 class="panel-title">⚡ إجراءات سريعة</h2>
                
                <button class="add-btn" onclick="openAddEmployeeModal()">
                    ➕ إضافة موظف جديد
                </button>
                
                <button class="add-btn" onclick="manualAttendance()">
                    ✏️ تسجيل يدوي
                </button>
                
                <button class="add-btn" onclick="viewTodayReport()">
                    📊 تقرير اليوم
                </button>
                
                <button class="add-btn" onclick="exportData()">
                    📤 تصدير البيانات
                </button>
            </div>
        </div>
        
        <!-- Search Section -->
        <div class="search-section">
            <h2 class="panel-title">🔎 بحث سريع</h2>
            
            <div class="search-grid">
                <input type="date" class="search-input" id="searchDate" placeholder="البحث بالتاريخ">
                <input type="text" class="search-input" id="searchEmployee" placeholder="البحث بالموظف">
                <select class="search-input" id="searchType">
                    <option value="">نوع العملية</option>
                    <option value="checkin">دخول</option>
                    <option value="checkout">انصراف</option>
                </select>
                <button class="add-btn" onclick="performSearch()">🔍 بحث</button>
            </div>
            
            <div id="searchResults"></div>
        </div>
        
        <!-- Employee Information -->
        <div class="panel">
            <h2 class="panel-title">🧾 معلومات الموظفين</h2>
            
            <div style="overflow-x: auto;">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>رقم الموظف</th>
                            <th>الاسم</th>
                            <th>القسم</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="employeesTableBody">
                        <!-- Data will be populated here -->
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Attendance Records -->
        <div class="panel">
            <h2 class="panel-title">📅 سجل الحضور والانصراف (بتاريخ: <span id="currentDate"></span>)</h2>
            
            <div style="overflow-x: auto;">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>الوقت</th>
                            <th>رقم الموظف</th>
                            <th>الاسم</th>
                            <th>النوع</th>
                            <th>الطريقة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="attendanceTableBody">
                        <!-- Data will be populated here -->
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Operations Log -->
        <div class="panel">
            <h2 class="panel-title">✅ العمليات المُسجلة</h2>
            
            <div style="overflow-x: auto;">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>العملية</th>
                            <th>رقم الموظف</th>
                            <th>الاسم</th>
                            <th>الوقت</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody id="operationsTableBody">
                        <!-- Data will be populated here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Add Employee Modal -->
    <div id="employeeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">إضافة موظف جديد</h3>
                <button class="close-btn" onclick="closeEmployeeModal()">&times;</button>
            </div>
            
            <form id="employeeForm">
                <div class="form-group">
                    <label class="form-label">رقم الموظف:</label>
                    <input type="text" class="form-input" id="employeeNumber" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">الاسم الكامل:</label>
                    <input type="text" class="form-input" id="employeeName" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">القسم:</label>
                    <select class="form-input" id="employeeDepartment" required>
                        <option value="">اختر القسم</option>
                        <option value="الموارد البشرية">الموارد البشرية</option>
                        <option value="المالية">المالية</option>
                        <option value="تقنية المعلومات">تقنية المعلومات</option>
                        <option value="خدمة العملاء">خدمة العملاء</option>
                        <option value="الأمان">الأمان</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">الحالة:</label>
                    <select class="form-input" id="employeeStatus" required>
                        <option value="نشط">نشط</option>
                        <option value="مفصول">مفصول</option>
                        <option value="إجازة">إجازة</option>
                    </select>
                </div>
                
                <button type="submit" class="save-btn">💾 حفظ الموظف</button>
            </form>
        </div>
    </div>
    
    <!-- Voice Indicator -->
    <div id="voiceIndicator" class="voice-indicator">
        <div id="voiceMessage">🔊 جاري التسجيل...</div>
    </div>
    
    <script>
        // SmartVoice Attendance System
        class SmartVoiceAttendance {
            constructor() {
                this.employees = [];
                this.attendanceRecords = [];
                this.operations = [];
                this.isRecording = false;
                this.recognition = null;
                this.speechSynthesis = window.speechSynthesis;
                this.init();
            }

            init() {
                console.log('🎤 Initializing SmartVoice Attendance System...');

                this.initializeVoiceRecognition();
                this.loadSampleData();
                this.updateCurrentDate();
                this.renderTables();
                this.setupEventListeners();

                console.log('✅ SmartVoice Attendance System initialized successfully');
            }

            initializeVoiceRecognition() {
                if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                    this.recognition = new SpeechRecognition();
                    this.recognition.lang = 'ar-SA';
                    this.recognition.continuous = false;
                    this.recognition.interimResults = false;

                    this.recognition.onstart = () => {
                        console.log('🎤 Voice recognition started');
                        this.updateVoiceStatus('جاري الاستماع... تحدث الآن');
                    };

                    this.recognition.onresult = (event) => {
                        const transcript = event.results[0][0].transcript;
                        console.log('🎤 Voice recognized:', transcript);
                        this.processVoiceCommand(transcript);
                    };

                    this.recognition.onerror = (event) => {
                        console.error('🎤 Voice recognition error:', event.error);
                        this.updateVoiceStatus('خطأ في التعرف على الصوت');
                        this.stopRecording();
                    };

                    this.recognition.onend = () => {
                        console.log('🎤 Voice recognition ended');
                        this.stopRecording();
                    };
                } else {
                    console.warn('🎤 Speech recognition not supported');
                    this.updateVoiceStatus('التعرف على الصوت غير مدعوم في هذا المتصفح');
                }
            }

            loadSampleData() {
                // Load employees from localStorage or create sample data
                const savedEmployees = localStorage.getItem('smartvoice_employees');
                if (savedEmployees) {
                    this.employees = JSON.parse(savedEmployees);
                } else {
                    this.employees = [
                        {
                            id: '001',
                            name: 'محمد أحمد',
                            department: 'الموارد البشرية',
                            status: 'نشط',
                            voicePattern: null
                        },
                        {
                            id: '002',
                            name: 'فاطمة يوسف',
                            department: 'المالية',
                            status: 'نشط',
                            voicePattern: null
                        },
                        {
                            id: '003',
                            name: 'أحمد عبد الرحمن',
                            department: 'تقنية المعلومات',
                            status: 'مفصول',
                            voicePattern: null
                        }
                    ];
                    this.saveEmployees();
                }

                // Load attendance records
                const savedRecords = localStorage.getItem('smartvoice_attendance');
                if (savedRecords) {
                    this.attendanceRecords = JSON.parse(savedRecords);
                } else {
                    // Create sample attendance records for today
                    const today = new Date().toISOString().split('T')[0];
                    this.attendanceRecords = [
                        {
                            id: Date.now() + 1,
                            employeeId: '001',
                            employeeName: 'محمد أحمد',
                            type: 'دخول',
                            method: 'بصمة صوت',
                            time: '08:01 صباحًا',
                            date: today,
                            timestamp: new Date().toISOString()
                        },
                        {
                            id: Date.now() + 2,
                            employeeId: '002',
                            employeeName: 'فاطمة يوسف',
                            type: 'دخول',
                            method: 'بصمة صوت',
                            time: '08:07 صباحًا',
                            date: today,
                            timestamp: new Date().toISOString()
                        },
                        {
                            id: Date.now() + 3,
                            employeeId: '001',
                            employeeName: 'محمد أحمد',
                            type: 'انصراف',
                            method: 'بصمة صوت',
                            time: '04:15 مساءً',
                            date: today,
                            timestamp: new Date().toISOString()
                        }
                    ];
                    this.saveAttendanceRecords();
                }

                // Load operations
                const savedOperations = localStorage.getItem('smartvoice_operations');
                if (savedOperations) {
                    this.operations = JSON.parse(savedOperations);
                } else {
                    this.operations = [
                        {
                            id: Date.now() + 1,
                            operation: 'تسجيل دخول',
                            employeeId: '001',
                            employeeName: 'محمد أحمد',
                            time: '08:01 صباحًا',
                            notes: 'نجحت بصمة الصوت ✅'
                        },
                        {
                            id: Date.now() + 2,
                            operation: 'تعديل وقت',
                            employeeId: '002',
                            employeeName: 'فاطمة يوسف',
                            time: '08:10 صباحًا',
                            notes: 'تم التعديل يدويًا ⚠️'
                        },
                        {
                            id: Date.now() + 3,
                            operation: 'حذف سجل',
                            employeeId: '003',
                            employeeName: 'أحمد عبد الرحمن',
                            time: '09:00 صباحًا',
                            notes: 'تم الحذف لغياب مبرر 🗑️'
                        }
                    ];
                    this.saveOperations();
                }
            }

            updateCurrentDate() {
                const today = new Date();
                const dateString = today.toLocaleDateString('ar-EG', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit'
                });
                document.getElementById('currentDate').textContent = dateString;
            }

            renderTables() {
                this.renderEmployeesTable();
                this.renderAttendanceTable();
                this.renderOperationsTable();
                this.updateEmployeeSelect();
            }

            renderEmployeesTable() {
                const tbody = document.getElementById('employeesTableBody');
                tbody.innerHTML = '';

                this.employees.forEach(employee => {
                    const row = tbody.insertRow();
                    const statusClass = employee.status === 'نشط' ? 'status-active' : 'status-inactive';

                    row.innerHTML = `
                        <td>${employee.id}</td>
                        <td>${employee.name}</td>
                        <td>${employee.department}</td>
                        <td><span class="${statusClass}">${employee.status}</span></td>
                        <td>
                            <button class="action-btn edit-btn" onclick="editEmployee('${employee.id}')">✏️ تعديل</button>
                            <button class="action-btn delete-btn" onclick="deleteEmployee('${employee.id}')">🗑️ حذف</button>
                        </td>
                    `;
                });
            }

            renderAttendanceTable() {
                const tbody = document.getElementById('attendanceTableBody');
                tbody.innerHTML = '';

                const today = new Date().toISOString().split('T')[0];
                const todayRecords = this.attendanceRecords.filter(record => record.date === today);

                todayRecords.forEach(record => {
                    const row = tbody.insertRow();
                    row.innerHTML = `
                        <td>${record.time}</td>
                        <td>${record.employeeId}</td>
                        <td>${record.employeeName}</td>
                        <td>${record.type}</td>
                        <td>${record.method}</td>
                        <td>
                            <button class="action-btn edit-btn" onclick="editAttendance('${record.id}')">✏️ تعديل</button>
                            <button class="action-btn delete-btn" onclick="deleteAttendance('${record.id}')">🗑️ حذف</button>
                        </td>
                    `;
                });
            }

            renderOperationsTable() {
                const tbody = document.getElementById('operationsTableBody');
                tbody.innerHTML = '';

                this.operations.forEach(operation => {
                    const row = tbody.insertRow();
                    row.innerHTML = `
                        <td>${operation.operation}</td>
                        <td>${operation.employeeId}</td>
                        <td>${operation.employeeName}</td>
                        <td>${operation.time}</td>
                        <td>${operation.notes}</td>
                    `;
                });
            }

            updateEmployeeSelect() {
                const select = document.getElementById('employeeSelect');
                select.innerHTML = '<option value="">اختر الموظف</option>';

                this.employees.filter(emp => emp.status === 'نشط').forEach(employee => {
                    const option = document.createElement('option');
                    option.value = employee.id;
                    option.textContent = `${employee.id} - ${employee.name}`;
                    select.appendChild(option);
                });
            }

            setupEventListeners() {
                // Employee form submission
                document.getElementById('employeeForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.saveEmployee();
                });

                // Set today's date in search
                document.getElementById('searchDate').value = new Date().toISOString().split('T')[0];
            }

            toggleVoiceRecording() {
                if (!this.recognition) {
                    alert('التعرف على الصوت غير مدعوم في هذا المتصفح');
                    return;
                }

                const selectedEmployee = document.getElementById('employeeSelect').value;
                if (!selectedEmployee) {
                    alert('يرجى اختيار موظف أولاً');
                    return;
                }

                if (this.isRecording) {
                    this.stopRecording();
                } else {
                    this.startRecording();
                }
            }

            startRecording() {
                this.isRecording = true;
                const button = document.getElementById('voiceButton');
                const icon = document.getElementById('voiceIcon');
                const text = document.getElementById('voiceText');

                button.classList.add('recording');
                icon.textContent = '🔴';
                text.textContent = 'جاري التسجيل...';

                this.updateVoiceStatus('جاري التسجيل... تحدث الآن');
                this.recognition.start();

                // Show voice indicator
                this.showVoiceIndicator('🎤 جاري تسجيل بصمة الصوت...');
            }

            stopRecording() {
                this.isRecording = false;
                const button = document.getElementById('voiceButton');
                const icon = document.getElementById('voiceIcon');
                const text = document.getElementById('voiceText');

                button.classList.remove('recording');
                icon.textContent = '🎤';
                text.textContent = 'اضغط للتسجيل';

                this.updateVoiceStatus('جاهز لتسجيل بصمة الصوت');

                if (this.recognition) {
                    this.recognition.stop();
                }
            }

            processVoiceCommand(transcript) {
                const selectedEmployeeId = document.getElementById('employeeSelect').value;
                const employee = this.employees.find(emp => emp.id === selectedEmployeeId);

                if (!employee) {
                    this.updateVoiceStatus('خطأ: لم يتم العثور على الموظف');
                    return;
                }

                // Determine if it's check-in or check-out based on voice command
                const isCheckIn = transcript.includes('دخول') || transcript.includes('حضور') || transcript.includes('وصول');
                const isCheckOut = transcript.includes('خروج') || transcript.includes('انصراف') || transcript.includes('مغادرة');

                let type = 'دخول'; // Default to check-in
                if (isCheckOut) {
                    type = 'انصراف';
                }

                // Check if employee already has a record today
                const today = new Date().toISOString().split('T')[0];
                const todayRecords = this.attendanceRecords.filter(record =>
                    record.employeeId === selectedEmployeeId && record.date === today
                );

                // If no records today, it's check-in. If has check-in, it's check-out
                if (todayRecords.length === 0) {
                    type = 'دخول';
                } else if (todayRecords.some(record => record.type === 'دخول') && !todayRecords.some(record => record.type === 'انصراف')) {
                    type = 'انصراف';
                }

                this.recordAttendance(employee, type, 'بصمة صوت');
                this.updateVoiceStatus(`تم تسجيل ${type} للموظف ${employee.name} بنجاح`);

                // Play voice confirmation
                this.playVoiceConfirmation(`تم تسجيل ${type} ${employee.name} بنجاح`);
            }

            recordAttendance(employee, type, method) {
                const now = new Date();
                const time = now.toLocaleTimeString('ar-EG', {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: true
                });
                const date = now.toISOString().split('T')[0];

                const record = {
                    id: Date.now(),
                    employeeId: employee.id,
                    employeeName: employee.name,
                    type: type,
                    method: method,
                    time: time,
                    date: date,
                    timestamp: now.toISOString()
                };

                this.attendanceRecords.push(record);
                this.saveAttendanceRecords();

                // Add operation log
                const operation = {
                    id: Date.now() + 1,
                    operation: `تسجيل ${type}`,
                    employeeId: employee.id,
                    employeeName: employee.name,
                    time: time,
                    notes: `${method} ✅`
                };

                this.operations.push(operation);
                this.saveOperations();

                // Refresh tables
                this.renderAttendanceTable();
                this.renderOperationsTable();
            }

            playVoiceConfirmation(message) {
                if (this.speechSynthesis) {
                    const utterance = new SpeechSynthesisUtterance(message);
                    utterance.lang = 'ar-SA';
                    utterance.rate = 0.9;
                    utterance.pitch = 1.0;
                    utterance.volume = 0.8;
                    this.speechSynthesis.speak(utterance);
                }
            }

            updateVoiceStatus(message) {
                document.getElementById('voiceStatus').textContent = message;
            }

            showVoiceIndicator(message) {
                const indicator = document.getElementById('voiceIndicator');
                const messageElement = document.getElementById('voiceMessage');

                messageElement.textContent = message;
                indicator.classList.add('show');

                setTimeout(() => {
                    indicator.classList.remove('show');
                }, 3000);
            }

            saveEmployees() {
                localStorage.setItem('smartvoice_employees', JSON.stringify(this.employees));
            }

            saveAttendanceRecords() {
                localStorage.setItem('smartvoice_attendance', JSON.stringify(this.attendanceRecords));
            }

            saveOperations() {
                localStorage.setItem('smartvoice_operations', JSON.stringify(this.operations));
            }

            openAddEmployeeModal() {
                document.getElementById('employeeModal').style.display = 'block';
            }

            closeEmployeeModal() {
                document.getElementById('employeeModal').style.display = 'none';
                document.getElementById('employeeForm').reset();
            }

            saveEmployee() {
                const number = document.getElementById('employeeNumber').value;
                const name = document.getElementById('employeeName').value;
                const department = document.getElementById('employeeDepartment').value;
                const status = document.getElementById('employeeStatus').value;

                // Check if employee number already exists
                if (this.employees.some(emp => emp.id === number)) {
                    alert('رقم الموظف موجود بالفعل');
                    return;
                }

                const employee = {
                    id: number,
                    name: name,
                    department: department,
                    status: status,
                    voicePattern: null
                };

                this.employees.push(employee);
                this.saveEmployees();
                this.renderEmployeesTable();
                this.updateEmployeeSelect();
                this.closeEmployeeModal();

                alert('تم إضافة الموظف بنجاح');
            }

            performSearch() {
                const date = document.getElementById('searchDate').value;
                const employee = document.getElementById('searchEmployee').value.toLowerCase();
                const type = document.getElementById('searchType').value;

                let results = this.attendanceRecords;

                if (date) {
                    results = results.filter(record => record.date === date);
                }

                if (employee) {
                    results = results.filter(record =>
                        record.employeeName.toLowerCase().includes(employee) ||
                        record.employeeId.includes(employee)
                    );
                }

                if (type) {
                    const typeMap = {
                        'checkin': 'دخول',
                        'checkout': 'انصراف'
                    };
                    results = results.filter(record => record.type === typeMap[type]);
                }

                this.displaySearchResults(results);
            }

            displaySearchResults(results) {
                const container = document.getElementById('searchResults');

                if (results.length === 0) {
                    container.innerHTML = '<div class="search-result">لا توجد نتائج للبحث</div>';
                    return;
                }

                let html = '<div class="search-result">';
                html += `<h3>نتائج البحث (${results.length} سجل)</h3>`;

                results.forEach(record => {
                    html += `
                        <div style="margin: 10px 0; padding: 10px; background: rgba(255,255,255,0.05); border-radius: 5px;">
                            <strong>${record.employeeName}</strong> (${record.employeeId}) -
                            ${record.type} في ${record.time} بتاريخ ${record.date}
                        </div>
                    `;
                });

                html += '</div>';
                container.innerHTML = html;
            }

            printReport() {
                const printWindow = window.open('', '_blank');
                const today = new Date().toLocaleDateString('ar-EG');

                let html = `
                    <html dir="rtl">
                    <head>
                        <title>تقرير الحضور والانصراف - ${today}</title>
                        <style>
                            body { font-family: Arial, sans-serif; margin: 20px; }
                            table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                            th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                            th { background-color: #f2f2f2; }
                            h1, h2 { color: #333; }
                        </style>
                    </head>
                    <body>
                        <h1>🏦 EG BANK - SmartVoice Attendance</h1>
                        <h2>تقرير الحضور والانصراف - ${today}</h2>

                        <h3>🧾 معلومات الموظفين</h3>
                        <table>
                            <tr><th>رقم الموظف</th><th>الاسم</th><th>القسم</th><th>الحالة</th></tr>
                `;

                this.employees.forEach(emp => {
                    html += `<tr><td>${emp.id}</td><td>${emp.name}</td><td>${emp.department}</td><td>${emp.status}</td></tr>`;
                });

                html += `
                        </table>

                        <h3>📅 سجل الحضور والانصراف</h3>
                        <table>
                            <tr><th>الوقت</th><th>رقم الموظف</th><th>الاسم</th><th>النوع</th><th>الطريقة</th></tr>
                `;

                const todayRecords = this.attendanceRecords.filter(record =>
                    record.date === new Date().toISOString().split('T')[0]
                );

                todayRecords.forEach(record => {
                    html += `<tr><td>${record.time}</td><td>${record.employeeId}</td><td>${record.employeeName}</td><td>${record.type}</td><td>${record.method}</td></tr>`;
                });

                html += `
                        </table>

                        <h3>✅ العمليات المُسجلة</h3>
                        <table>
                            <tr><th>العملية</th><th>رقم الموظف</th><th>الاسم</th><th>الوقت</th><th>ملاحظات</th></tr>
                `;

                this.operations.forEach(op => {
                    html += `<tr><td>${op.operation}</td><td>${op.employeeId}</td><td>${op.employeeName}</td><td>${op.time}</td><td>${op.notes}</td></tr>`;
                });

                html += `
                        </table>
                    </body>
                    </html>
                `;

                printWindow.document.write(html);
                printWindow.document.close();
                printWindow.print();
            }
        }

        // Global functions
        function toggleVoiceRecording() {
            if (window.smartVoiceSystem) {
                window.smartVoiceSystem.toggleVoiceRecording();
            }
        }

        function openAddEmployeeModal() {
            if (window.smartVoiceSystem) {
                window.smartVoiceSystem.openAddEmployeeModal();
            }
        }

        function closeEmployeeModal() {
            if (window.smartVoiceSystem) {
                window.smartVoiceSystem.closeEmployeeModal();
            }
        }

        function performSearch() {
            if (window.smartVoiceSystem) {
                window.smartVoiceSystem.performSearch();
            }
        }

        function printReport() {
            if (window.smartVoiceSystem) {
                window.smartVoiceSystem.printReport();
            }
        }

        function editEmployee(id) {
            alert('ميزة التعديل ستكون متاحة قريباً');
        }

        function deleteEmployee(id) {
            if (confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
                const system = window.smartVoiceSystem;
                system.employees = system.employees.filter(emp => emp.id !== id);
                system.saveEmployees();
                system.renderEmployeesTable();
                system.updateEmployeeSelect();
                alert('تم حذف الموظف بنجاح');
            }
        }

        function editAttendance(id) {
            alert('ميزة التعديل ستكون متاحة قريباً');
        }

        function deleteAttendance(id) {
            if (confirm('هل أنت متأكد من حذف هذا السجل؟')) {
                const system = window.smartVoiceSystem;
                system.attendanceRecords = system.attendanceRecords.filter(record => record.id != id);
                system.saveAttendanceRecords();
                system.renderAttendanceTable();
                alert('تم حذف السجل بنجاح');
            }
        }

        function manualAttendance() {
            const employeeId = prompt('أدخل رقم الموظف:');
            if (!employeeId) return;

            const system = window.smartVoiceSystem;
            const employee = system.employees.find(emp => emp.id === employeeId);

            if (!employee) {
                alert('الموظف غير موجود');
                return;
            }

            const type = confirm('اختر نوع التسجيل:\nOK = دخول\nCancel = انصراف') ? 'دخول' : 'انصراف';
            system.recordAttendance(employee, type, 'تسجيل يدوي');
            alert(`تم تسجيل ${type} للموظف ${employee.name} بنجاح`);
        }

        function viewTodayReport() {
            const system = window.smartVoiceSystem;
            const today = new Date().toISOString().split('T')[0];
            const todayRecords = system.attendanceRecords.filter(record => record.date === today);

            let report = `تقرير اليوم (${new Date().toLocaleDateString('ar-EG')}):\n\n`;
            report += `عدد السجلات: ${todayRecords.length}\n\n`;

            todayRecords.forEach(record => {
                report += `${record.employeeName} - ${record.type} في ${record.time}\n`;
            });

            alert(report);
        }

        function exportData() {
            const system = window.smartVoiceSystem;
            const data = {
                employees: system.employees,
                attendance: system.attendanceRecords,
                operations: system.operations,
                exportDate: new Date().toISOString()
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `smartvoice_attendance_${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // Initialize system when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎤 SmartVoice Attendance System loading...');
            window.smartVoiceSystem = new SmartVoiceAttendance();
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            const modal = document.getElementById('employeeModal');
            if (event.target === modal) {
                closeEmployeeModal();
            }
        });

        console.log('🎤 SmartVoice Attendance System Script Loaded');
    </script></script>
</body>
</html>
