// EG BANK Attendance Database Management System
// نظام إدارة قاعدة بيانات الحضور والانصراف

class AttendanceDatabaseManager {
    constructor() {
        this.employees = [];
        this.attendanceRecords = [];
        this.currentEmployeePage = 1;
        this.currentRecordsPage = 1;
        this.itemsPerPage = 10;
        this.employeeSearchTerm = '';
        this.recordsSearchTerm = '';
        this.init();
    }

    // Initialize the database manager
    init() {
        console.log('🗄️ Initializing Database Manager...');
        
        this.loadData();
        this.updateStatistics();
        this.renderEmployeesTable();
        this.renderRecordsTable();
        this.setupEventListeners();
        
        console.log('✅ Database Manager initialized successfully');
    }

    // Load data from localStorage
    loadData() {
        try {
            this.employees = JSON.parse(localStorage.getItem('attendanceEmployees') || '[]');
            this.attendanceRecords = JSON.parse(localStorage.getItem('attendanceRecordsAdvanced') || '[]');
            console.log(`📊 Loaded ${this.employees.length} employees and ${this.attendanceRecords.length} records`);
        } catch (error) {
            console.error('Error loading data:', error);
            this.employees = [];
            this.attendanceRecords = [];
        }
    }

    // Setup event listeners
    setupEventListeners() {
        // Employee search
        document.getElementById('employeeSearch').addEventListener('input', (e) => {
            this.employeeSearchTerm = e.target.value.toLowerCase();
            this.currentEmployeePage = 1;
            this.renderEmployeesTable();
        });

        // Records search
        document.getElementById('recordsSearch').addEventListener('input', (e) => {
            this.recordsSearchTerm = e.target.value.toLowerCase();
            this.currentRecordsPage = 1;
            this.renderRecordsTable();
        });
    }

    // Update statistics
    updateStatistics() {
        const today = new Date().toDateString();
        const todayRecords = this.attendanceRecords.filter(record => 
            new Date(record.date).toDateString() === today
        );

        // Calculate database size
        const dataSize = new Blob([
            JSON.stringify(this.employees) + JSON.stringify(this.attendanceRecords)
        ]).size;

        document.getElementById('totalEmployees').textContent = this.employees.length;
        document.getElementById('totalRecords').textContent = this.attendanceRecords.length;
        document.getElementById('todayRecords').textContent = todayRecords.length;
        document.getElementById('databaseSize').textContent = Math.round(dataSize / 1024);
    }

    // Filter employees based on search term
    getFilteredEmployees() {
        if (!this.employeeSearchTerm) {
            return this.employees;
        }

        return this.employees.filter(employee => 
            employee.name.toLowerCase().includes(this.employeeSearchTerm) ||
            employee.employeeId.toLowerCase().includes(this.employeeSearchTerm) ||
            employee.department.toLowerCase().includes(this.employeeSearchTerm) ||
            (employee.position && employee.position.toLowerCase().includes(this.employeeSearchTerm))
        );
    }

    // Filter records based on search term
    getFilteredRecords() {
        if (!this.recordsSearchTerm) {
            return this.attendanceRecords;
        }

        return this.attendanceRecords.filter(record => 
            record.employeeName.toLowerCase().includes(this.recordsSearchTerm) ||
            record.employeeNumber.toLowerCase().includes(this.recordsSearchTerm) ||
            record.department.toLowerCase().includes(this.recordsSearchTerm)
        );
    }

    // Render employees table
    renderEmployeesTable() {
        const filteredEmployees = this.getFilteredEmployees();
        const startIndex = (this.currentEmployeePage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pageEmployees = filteredEmployees.slice(startIndex, endIndex);

        const tbody = document.getElementById('employeesTableBody');
        tbody.innerHTML = '';

        pageEmployees.forEach((employee, index) => {
            const row = tbody.insertRow();
            const globalIndex = startIndex + index + 1;

            const statusText = employee.status === 'active' ? 'نشط' : 'غير نشط';
            const statusClass = employee.status === 'active' ? 'status-active' : 'status-inactive';

            row.innerHTML = `
                <td>${globalIndex}</td>
                <td>${employee.name}</td>
                <td>${employee.employeeId}</td>
                <td>${employee.department}</td>
                <td>${employee.position || 'غير محدد'}</td>
                <td>${employee.phone || 'غير محدد'}</td>
                <td>${employee.email || 'غير محدد'}</td>
                <td class="${statusClass}">${statusText}</td>
                <td>${new Date(employee.createdAt).toLocaleDateString('ar-EG')}</td>
            `;

            // Add click event to show employee details
            row.style.cursor = 'pointer';
            row.addEventListener('click', () => {
                this.showEmployeeDetails(employee);
            });
        });

        // Render pagination
        this.renderEmployeesPagination(filteredEmployees.length);
    }

    // Render records table
    renderRecordsTable() {
        const filteredRecords = this.getFilteredRecords();
        // Sort by date (newest first)
        filteredRecords.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        
        const startIndex = (this.currentRecordsPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pageRecords = filteredRecords.slice(startIndex, endIndex);

        const tbody = document.getElementById('recordsTableBody');
        tbody.innerHTML = '';

        pageRecords.forEach((record, index) => {
            const row = tbody.insertRow();
            const globalIndex = startIndex + index + 1;

            const checkinTime = record.checkinTime ? 
                new Date(record.checkinTime).toLocaleTimeString('ar-EG') : '--:--';
            const checkoutTime = record.checkoutTime ? 
                new Date(record.checkoutTime).toLocaleTimeString('ar-EG') : '--:--';
            const workingHours = record.workingHours ? 
                record.workingHours.toFixed(1) + ' ساعة' : '--';

            let status = 'غائب';
            let statusClass = 'status-absent';

            if (record.status === 'checked_in') {
                status = 'حاضر';
                statusClass = 'status-present';
            } else if (record.status === 'checked_out') {
                status = 'منصرف';
                statusClass = 'status-present';
            }

            row.innerHTML = `
                <td>${globalIndex}</td>
                <td>${record.employeeName}</td>
                <td>${record.employeeNumber}</td>
                <td>${record.department}</td>
                <td>${new Date(record.date).toLocaleDateString('ar-EG')}</td>
                <td>${checkinTime}</td>
                <td>${checkoutTime}</td>
                <td>${workingHours}</td>
                <td class="${statusClass}">${status}</td>
            `;

            // Add click event to show record details
            row.style.cursor = 'pointer';
            row.addEventListener('click', () => {
                this.showRecordDetails(record);
            });
        });

        // Render pagination
        this.renderRecordsPagination(filteredRecords.length);
    }

    // Render employees pagination
    renderEmployeesPagination(totalItems) {
        const totalPages = Math.ceil(totalItems / this.itemsPerPage);
        const pagination = document.getElementById('employeesPagination');
        pagination.innerHTML = '';

        if (totalPages <= 1) return;

        // Previous button
        if (this.currentEmployeePage > 1) {
            const prevBtn = document.createElement('button');
            prevBtn.className = 'page-btn';
            prevBtn.textContent = '« السابق';
            prevBtn.onclick = () => {
                this.currentEmployeePage--;
                this.renderEmployeesTable();
            };
            pagination.appendChild(prevBtn);
        }

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= this.currentEmployeePage - 2 && i <= this.currentEmployeePage + 2)) {
                const pageBtn = document.createElement('button');
                pageBtn.className = `page-btn ${i === this.currentEmployeePage ? 'active' : ''}`;
                pageBtn.textContent = i;
                pageBtn.onclick = () => {
                    this.currentEmployeePage = i;
                    this.renderEmployeesTable();
                };
                pagination.appendChild(pageBtn);
            } else if (i === this.currentEmployeePage - 3 || i === this.currentEmployeePage + 3) {
                const dots = document.createElement('span');
                dots.textContent = '...';
                dots.style.color = 'rgba(255,255,255,0.5)';
                pagination.appendChild(dots);
            }
        }

        // Next button
        if (this.currentEmployeePage < totalPages) {
            const nextBtn = document.createElement('button');
            nextBtn.className = 'page-btn';
            nextBtn.textContent = 'التالي »';
            nextBtn.onclick = () => {
                this.currentEmployeePage++;
                this.renderEmployeesTable();
            };
            pagination.appendChild(nextBtn);
        }
    }

    // Render records pagination
    renderRecordsPagination(totalItems) {
        const totalPages = Math.ceil(totalItems / this.itemsPerPage);
        const pagination = document.getElementById('recordsPagination');
        pagination.innerHTML = '';

        if (totalPages <= 1) return;

        // Previous button
        if (this.currentRecordsPage > 1) {
            const prevBtn = document.createElement('button');
            prevBtn.className = 'page-btn';
            prevBtn.textContent = '« السابق';
            prevBtn.onclick = () => {
                this.currentRecordsPage--;
                this.renderRecordsTable();
            };
            pagination.appendChild(prevBtn);
        }

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= this.currentRecordsPage - 2 && i <= this.currentRecordsPage + 2)) {
                const pageBtn = document.createElement('button');
                pageBtn.className = `page-btn ${i === this.currentRecordsPage ? 'active' : ''}`;
                pageBtn.textContent = i;
                pageBtn.onclick = () => {
                    this.currentRecordsPage = i;
                    this.renderRecordsTable();
                };
                pagination.appendChild(pageBtn);
            } else if (i === this.currentRecordsPage - 3 || i === this.currentRecordsPage + 3) {
                const dots = document.createElement('span');
                dots.textContent = '...';
                dots.style.color = 'rgba(255,255,255,0.5)';
                pagination.appendChild(dots);
            }
        }

        // Next button
        if (this.currentRecordsPage < totalPages) {
            const nextBtn = document.createElement('button');
            nextBtn.className = 'page-btn';
            nextBtn.textContent = 'التالي »';
            nextBtn.onclick = () => {
                this.currentRecordsPage++;
                this.renderRecordsTable();
            };
            pagination.appendChild(nextBtn);
        }
    }

    // Show employee details
    showEmployeeDetails(employee) {
        const modal = document.getElementById('recordModal');
        const title = modal.querySelector('.modal-title');
        const details = document.getElementById('recordDetails');

        title.textContent = 'تفاصيل الموظف';

        const employeeRecords = this.attendanceRecords.filter(record => record.employeeId === employee.id);
        const totalWorkingHours = employeeRecords.reduce((sum, record) => sum + (record.workingHours || 0), 0);

        details.innerHTML = `
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                <div>
                    <h4 style="color: #3182ce; margin-bottom: 10px;">المعلومات الأساسية</h4>
                    <p><strong>الاسم:</strong> ${employee.name}</p>
                    <p><strong>الرقم الوظيفي:</strong> ${employee.employeeId}</p>
                    <p><strong>القسم:</strong> ${employee.department}</p>
                    <p><strong>المنصب:</strong> ${employee.position || 'غير محدد'}</p>
                </div>
                <div>
                    <h4 style="color: #3182ce; margin-bottom: 10px;">معلومات الاتصال</h4>
                    <p><strong>الهاتف:</strong> ${employee.phone || 'غير محدد'}</p>
                    <p><strong>البريد الإلكتروني:</strong> ${employee.email || 'غير محدد'}</p>
                    <p><strong>الحالة:</strong> ${employee.status === 'active' ? 'نشط' : 'غير نشط'}</p>
                    <p><strong>تاريخ الإنشاء:</strong> ${new Date(employee.createdAt).toLocaleDateString('ar-EG')}</p>
                </div>
            </div>
            <div>
                <h4 style="color: #3182ce; margin-bottom: 10px;">إحصائيات الحضور</h4>
                <p><strong>إجمالي السجلات:</strong> ${employeeRecords.length}</p>
                <p><strong>إجمالي ساعات العمل:</strong> ${totalWorkingHours.toFixed(1)} ساعة</p>
                <p><strong>آخر حضور:</strong> ${employee.lastAttendance ? new Date(employee.lastAttendance).toLocaleString('ar-EG') : 'لا يوجد'}</p>
            </div>
        `;

        modal.style.display = 'block';
    }

    // Show record details
    showRecordDetails(record) {
        const modal = document.getElementById('recordModal');
        const title = modal.querySelector('.modal-title');
        const details = document.getElementById('recordDetails');

        title.textContent = 'تفاصيل سجل الحضور';

        const checkinTime = record.checkinTime ? 
            new Date(record.checkinTime).toLocaleString('ar-EG') : 'لم يسجل';
        const checkoutTime = record.checkoutTime ? 
            new Date(record.checkoutTime).toLocaleString('ar-EG') : 'لم يسجل';

        details.innerHTML = `
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h4 style="color: #3182ce; margin-bottom: 10px;">معلومات الموظف</h4>
                    <p><strong>الاسم:</strong> ${record.employeeName}</p>
                    <p><strong>الرقم الوظيفي:</strong> ${record.employeeNumber}</p>
                    <p><strong>القسم:</strong> ${record.department}</p>
                </div>
                <div>
                    <h4 style="color: #3182ce; margin-bottom: 10px;">تفاصيل الحضور</h4>
                    <p><strong>التاريخ:</strong> ${new Date(record.date).toLocaleDateString('ar-EG')}</p>
                    <p><strong>وقت الحضور:</strong> ${checkinTime}</p>
                    <p><strong>وقت الانصراف:</strong> ${checkoutTime}</p>
                    <p><strong>ساعات العمل:</strong> ${record.workingHours ? record.workingHours.toFixed(1) + ' ساعة' : 'غير محسوبة'}</p>
                    <p><strong>الحالة:</strong> ${record.status === 'checked_in' ? 'حاضر' : (record.checkoutTime ? 'منصرف' : 'غائب')}</p>
                </div>
            </div>
            ${record.notes ? `<div style="margin-top: 20px;"><h4 style="color: #3182ce; margin-bottom: 10px;">ملاحظات</h4><p>${record.notes}</p></div>` : ''}
        `;

        modal.style.display = 'block';
    }

    // Refresh data
    refreshData() {
        this.loadData();
        this.updateStatistics();
        this.renderEmployeesTable();
        this.renderRecordsTable();
        
        if (window.egbank) {
            window.egbank.showNotification('تم تحديث البيانات بنجاح', 'success');
        }
    }

    // Clear old data
    clearOldData() {
        const daysToKeep = prompt('كم يوم تريد الاحتفاظ بالبيانات؟ (اتركه فارغاً للإلغاء)', '30');
        
        if (daysToKeep === null) return;
        
        const days = parseInt(daysToKeep);
        if (isNaN(days) || days < 1) {
            if (window.egbank) {
                window.egbank.showNotification('يرجى إدخال رقم صحيح', 'error');
            }
            return;
        }
        
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - days);
        
        const originalCount = this.attendanceRecords.length;
        this.attendanceRecords = this.attendanceRecords.filter(record => 
            new Date(record.date) >= cutoffDate
        );
        
        const deletedCount = originalCount - this.attendanceRecords.length;
        
        // Save updated records
        localStorage.setItem('attendanceRecordsAdvanced', JSON.stringify(this.attendanceRecords));
        
        // Refresh display
        this.refreshData();
        
        if (window.egbank) {
            window.egbank.showNotification(`تم حذف ${deletedCount} سجل قديم`, 'success');
        }
    }
}

// Global functions
function refreshData() {
    if (window.databaseManager) {
        window.databaseManager.refreshData();
    }
}

function clearOldData() {
    if (window.databaseManager) {
        window.databaseManager.clearOldData();
    }
}

function closeRecordModal() {
    document.getElementById('recordModal').style.display = 'none';
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('🗄️ Database Manager loading...');
    window.databaseManager = new AttendanceDatabaseManager();
});

// Close modal when clicking outside
window.addEventListener('click', function(event) {
    const modal = document.getElementById('recordModal');
    if (event.target === modal) {
        closeRecordModal();
    }
});

console.log('🗄️ Database Manager Script Loaded');
