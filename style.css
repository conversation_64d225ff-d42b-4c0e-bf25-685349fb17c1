/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #2c2c2c; /* Dark gray background */
    color: white;
    line-height: 1.6;
}

/* Banking Header */
.bank-header {
    background: linear-gradient(to right, #3a3a3a, #4a4a4a); /* Dark gray gradient */
    color: white;
    padding: 15px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.5);
}

.bank-logo {
    font-size: 28px;
    font-weight: bold;
    letter-spacing: 1px;
}

.bank-nav ul {
    display: flex;
    list-style: none;
}

.bank-nav li {
    margin-left: 20px;
}

.bank-nav a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    padding: 5px 10px;
    border-radius: 4px;
    transition: background 0.3s;
}

.bank-nav a:hover {
    background: rgba(255,255,255,0.1);
}

/* Main Banking Container */
.bank-container {
    display: flex;
    min-height: calc(100vh - 120px);
}

/* Banking Sidebar */
.bank-sidebar {
    width: 250px;
    background: #3a3a3a; /* Dark gray sidebar */
    color: white;
    padding: 20px;
    border-right: 1px solid #555555;
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
}

.bank-sidebar h3 {
    color: white;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e1e4e8;
}

.bank-sidebar ul {
    list-style: none;
}

.bank-sidebar li {
    padding: 12px 15px;
    margin-bottom: 5px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
}

.bank-sidebar li:hover, .bank-sidebar h3:hover {
    color: black;
    background: white;
}

.bank-sidebar li.active {
    background: #1a365d; /* Dark blue active */
    color: white;
    font-weight: 500;
}

/* Main Content Area */
.bank-main {
    flex: 1;
    padding: 30px;
}

.bank-main h1 {
    color: white;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e1e4e8;
}

/* Camera Container */
.camera-container {
    background: #3a3a3a; /* Dark gray container */
    color: white;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

video {
    width: 100%;
    background-color: #000;
    border-radius: 6px;
    margin-bottom: 25px;
    border: 1px solid #ddd;
}

.controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 25px;
    flex-wrap: wrap;
}

button {
    padding: 12px 25px;
    font-size: 16px;
    font-weight: 500;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

#startBtn {
    background-color: #1a365d;
    color: white;
}

#startBtn:hover {
    background-color: #2c5282;
}

#recordBtn {
    background-color: #1a365d;
    color: white;
}

#recordBtn:hover {
    background-color: #2c5282;
}

#stopBtn {
    background-color: #1a365d;
    color: white;
}

#stopBtn:hover {
    background-color: #2c5282;
}

#downloadBtn {
    background-color: #1a365d;
    color: white;
}

#downloadBtn:hover {
    background-color: #2c5282;
}

button:disabled {
    background-color: #cbd5e0;
    color: #718096;
    cursor: not-allowed;
}

/* Recordings Grid */
#recordings {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 20px;
    margin-top: 25px;
}

.recording {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.recording video {
    width: 100%;
    margin-bottom: 0;
    border-radius: 6px 6px 0 0;
}

.recording button {
    width: 100%;
    padding: 10px;
    background: #1a365d;
    color: white;
    border-radius: 0 0 6px 6px;
}

.recording button:hover {
    background: #2c5282;
}

/* Camera Management Form */
.form-container {
    background: #3a3a3a;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: white;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid #555555;
    border-radius: 4px;
    font-size: 16px;
    color: white;
    background-color: #2c2c2c;
}

.form-group input:focus {
    border-color: #1a365d;
    outline: none;
    box-shadow: 0 0 0 3px rgba(26, 54, 93, 0.3);
}

.form-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    margin-top: 25px;
}

.form-actions button {
    padding: 12px 25px;
    font-size: 16px;
    font-weight: 500;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

#addBtn {
    background-color: #1a365d;
    color: white;
}

#addBtn:hover {
    background-color: #2c5282;
}

#editBtn {
    background-color: #1a365d;
    color: white;
}

#editBtn:hover {
    background-color: #2c5282;
}

#deleteBtn {
    background-color: #1a365d;
    color: white;
}

#deleteBtn:hover {
    background-color: #2c5282;
}

.form-actions button[type="reset"] {
    background-color: #1a365d;
    color: white;
}

.form-actions button[type="reset"]:hover {
    background-color: #2c5282;
}

/* Camera List Table */
.camera-list {
    background: #3a3a3a; /* Dark gray container */
    color: white;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

.camera-list h2 {
    color: white;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #555555;
}

table {
    width: 100%;
    border-collapse: collapse;
}

table th {
    background: #1a365d; /* Dark blue header */
    color: white;
    text-align: left;
    padding: 12px;
}

table td {
    padding: 12px;
    border-bottom: 1px solid #555555;
    color: white;
}

table tr:nth-child(even) {
    background-color: #4a4a4a;
}

table button.edit {
    background: #1a365d;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
}

table button.edit:hover {
    background: #2c5282;
}

/* Settings Page Styles */
.vibrant-input {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.3);
    color: #f8f9fa;
    padding: 12px;
    border-radius: 4px;
    width: 100%;
    font-size: 16px;
}

.vibrant-input:focus {
    background: rgba(255,255,255,0.2);
    border-color: #4fd1c5;
    outline: none;
    box-shadow: 0 0 0 3px rgba(79, 209, 197, 0.3);
}

.permissions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin: 15px 0;
}

.permission-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* User List Table */
.user-list {
    background: #3a3a3a;
    border-radius: 8px;
    padding: 25px;
    margin-top: 25px;
}

.user-list h3 {
    color: white;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #555555;
}

table {
    width: 100%;
    border-collapse: collapse;
}

table th {
    background: #1a365d;
    color: white;
    text-align: left;
    padding: 12px;
}

table td {
    padding: 12px;
    border-bottom: 1px solid #555555;
    color: white;
}

table tr:nth-child(even) {
    background-color: #4a4a4a;
}

table button.edit-user {
    background: #1a365d;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
    font-weight: 500;
}

table button.edit-user:hover {
    background: #2c5282;
}

/* Banking Footer */
.bank-footer {
    background: #3a3a3a; /* Dark gray footer */
    color: white;
    text-align: center;
    padding: 15px;
    font-size: 14px;
}

input[type="text"],
input[type="password"],
input[type="number"],
input[type="tel"],
input[type="url"],
select {
    color: white;
    background-color: #2c2c2c;
    border: 1px solid #555555;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: white;
}

/* Floating Toolbar */
.floating-toolbar {
    position: fixed;
    right: -280px;
    top: 50%;
    transform: translateY(-50%);
    width: 280px;
    background: linear-gradient(135deg, #3a3a3a 0%, #2c2c2c 100%);
    border-radius: 15px 0 0 15px;
    box-shadow: -5px 0 20px rgba(0,0,0,0.5);
    z-index: 1000;
    transition: right 0.3s ease-in-out;
    border: 2px solid #1a365d;
    border-right: none;
}

.floating-toolbar:hover {
    right: 0;
}

.floating-toolbar::before {
    content: '';
    position: absolute;
    left: -20px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 60px;
    background: linear-gradient(135deg, #1a365d 0%, #2c5282 100%);
    border-radius: 10px 0 0 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.floating-toolbar::after {
    content: '⚙️';
    position: absolute;
    left: -15px;
    top: 50%;
    transform: translateY(-50%);
    color: white;
    font-size: 16px;
    pointer-events: none;
}

.toolbar-header {
    background: linear-gradient(135deg, #1a365d 0%, #2c5282 100%);
    color: white;
    padding: 15px 20px;
    border-radius: 13px 0 0 0;
    text-align: center;
    font-weight: bold;
    font-size: 16px;
    border-bottom: 2px solid #555555;
}

.toolbar-section {
    padding: 15px 0;
    border-bottom: 1px solid #555555;
}

.toolbar-section:last-child {
    border-bottom: none;
}

.toolbar-section-title {
    color: #1a365d;
    font-weight: bold;
    font-size: 14px;
    padding: 0 20px 10px 20px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.toolbar-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    position: relative;
}

.toolbar-item:hover {
    background: linear-gradient(90deg, rgba(26, 54, 93, 0.3) 0%, rgba(44, 82, 130, 0.1) 100%);
    border-left-color: #1a365d;
    color: #ffffff;
    transform: translateX(5px);
}

.toolbar-item.active {
    background: linear-gradient(90deg, rgba(26, 54, 93, 0.5) 0%, rgba(44, 82, 130, 0.2) 100%);
    border-left-color: #1a365d;
    color: #ffffff;
}

.toolbar-item-icon {
    width: 20px;
    height: 20px;
    margin-left: 12px;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toolbar-item-text {
    flex: 1;
    font-size: 14px;
    font-weight: 500;
}

.toolbar-user {
    background: linear-gradient(135deg, #2c2c2c 0%, #1a1a1a 100%);
    padding: 15px 20px;
    border-radius: 0 0 0 13px;
    border-top: 2px solid #555555;
}

.toolbar-user-name {
    color: white;
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 5px;
}

.toolbar-user-role {
    color: #a0a0a0;
    font-size: 12px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .floating-toolbar {
        width: 250px;
        right: -250px;
    }

    .toolbar-item-text {
        font-size: 13px;
    }

    .toolbar-header {
        font-size: 14px;
        padding: 12px 15px;
    }
}