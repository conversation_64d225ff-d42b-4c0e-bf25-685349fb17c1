/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #121212; /* Dark background */
    color: white;
    line-height: 1.6;
}

/* Banking Header */
.bank-header {
    background: linear-gradient(to right, #301934, #4B0082); /* Dark purple gradient */
    color: #f0f0f0;
    padding: 15px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.5);
}

.bank-logo {
    font-size: 28px;
    font-weight: bold;
    letter-spacing: 1px;
}

.bank-nav ul {
    display: flex;
    list-style: none;
}

.bank-nav li {
    margin-left: 20px;
}

.bank-nav a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    padding: 5px 10px;
    border-radius: 4px;
    transition: background 0.3s;
}

.bank-nav a:hover {
    background: rgba(255,255,255,0.1);
}

/* Main Banking Container */
.bank-container {
    display: flex;
    min-height: calc(100vh - 120px);
}

/* Banking Sidebar */
.bank-sidebar {
    width: 250px;
    background: #212121; /* Dark sidebar */
    color: #f0f0f0;
    padding: 20px;
    border-right: 1px solid #424242;
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
}

.bank-sidebar h3 {
    color: white;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e1e4e8;
}

.bank-sidebar ul {
    list-style: none;
}

.bank-sidebar li {
    padding: 12px 15px;
    margin-bottom: 5px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
}

.bank-sidebar li:hover, .bank-sidebar h3:hover {
    color: black;
    background: white;
}

.bank-sidebar li.active {
    background: #4B0082; /* Dark purple active */
    color: white;
    font-weight: 500;
}

/* Main Content Area */
.bank-main {
    flex: 1;
    padding: 30px;
}

.bank-main h1 {
    color: white;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e1e4e8;
}

/* Camera Container */
.camera-container {
    background: #212121; /* Dark container */
    color: #f0f0f0;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

video {
    width: 100%;
    background-color: #000;
    border-radius: 6px;
    margin-bottom: 25px;
    border: 1px solid #ddd;
}

.controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 25px;
    flex-wrap: wrap;
}

button {
    padding: 12px 25px;
    font-size: 16px;
    font-weight: 500;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

#startBtn {
    background-color: #1a3a6c;
    color: white;
}

#startBtn:hover {
    background-color: #2c5282;
}

#recordBtn {
    background-color: #c53030;
    color: white;
}

#recordBtn:hover {
    background-color: #e53e3e;
}

#stopBtn {
    background-color: #2f855a;
    color: white;
}

#stopBtn:hover {
    background-color: #38a169;
}

#downloadBtn {
    background-color: #2b6cb0;
    color: white;
}

#downloadBtn:hover {
    background-color: #3182ce;
}

button:disabled {
    background-color: #cbd5e0;
    color: #718096;
    cursor: not-allowed;
}

/* Recordings Grid */
#recordings {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 20px;
    margin-top: 25px;
}

.recording {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.recording video {
    width: 100%;
    margin-bottom: 0;
    border-radius: 6px 6px 0 0;
}

.recording button {
    width: 100%;
    padding: 10px;
    background: #1a3a6c;
    color: white;
    border-radius: 0 0 6px 6px;
}

.recording button:hover {
    background: #2c5282;
}

/* Camera Management Form */
.form-container {
    background: white;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #1a3a6c;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid #cbd5e0;
    border-radius: 4px;
    font-size: 16px;
    color: black;
}

.form-group input:focus {
    border-color: #1a3a6c;
    outline: none;
    box-shadow: 0 0 0 3px rgba(26, 58, 108, 0.1);
}

.form-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    margin-top: 25px;
}

.form-actions button {
    padding: 12px 25px;
    font-size: 16px;
    font-weight: 500;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

#addBtn {
    background-color: #2f855a;
    color: white;
}

#addBtn:hover {
    background-color: #38a169;
}

#editBtn {
    background-color: #2b6cb0;
    color: white;
}

#editBtn:hover {
    background-color: #3182ce;
}

#deleteBtn {
    background-color: #c53030;
    color: white;
}

#deleteBtn:hover {
    background-color: #e53e3e;
}

.form-actions button[type="reset"] {
    background-color: #a0aec0;
    color: white;
}

.form-actions button[type="reset"]:hover {
    background-color: #cbd5e0;
}

/* Camera List Table */
.camera-list {
    background: #212121; /* Dark container */
    color: #f0f0f0;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

.camera-list h2 {
    color: #1a3a6c;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e2e8f0;
}

table {
    width: 100%;
    border-collapse: collapse;
}

table th {
    background: #4B0082; /* Dark purple header */
    color: #f0f0f0;
    text-align: left;
    padding: 12px;
}

table td {
    padding: 12px;
    border-bottom: 1px solid #e2e8f0;
}

table tr:nth-child(even) {
    background-color: #f8fafc;
}

table button.edit {
    background: #2b6cb0;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
}

table button.edit:hover {
    background: #3182ce;
}

/* Settings Page Styles */
.vibrant-input {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.3);
    color: #f8f9fa;
    padding: 12px;
    border-radius: 4px;
    width: 100%;
    font-size: 16px;
}

.vibrant-input:focus {
    background: rgba(255,255,255,0.2);
    border-color: #4fd1c5;
    outline: none;
    box-shadow: 0 0 0 3px rgba(79, 209, 197, 0.3);
}

.permissions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin: 15px 0;
}

.permission-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* User List Table */
.user-list {
    background: rgba(45, 55, 72, 0.3);
    border-radius: 8px;
    padding: 25px;
    margin-top: 25px;
}

.user-list h3 {
    color: #f8f9fa;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #4a5568;
}

table {
    width: 100%;
    border-collapse: collapse;
}

table th {
    background: #2d3748;
    color: #f8f9fa;
    text-align: left;
    padding: 12px;
}

table td {
    padding: 12px;
    border-bottom: 1px solid #4a5568;
    color: #f8f9fa;
}

table tr:nth-child(even) {
    background-color: rgba(45, 55, 72, 0.5);
}

table button.edit-user {
    background: #4fd1c5;
    color: #1a202c;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
    font-weight: 500;
}

table button.edit-user:hover {
    background: #81e6d9;
}

/* Banking Footer */
.bank-footer {
    background: #301934; /* Dark purple footer */
    color: #f0f0f0;
    text-align: center;
    padding: 15px;
    font-size: 14px;
}

input[type="text"],
input[type="password"],
input[type="number"],
input[type="tel"],
input[type="url"],
select {
    color: black;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: black;
}