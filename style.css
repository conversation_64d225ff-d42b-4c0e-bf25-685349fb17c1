/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #2c2c2c; /* Dark gray background */
    color: white;
    line-height: 1.6;
}

/* Banking Header */
.bank-header {
    background: linear-gradient(to right, #3a3a3a, #4a4a4a); /* Dark gray gradient */
    color: white;
    padding: 15px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.5);
}

.bank-logo {
    font-size: 28px;
    font-weight: bold;
    letter-spacing: 1px;
}

.bank-nav ul {
    display: flex;
    list-style: none;
}

.bank-nav li {
    margin-left: 20px;
}

.bank-nav a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    padding: 5px 10px;
    border-radius: 4px;
    transition: background 0.3s;
}

.bank-nav a:hover {
    background: rgba(255,255,255,0.1);
}

/* Main Banking Container */
.bank-container {
    display: flex;
    min-height: calc(100vh - 120px);
}

/* Banking Sidebar */
.bank-sidebar {
    width: 250px;
    background: #3a3a3a; /* Dark gray sidebar */
    color: white;
    padding: 20px;
    border-right: 1px solid #555555;
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
}

.bank-sidebar h3 {
    color: white;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e1e4e8;
}

.bank-sidebar ul {
    list-style: none;
}

.bank-sidebar li {
    padding: 12px 15px;
    margin-bottom: 5px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
}

.bank-sidebar li:hover, .bank-sidebar h3:hover {
    color: black;
    background: white;
}

.bank-sidebar li.active {
    background: #1a365d; /* Dark blue active */
    color: white;
    font-weight: 500;
}

/* Main Content Area */
.bank-main {
    flex: 1;
    padding: 30px;
}

.bank-main h1 {
    color: white;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e1e4e8;
}

/* Camera Container */
.camera-container {
    background: #3a3a3a; /* Dark gray container */
    color: white;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

video {
    width: 100%;
    background-color: #000;
    border-radius: 6px;
    margin-bottom: 25px;
    border: 1px solid #ddd;
}

.controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 25px;
    flex-wrap: wrap;
}

button {
    padding: 12px 25px;
    font-size: 16px;
    font-weight: 500;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

#startBtn {
    background-color: #1a365d;
    color: white;
}

#startBtn:hover {
    background-color: #2c5282;
}

#recordBtn {
    background-color: #1a365d;
    color: white;
}

#recordBtn:hover {
    background-color: #2c5282;
}

#stopBtn {
    background-color: #1a365d;
    color: white;
}

#stopBtn:hover {
    background-color: #2c5282;
}

#downloadBtn {
    background-color: #1a365d;
    color: white;
}

#downloadBtn:hover {
    background-color: #2c5282;
}

button:disabled {
    background-color: #cbd5e0;
    color: #718096;
    cursor: not-allowed;
}

/* Recordings Grid */
#recordings {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 20px;
    margin-top: 25px;
}

.recording {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.recording video {
    width: 100%;
    margin-bottom: 0;
    border-radius: 6px 6px 0 0;
}

.recording button {
    width: 100%;
    padding: 10px;
    background: #1a365d;
    color: white;
    border-radius: 0 0 6px 6px;
}

.recording button:hover {
    background: #2c5282;
}

/* Camera Management Form */
.form-container {
    background: #3a3a3a;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: white;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid #555555;
    border-radius: 4px;
    font-size: 16px;
    color: white;
    background-color: #2c2c2c;
}

.form-group input:focus {
    border-color: #1a365d;
    outline: none;
    box-shadow: 0 0 0 3px rgba(26, 54, 93, 0.3);
}

.form-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    margin-top: 25px;
}

.form-actions button {
    padding: 12px 25px;
    font-size: 16px;
    font-weight: 500;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

#addBtn {
    background-color: #1a365d;
    color: white;
}

#addBtn:hover {
    background-color: #2c5282;
}

#editBtn {
    background-color: #1a365d;
    color: white;
}

#editBtn:hover {
    background-color: #2c5282;
}

#deleteBtn {
    background-color: #1a365d;
    color: white;
}

#deleteBtn:hover {
    background-color: #2c5282;
}

.form-actions button[type="reset"] {
    background-color: #1a365d;
    color: white;
}

.form-actions button[type="reset"]:hover {
    background-color: #2c5282;
}

/* Camera List Table */
.camera-list {
    background: #3a3a3a; /* Dark gray container */
    color: white;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

.camera-list h2 {
    color: white;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #555555;
}

table {
    width: 100%;
    border-collapse: collapse;
}

table th {
    background: #1a365d; /* Dark blue header */
    color: white;
    text-align: left;
    padding: 12px;
}

table td {
    padding: 12px;
    border-bottom: 1px solid #555555;
    color: white;
}

table tr:nth-child(even) {
    background-color: #4a4a4a;
}

table button.edit {
    background: #1a365d;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
}

table button.edit:hover {
    background: #2c5282;
}

/* Settings Page Styles */
.vibrant-input {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.3);
    color: #f8f9fa;
    padding: 12px;
    border-radius: 4px;
    width: 100%;
    font-size: 16px;
}

.vibrant-input:focus {
    background: rgba(255,255,255,0.2);
    border-color: #4fd1c5;
    outline: none;
    box-shadow: 0 0 0 3px rgba(79, 209, 197, 0.3);
}

.permissions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin: 15px 0;
}

.permission-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* User List Table */
.user-list {
    background: #3a3a3a;
    border-radius: 8px;
    padding: 25px;
    margin-top: 25px;
}

.user-list h3 {
    color: white;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #555555;
}

table {
    width: 100%;
    border-collapse: collapse;
}

table th {
    background: #1a365d;
    color: white;
    text-align: left;
    padding: 12px;
}

table td {
    padding: 12px;
    border-bottom: 1px solid #555555;
    color: white;
}

table tr:nth-child(even) {
    background-color: #4a4a4a;
}

table button.edit-user {
    background: #1a365d;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
    font-weight: 500;
}

table button.edit-user:hover {
    background: #2c5282;
}

/* Banking Footer */
.bank-footer {
    background: #3a3a3a; /* Dark gray footer */
    color: white;
    text-align: center;
    padding: 15px;
    font-size: 14px;
}

input[type="text"],
input[type="password"],
input[type="number"],
input[type="tel"],
input[type="url"],
select {
    color: white;
    background-color: #2c2c2c;
    border: 1px solid #555555;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: white;
}