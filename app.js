// Get DOM elements
const video = document.getElementById('video');
const startBtn = document.getElementById('startBtn');
const recordBtn = document.getElementById('recordBtn');
const stopBtn = document.getElementById('stopBtn');
const downloadBtn = document.getElementById('downloadBtn');
const recordingsContainer = document.getElementById('recordings');

let mediaRecorder;
let recordedChunks = [];
let stream;

// Start camera when Start Camera button is clicked
startBtn.addEventListener('click', async () => {
    try {
        stream = await navigator.mediaDevices.getUserMedia({ video: true });
        video.srcObject = stream;
        recordBtn.disabled = false;
        startBtn.disabled = true;
    } catch (err) {
        console.error('Error accessing camera:', err);
        alert('Could not access camera. Please ensure you have a camera connected and permissions are granted.');
    }
});

// Start recording
recordBtn.addEventListener('click', () => {
    recordedChunks = [];
    mediaRecorder = new MediaRecorder(stream);
    
    mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
            recordedChunks.push(event.data);
        }
    };
    
    mediaRecorder.onstop = () => {
        const blob = new Blob(recordedChunks, { type: 'video/webm' });
        const url = URL.createObjectURL(blob);
        
        // Create download link
        downloadBtn.disabled = false;
        downloadBtn.onclick = () => {
            const a = document.createElement('a');
            a.href = url;
            a.download = `recording-${new Date().toISOString().replace(/:/g, '-')}.webm`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        };
        
        // Create preview
        const recordingElement = document.createElement('div');
        recordingElement.className = 'recording';
        recordingElement.innerHTML = `
            <video controls src="${url}" width="200"></video>
            <button class="download-recording">Download</button>
        `;
        recordingsContainer.appendChild(recordingElement);
        
        // Add download handler for this specific recording
        recordingElement.querySelector('.download-recording').onclick = () => {
            const a = document.createElement('a');
            a.href = url;
            a.download = `recording-${new Date().toISOString().replace(/:/g, '-')}.webm`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        };
    };
    
    mediaRecorder.start();
    recordBtn.disabled = true;
    stopBtn.disabled = false;
});

// Stop recording
stopBtn.addEventListener('click', () => {
    if (mediaRecorder && mediaRecorder.state !== 'inactive') {
        mediaRecorder.stop();
        recordBtn.disabled = false;
        stopBtn.disabled = true;
    }
});

// Initialize button states
recordBtn.disabled = true;
stopBtn.disabled = true;
downloadBtn.disabled = true;