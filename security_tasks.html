<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - تسجيل الأعمال اليومية - الأنظمة الأمنية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(26, 54, 93, 0.9);
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(45deg, #1a365d, #2c5282);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .logo-text {
            font-size: 28px;
            font-weight: 900;
            color: white;
        }
        
        .nav-buttons {
            display: flex;
            gap: 15px;
        }
        
        .nav-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
        }
        
        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .page-title {
            text-align: center;
            font-size: 36px;
            font-weight: 900;
            margin-bottom: 10px;
            color: #3182ce;
            text-shadow: 0 0 20px rgba(49, 130, 206, 0.5);
        }
        
        .page-subtitle {
            text-align: center;
            font-size: 18px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 40px;
        }
        
        .controls-panel {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(20px);
        }
        
        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .control-label {
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
            font-size: 14px;
        }
        
        .control-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 12px;
            color: white;
            font-size: 14px;
        }
        
        .control-input:focus {
            outline: none;
            border-color: #3182ce;
            box-shadow: 0 0 0 2px rgba(49, 130, 206, 0.2);
        }
        
        .control-input option {
            background: #1a1a2e;
            color: white;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .action-btn {
            background: linear-gradient(135deg, #1a365d, #2c5282);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(26, 54, 93, 0.4);
        }
        
        .add-btn {
            background: linear-gradient(135deg, #48bb78, #38a169);
        }
        
        .export-btn {
            background: linear-gradient(135deg, #ed8936, #dd6b20);
        }
        
        .email-btn {
            background: linear-gradient(135deg, #9f7aea, #805ad5);
        }
        
        .report-btn {
            background: linear-gradient(135deg, #4299e1, #3182ce);
        }
        
        .panel {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(20px);
        }
        
        .panel-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 20px;
            color: white;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .tasks-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: rgba(255, 255, 255, 0.02);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .tasks-table th,
        .tasks-table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 13px;
        }
        
        .tasks-table th {
            background: linear-gradient(135deg, #1a365d, #2c5282);
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .tasks-table td {
            color: rgba(255, 255, 255, 0.9);
        }
        
        .tasks-table tbody tr {
            transition: all 0.3s ease;
        }
        
        .tasks-table tbody tr:hover {
            background: rgba(255, 255, 255, 0.05);
            transform: scale(1.01);
        }
        
        .status-pending {
            background: linear-gradient(135deg, #ed8936, #dd6b20);
            color: white;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 600;
        }
        
        .status-completed {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 600;
        }
        
        .status-postponed {
            background: linear-gradient(135deg, #f56565, #e53e3e);
            color: white;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 600;
        }
        
        .table-action-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 6px 10px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 11px;
            margin: 0 2px;
            transition: all 0.3s ease;
        }
        
        .table-action-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.05);
        }
        
        .edit-btn {
            background: linear-gradient(135deg, #f6e05e, #d69e2e);
            color: black;
        }
        
        .delete-btn {
            background: linear-gradient(135deg, #f56565, #e53e3e);
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
        }
        
        .modal-content {
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            margin: 3% auto;
            padding: 30px;
            border-radius: 20px;
            width: 90%;
            max-width: 600px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            max-height: 90vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .modal-title {
            font-size: 24px;
            font-weight: 700;
            color: #3182ce;
        }
        
        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 28px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .close-btn:hover {
            color: #f56565;
            transform: scale(1.1);
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 12px;
            color: white;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #3182ce;
            box-shadow: 0 0 0 2px rgba(49, 130, 206, 0.2);
            background: rgba(255, 255, 255, 0.15);
        }
        
        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        .save-btn {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
            font-size: 16px;
        }
        
        .save-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            backdrop-filter: blur(20px);
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }
        
        .stat-icon {
            font-size: 32px;
            margin-bottom: 10px;
            display: block;
        }
        
        .stat-value {
            font-size: 28px;
            font-weight: 900;
            color: #3182ce;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        @media (max-width: 768px) {
            .controls-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .tasks-table {
                font-size: 11px;
            }
            
            .tasks-table th,
            .tasks-table td {
                padding: 8px 6px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">🏦</div>
                <div class="logo-text">EG BANK</div>
            </div>
            
            <div class="nav-buttons">
                <a href="home_simple.html" class="nav-btn">🏠 الرئيسية</a>
                <a href="cameras.html" class="nav-btn">📹 المراقبة</a>
                <a href="gps.html" class="nav-btn">🛰️ تتبع الصرافات</a>
                <a href="smartvoice_attendance.html" class="nav-btn">🎤 الحضور</a>
            </div>
        </div>
    </header>
    
    <div class="container">
        <h1 class="page-title">🔧 تسجيل الأعمال اليومية</h1>
        <p class="page-subtitle">إدارة مهام الأنظمة الأمنية والصيانة</p>
        
        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <span class="stat-icon">📋</span>
                <div class="stat-value" id="totalTasks">0</div>
                <div class="stat-label">إجمالي المهام</div>
            </div>
            
            <div class="stat-card">
                <span class="stat-icon">⏳</span>
                <div class="stat-value" id="pendingTasks">0</div>
                <div class="stat-label">قيد التنفيذ</div>
            </div>
            
            <div class="stat-card">
                <span class="stat-icon">✅</span>
                <div class="stat-value" id="completedTasks">0</div>
                <div class="stat-label">مكتملة</div>
            </div>
            
            <div class="stat-card">
                <span class="stat-icon">⏸️</span>
                <div class="stat-value" id="postponedTasks">0</div>
                <div class="stat-label">مؤجلة</div>
            </div>
        </div>
        
        <!-- Controls Panel -->
        <div class="controls-panel">
            <div class="controls-grid">
                <div class="control-group">
                    <label class="control-label">من تاريخ:</label>
                    <input type="date" class="control-input" id="startDate">
                </div>
                
                <div class="control-group">
                    <label class="control-label">إلى تاريخ:</label>
                    <input type="date" class="control-input" id="endDate">
                </div>
                
                <div class="control-group">
                    <label class="control-label">نوع المهمة:</label>
                    <select class="control-input" id="taskTypeFilter">
                        <option value="">جميع الأنواع</option>
                        <option value="صيانة">صيانة</option>
                        <option value="تركيب">تركيب</option>
                        <option value="فحص">فحص</option>
                        <option value="إصلاح">إصلاح</option>
                        <option value="تحديث">تحديث</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label class="control-label">الحالة:</label>
                    <select class="control-input" id="statusFilter">
                        <option value="">جميع الحالات</option>
                        <option value="قيد التنفيذ">قيد التنفيذ</option>
                        <option value="مكتمل">مكتمل</option>
                        <option value="مؤجل">مؤجل</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label class="control-label">العميل/الموقع:</label>
                    <input type="text" class="control-input" id="clientFilter" placeholder="البحث بالعميل أو الموقع">
                </div>
                
                <div class="control-group">
                    <label class="control-label">نوع النظام:</label>
                    <select class="control-input" id="systemTypeFilter">
                        <option value="">جميع الأنظمة</option>
                        <option value="كاميرات مراقبة">كاميرات مراقبة</option>
                        <option value="أنظمة إنذار">أنظمة إنذار</option>
                        <option value="أنظمة دخول">أنظمة دخول</option>
                        <option value="أنظمة حريق">أنظمة حريق</option>
                        <option value="شبكات">شبكات</option>
                    </select>
                </div>
            </div>
            
            <div class="action-buttons">
                <button class="action-btn add-btn" onclick="openAddTaskModal()">
                    ➕ إضافة مهمة جديدة
                </button>
                <button class="action-btn" onclick="filterTasks()">
                    🔍 تطبيق الفلتر
                </button>
                <button class="action-btn report-btn" onclick="generateReport()">
                    📊 تقرير مفصل
                </button>
                <button class="action-btn export-btn" onclick="exportToPDF()">
                    📄 تصدير PDF
                </button>
                <button class="action-btn export-btn" onclick="exportToExcel()">
                    📊 تصدير Excel
                </button>
                <button class="action-btn email-btn" onclick="sendEmailReport()">
                    📧 إرسال بالإيميل
                </button>
            </div>
        </div>
        
        <!-- Tasks Table -->
        <div class="panel">
            <h2 class="panel-title">📋 جدول المهام اليومية</h2>
            
            <div style="overflow-x: auto;">
                <table class="tasks-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>وصف المهمة</th>
                            <th>نوع المهمة</th>
                            <th>العميل/الموقع</th>
                            <th>نوع النظام</th>
                            <th>تاريخ التنفيذ</th>
                            <th>وقت التنفيذ</th>
                            <th>الحالة</th>
                            <th>المسؤول</th>
                            <th>ملاحظات</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="tasksTableBody">
                        <!-- Data will be populated here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Add/Edit Task Modal -->
    <div id="taskModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">إضافة مهمة جديدة</h3>
                <button class="close-btn" onclick="closeTaskModal()">&times;</button>
            </div>
            
            <form id="taskForm">
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">نوع المهمة:</label>
                        <select class="form-input" id="taskType" required>
                            <option value="">اختر نوع المهمة</option>
                            <option value="صيانة">صيانة</option>
                            <option value="تركيب">تركيب</option>
                            <option value="فحص">فحص</option>
                            <option value="إصلاح">إصلاح</option>
                            <option value="تحديث">تحديث</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">العميل/الموقع:</label>
                        <input type="text" class="form-input" id="taskClient" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">نوع النظام:</label>
                        <select class="form-input" id="taskSystemType" required>
                            <option value="">اختر نوع النظام</option>
                            <option value="كاميرات مراقبة">كاميرات مراقبة</option>
                            <option value="أنظمة إنذار">أنظمة إنذار</option>
                            <option value="أنظمة دخول">أنظمة دخول</option>
                            <option value="أنظمة حريق">أنظمة حريق</option>
                            <option value="شبكات">شبكات</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">تاريخ التنفيذ:</label>
                        <input type="date" class="form-input" id="taskDate" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">وقت التنفيذ:</label>
                        <input type="time" class="form-input" id="taskTime" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">الحالة:</label>
                        <select class="form-input" id="taskStatus" required>
                            <option value="قيد التنفيذ">قيد التنفيذ</option>
                            <option value="مكتمل">مكتمل</option>
                            <option value="مؤجل">مؤجل</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">المسؤول:</label>
                        <input type="text" class="form-input" id="taskResponsible" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">وصف المهمة:</label>
                    <textarea class="form-input form-textarea" id="taskDescription" required placeholder="وصف تفصيلي للمهمة..."></textarea>
                </div>
                
                <div class="form-group">
                    <label class="form-label">ملاحظات:</label>
                    <textarea class="form-input form-textarea" id="taskNotes" placeholder="أي ملاحظات إضافية..."></textarea>
                </div>
                
                <button type="submit" class="save-btn">💾 حفظ المهمة</button>
            </form>
        </div>
    </div>
    
    <script src="security_tasks.js"></script>
</body>
</html>
