<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - Futuristic Security Dashboard</title>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Cairo:wght@300;400;600&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: #000;
            color: #fff;
            overflow-x: hidden;
            perspective: 1000px;
        }
        
        /* Cyberpunk Grid Background */
        .cyber-grid {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: gridMove 20s linear infinite;
            z-index: -2;
        }
        
        @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }
        
        /* Holographic Background */
        .holo-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 20% 50%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 40% 80%, rgba(0, 255, 0, 0.1) 0%, transparent 50%);
            animation: holoShift 15s ease-in-out infinite;
            z-index: -1;
        }
        
        @keyframes holoShift {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.7; }
        }
        
        /* Futuristic Header */
        .cyber-header {
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(255, 0, 255, 0.1));
            backdrop-filter: blur(20px);
            border-bottom: 2px solid #00ffff;
            padding: 20px 0;
            position: relative;
            overflow: hidden;
        }
        
        .cyber-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.2), transparent);
            animation: scanLine 3s linear infinite;
        }
        
        @keyframes scanLine {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        
        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 30px;
            position: relative;
            z-index: 1;
        }
        
        .cyber-logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo-hologram {
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #00ffff, #ff00ff);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            position: relative;
            animation: logoSpin 10s linear infinite;
            box-shadow: 0 0 30px #00ffff;
        }
        
        @keyframes logoSpin {
            0% { transform: rotateY(0deg); }
            100% { transform: rotateY(360deg); }
        }
        
        .logo-text {
            font-family: 'Orbitron', monospace;
            font-size: 32px;
            font-weight: 900;
            background: linear-gradient(45deg, #00ffff, #ff00ff, #00ff00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 20px #00ffff;
        }
        
        .cyber-time {
            font-family: 'Orbitron', monospace;
            font-size: 18px;
            color: #00ffff;
            text-shadow: 0 0 10px #00ffff;
        }
        
        /* Main Dashboard */
        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 30px;
        }
        
        .dashboard-title {
            text-align: center;
            margin-bottom: 50px;
        }
        
        .title-main {
            font-family: 'Orbitron', monospace;
            font-size: 48px;
            font-weight: 900;
            background: linear-gradient(45deg, #00ffff, #ff00ff, #00ff00, #ffff00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: titleGlow 3s ease-in-out infinite;
            margin-bottom: 15px;
        }
        
        @keyframes titleGlow {
            0%, 100% { filter: brightness(1); }
            50% { filter: brightness(1.5); }
        }
        
        .title-sub {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 300;
        }
        
        /* 3D Cards Grid */
        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }
        
        .cyber-card {
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(255, 0, 255, 0.1));
            backdrop-filter: blur(20px);
            border: 1px solid #00ffff;
            border-radius: 20px;
            padding: 30px;
            position: relative;
            transform-style: preserve-3d;
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            cursor: pointer;
            overflow: hidden;
        }
        
        .cyber-card::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #00ffff, #ff00ff, #00ff00, #ffff00);
            border-radius: 20px;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s;
        }
        
        .cyber-card:hover::before {
            opacity: 1;
        }
        
        .cyber-card:hover {
            transform: translateY(-15px) rotateX(10deg) rotateY(5deg);
            box-shadow: 0 25px 50px rgba(0, 255, 255, 0.3);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .card-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #00ffff, #ff00ff);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            animation: iconFloat 3s ease-in-out infinite;
        }
        
        @keyframes iconFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }
        
        .card-title {
            font-family: 'Orbitron', monospace;
            font-size: 20px;
            font-weight: 700;
            color: #00ffff;
            text-shadow: 0 0 10px #00ffff;
        }
        
        .card-content {
            margin-bottom: 20px;
        }
        
        .card-description {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .card-metrics {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
        }
        
        .metric-item {
            text-align: center;
            padding: 10px;
            background: rgba(0, 255, 255, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(0, 255, 255, 0.3);
        }
        
        .metric-value {
            font-family: 'Orbitron', monospace;
            font-size: 24px;
            font-weight: 700;
            color: #00ffff;
            text-shadow: 0 0 10px #00ffff;
        }
        
        .metric-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
            margin-top: 5px;
        }
        
        /* Holographic Controls */
        .holo-controls {
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.05), rgba(255, 0, 255, 0.05));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 50px;
        }
        
        .controls-title {
            font-family: 'Orbitron', monospace;
            font-size: 24px;
            font-weight: 700;
            color: #00ffff;
            text-align: center;
            margin-bottom: 25px;
            text-shadow: 0 0 15px #00ffff;
        }
        
        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .holo-button {
            background: linear-gradient(45deg, rgba(0, 255, 255, 0.2), rgba(255, 0, 255, 0.2));
            border: 2px solid #00ffff;
            border-radius: 15px;
            padding: 20px;
            color: #fff;
            font-family: 'Orbitron', monospace;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            text-align: center;
        }
        
        .holo-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .holo-button:hover::before {
            left: 100%;
        }
        
        .holo-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 255, 255, 0.4);
            border-color: #ff00ff;
        }
        
        .button-icon {
            font-size: 24px;
            margin-bottom: 10px;
            display: block;
        }
        
        /* Status Panel */
        .status-panel {
            background: linear-gradient(135deg, rgba(0, 255, 0, 0.1), rgba(0, 255, 255, 0.1));
            backdrop-filter: blur(20px);
            border: 1px solid #00ff00;
            border-radius: 20px;
            padding: 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-led {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background: #00ff00;
            box-shadow: 0 0 15px #00ff00;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .status-text {
            font-family: 'Orbitron', monospace;
            color: #00ff00;
            font-weight: 600;
            text-shadow: 0 0 10px #00ff00;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .header-content {
                padding: 0 20px;
            }
            
            .dashboard-container {
                padding: 20px 15px;
            }
            
            .title-main {
                font-size: 32px;
            }
            
            .cards-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .controls-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .status-panel {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Cyberpunk Grid Background -->
    <div class="cyber-grid"></div>
    
    <!-- Holographic Background -->
    <div class="holo-bg"></div>
    
    <!-- Futuristic Header -->
    <header class="cyber-header">
        <div class="header-content">
            <div class="cyber-logo">
                <div class="logo-hologram">🏦</div>
                <div class="logo-text">EG BANK</div>
            </div>
            
            <div style="display: flex; align-items: center; gap: 20px;">
                <div class="cyber-time" id="cyberTime">
                    <!-- Time will be updated by JavaScript -->
                </div>
                <button onclick="logout()" style="background: linear-gradient(45deg, rgba(255, 0, 255, 0.2), rgba(0, 255, 255, 0.2)); border: 2px solid #ff00ff; border-radius: 10px; padding: 10px 15px; color: #ff00ff; font-family: 'Orbitron', monospace; font-weight: 600; cursor: pointer; transition: all 0.3s ease;" onmouseover="this.style.borderColor='#00ffff'; this.style.color='#00ffff'" onmouseout="this.style.borderColor='#ff00ff'; this.style.color='#ff00ff'">
                    LOGOUT
                </button>
            </div>
        </div>
    </header>
    
    <!-- Main Dashboard -->
    <div class="dashboard-container">
        <!-- Dashboard Title -->
        <div class="dashboard-title">
            <h1 class="title-main">SECURITY MATRIX</h1>
            <p class="title-sub">نظام الأمان المتقدم - الجيل القادم</p>
        </div>
        
        <!-- 3D Cards Grid -->
        <div class="cards-grid">
            <div class="cyber-card" onclick="openSystem('cameras')">
                <div class="card-header">
                    <div class="card-icon">📹</div>
                    <div class="card-title">SURVEILLANCE NET</div>
                </div>
                <div class="card-content">
                    <div class="card-description">شبكة مراقبة ذكية مع تحليل فوري للصور وكشف التهديدات</div>
                    <div class="card-metrics">
                        <div class="metric-item">
                            <div class="metric-value">24</div>
                            <div class="metric-label">NODES</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">98%</div>
                            <div class="metric-label">UPTIME</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">4K</div>
                            <div class="metric-label">QUALITY</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="cyber-card" onclick="openSystem('access')">
                <div class="card-header">
                    <div class="card-icon">🔐</div>
                    <div class="card-title">ACCESS MATRIX</div>
                </div>
                <div class="card-content">
                    <div class="card-description">نظام تحكم متقدم بالوصول مع مصادقة بيومترية وذكاء اصطناعي</div>
                    <div class="card-metrics">
                        <div class="metric-item">
                            <div class="metric-value">12</div>
                            <div class="metric-label">GATES</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">156</div>
                            <div class="metric-label">USERS</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">99%</div>
                            <div class="metric-label">SECURE</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="cyber-card" onclick="openSystem('gps')">
                <div class="card-header">
                    <div class="card-icon">🛰️</div>
                    <div class="card-title">TRACKING GRID</div>
                </div>
                <div class="card-content">
                    <div class="card-description">تتبع متقدم للأصول مع GPS عالي الدقة وتحليل المسارات</div>
                    <div class="card-metrics">
                        <div class="metric-item">
                            <div class="metric-value">8</div>
                            <div class="metric-label">ASSETS</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">247</div>
                            <div class="metric-label">KM TODAY</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">LIVE</div>
                            <div class="metric-label">STATUS</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="cyber-card" onclick="openSystem('analytics')">
                <div class="card-header">
                    <div class="card-icon">🧠</div>
                    <div class="card-title">AI ANALYTICS</div>
                </div>
                <div class="card-content">
                    <div class="card-description">تحليلات ذكية مدعومة بالذكاء الاصطناعي للتنبؤ بالتهديدات</div>
                    <div class="card-metrics">
                        <div class="metric-item">
                            <div class="metric-value">45</div>
                            <div class="metric-label">REPORTS</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">12</div>
                            <div class="metric-label">ALERTS</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">AI</div>
                            <div class="metric-label">POWERED</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Holographic Controls -->
        <div class="holo-controls">
            <h2 class="controls-title">QUANTUM CONTROLS</h2>
            <div class="controls-grid">
                <button class="holo-button" onclick="quantumAction('emergency')">
                    <span class="button-icon">🚨</span>
                    EMERGENCY PROTOCOL
                </button>
                <button class="holo-button" onclick="quantumAction('lockdown')">
                    <span class="button-icon">🔒</span>
                    FACILITY LOCKDOWN
                </button>
                <button class="holo-button" onclick="quantumAction('scan')">
                    <span class="button-icon">🔍</span>
                    DEEP SCAN MODE
                </button>
                <button class="holo-button" onclick="quantumAction('shield')">
                    <span class="button-icon">🛡️</span>
                    CYBER SHIELD
                </button>
                <button class="holo-button" onclick="switchInterface()">
                    <span class="button-icon">🎨</span>
                    MODERN INTERFACE
                </button>
            </div>
        </div>
        
        <!-- Status Panel -->
        <div class="status-panel">
            <div class="status-item">
                <div class="status-led"></div>
                <div class="status-text">QUANTUM CORE: ONLINE</div>
            </div>
            <div class="status-item">
                <div class="status-text">NEURAL NETWORK: ACTIVE</div>
            </div>
            <div class="status-item">
                <div class="status-text">SECURITY LEVEL: MAXIMUM</div>
            </div>
        </div>
    </div>
    
    <script>
        // Update cyber time
        function updateCyberTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-US', { 
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            const dateString = now.toLocaleDateString('en-US', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
            
            document.getElementById('cyberTime').textContent = `${dateString} | ${timeString}`;
        }
        
        // System navigation
        function openSystem(system) {
            const routes = {
                cameras: 'cameras.html',
                access: 'access.html',
                gps: 'gps.html',
                analytics: 'reports.html'
            };
            
            if (routes[system]) {
                showQuantumTransition();
                setTimeout(() => {
                    window.location.href = routes[system];
                }, 1000);
            }
        }
        
        // Quantum actions
        function quantumAction(action) {
            const actions = {
                emergency: 'EMERGENCY PROTOCOL ACTIVATED',
                lockdown: 'FACILITY LOCKDOWN INITIATED',
                scan: 'DEEP SCAN MODE ENGAGED',
                shield: 'CYBER SHIELD DEPLOYED'
            };
            
            showQuantumNotification(actions[action] || 'QUANTUM ACTION EXECUTED');
        }
        
        // Show quantum transition effect
        function showQuantumTransition() {
            const transition = document.createElement('div');
            transition.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(45deg, #00ffff, #ff00ff);
                z-index: 9999;
                opacity: 0;
                transition: opacity 0.5s ease;
            `;
            
            document.body.appendChild(transition);
            
            setTimeout(() => {
                transition.style.opacity = '0.8';
            }, 10);
            
            setTimeout(() => {
                transition.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(transition);
                }, 500);
            }, 500);
        }
        
        // Show quantum notification
        function showQuantumNotification(message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(45deg, rgba(0, 255, 255, 0.2), rgba(255, 0, 255, 0.2));
                backdrop-filter: blur(20px);
                border: 2px solid #00ffff;
                border-radius: 15px;
                padding: 20px;
                color: #00ffff;
                font-family: 'Orbitron', monospace;
                font-weight: 600;
                text-shadow: 0 0 10px #00ffff;
                z-index: 1001;
                animation: quantumSlide 0.5s ease;
            `;
            
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }
        
        // Add quantum slide animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes quantumSlide {
                from {
                    opacity: 0;
                    transform: translateX(100px);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }
        `;
        document.head.appendChild(style);
        
        // Check authentication
        function checkAuth() {
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            const username = localStorage.getItem('username');

            if (!isLoggedIn || isLoggedIn !== 'true') {
                window.location.href = 'login.html';
                return false;
            }

            return true;
        }

        // Logout function
        function logout() {
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('username');
            localStorage.removeItem('loginTime');
            window.location.href = 'login.html';
        }

        // Switch to modern interface
        function switchInterface() {
            showQuantumNotification('SWITCHING TO MODERN INTERFACE...');
            setTimeout(() => {
                window.location.href = 'modern_interface.html';
            }, 1000);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication first
            if (!checkAuth()) {
                return;
            }

            updateCyberTime();
            setInterval(updateCyberTime, 1000);
            console.log('Futuristic Security Dashboard Initialized');
        });
    </script>
</body>
</html>
