// Advanced User Management System
// نظام إدارة المستخدمين المتقدم

// Global variables
let users = JSON.parse(localStorage.getItem('systemUsers')) || [];
let currentEditingUser = null;

// Default admin user
const defaultUsers = [
    {
        id: 'admin001',
        username: 'admin',
        fullName: 'عبد الفتاح محمود',
        email: '<EMAIL>',
        password: 'admin123',
        role: 'admin',
        permissions: ['camera', 'access', 'gps', 'reports', 'settings', 'users'],
        status: 'active',
        createdAt: new Date().toISOString(),
        lastLogin: new Date().toISOString()
    }
];

document.addEventListener('DOMContentLoaded', () => {
    initializeUserSystem();
    loadCurrentUser();
    renderUsersTable();
    setupEventListeners();
});

// Initialize user system
function initializeUserSystem() {
    if (users.length === 0) {
        users = [...defaultUsers];
        saveUsers();
    }
    console.log('User Management System Initialized');
}

// Load current user info
function loadCurrentUser() {
    const currentUsername = localStorage.getItem('username') || 'admin';
    const currentUser = users.find(u => u.username === currentUsername);

    if (currentUser) {
        document.getElementById('currentUsername').value = currentUser.username;
    }
}

// Setup event listeners
function setupEventListeners() {
    // Add user button
    document.getElementById('addUserBtn').addEventListener('click', addNewUser);

    // Edit user button
    document.getElementById('editUserBtn').addEventListener('click', updateUser);

    // Delete user button
    document.getElementById('deleteUserBtn').addEventListener('click', deleteUser);

    // Form reset
    document.querySelector('button[type="reset"]').addEventListener('click', resetForm);
}
    
// Update current user settings
function updateCurrentUser() {
    const currentUsername = localStorage.getItem('username') || 'admin';
    const newUsername = document.getElementById('newUsername').value;
    const currentPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;

    const currentUser = users.find(u => u.username === currentUsername);

    if (!currentUser) {
        showNotification('المستخدم الحالي غير موجود', 'error');
        return;
    }

    // Validate current password
    if (currentPassword && currentUser.password !== currentPassword) {
        showNotification('كلمة المرور الحالية غير صحيحة', 'error');
        return;
    }

    // Validate new password
    if (newPassword && newPassword !== confirmPassword) {
        showNotification('كلمة المرور الجديدة غير متطابقة', 'error');
        return;
    }

    // Update username if provided
    if (newUsername && newUsername !== currentUsername) {
        if (users.find(u => u.username === newUsername)) {
            showNotification('اسم المستخدم موجود بالفعل', 'error');
            return;
        }
        currentUser.username = newUsername;
        localStorage.setItem('username', newUsername);
    }

    // Update password if provided
    if (newPassword) {
        currentUser.password = newPassword;
    }

    saveUsers();
    loadCurrentUser();
    resetCurrentUserForm();
    showNotification('تم تحديث بيانات المستخدم بنجاح', 'success');
}

// Reset current user form
function resetCurrentUserForm() {
    document.getElementById('newUsername').value = '';
    document.getElementById('currentPassword').value = '';
    document.getElementById('newPassword').value = '';
    document.getElementById('confirmPassword').value = '';
}

// Add new user
function addNewUser() {
    const username = document.getElementById('username').value;
    const fullName = document.getElementById('fullName').value;
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const role = document.getElementById('userRole').value;
    const permissions = getSelectedPermissions();

    if (!username || !fullName || !password || !role) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    if (users.find(u => u.username === username)) {
        showNotification('اسم المستخدم موجود بالفعل', 'error');
        return;
    }

    const newUser = {
        id: 'user' + Date.now(),
        username,
        fullName,
        email,
        password,
        role,
        permissions,
        status: 'active',
        createdAt: new Date().toISOString(),
        lastLogin: null
    };

    users.push(newUser);
    saveUsers();
    renderUsersTable();
    resetForm();
    showNotification('تم إضافة المستخدم بنجاح', 'success');
}
    
// Update user
function updateUser() {
    if (!currentEditingUser) {
        showNotification('لم يتم تحديد مستخدم للتعديل', 'error');
        return;
    }

    const username = document.getElementById('username').value;
    const fullName = document.getElementById('fullName').value;
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const role = document.getElementById('userRole').value;
    const permissions = getSelectedPermissions();

    if (!username || !fullName || !role) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    // Check if username already exists (excluding current user)
    if (username !== currentEditingUser.username && users.find(u => u.username === username)) {
        showNotification('اسم المستخدم موجود بالفعل', 'error');
        return;
    }

    // Update user data
    currentEditingUser.username = username;
    currentEditingUser.fullName = fullName;
    currentEditingUser.email = email;
    if (password) currentEditingUser.password = password;
    currentEditingUser.role = role;
    currentEditingUser.permissions = permissions;

    saveUsers();
    renderUsersTable();
    resetForm();
    currentEditingUser = null;

    // Reset buttons
    document.getElementById('editUserBtn').disabled = true;
    document.getElementById('deleteUserBtn').disabled = true;
    document.getElementById('addUserBtn').textContent = '➕ إضافة مستخدم';

    showNotification('تم تحديث المستخدم بنجاح', 'success');
}

// Delete user
function deleteUser() {
    if (!currentEditingUser) {
        showNotification('لم يتم تحديد مستخدم للحذف', 'error');
        return;
    }

    if (currentEditingUser.username === 'admin') {
        showNotification('لا يمكن حذف المدير الرئيسي', 'error');
        return;
    }

    if (confirm(`هل أنت متأكد من حذف المستخدم ${currentEditingUser.fullName}؟`)) {
        const index = users.findIndex(u => u.id === currentEditingUser.id);
        if (index !== -1) {
            users.splice(index, 1);
            saveUsers();
            renderUsersTable();
            resetForm();
            currentEditingUser = null;

            // Reset buttons
            document.getElementById('editUserBtn').disabled = true;
            document.getElementById('deleteUserBtn').disabled = true;
            document.getElementById('addUserBtn').textContent = '➕ إضافة مستخدم';

            showNotification('تم حذف المستخدم بنجاح', 'success');
        }
    }
}
    
// Render users table
function renderUsersTable() {
    const tbody = document.getElementById('usersTableBody');
    if (!tbody) return;

    tbody.innerHTML = '';

    users.forEach(user => {
        const row = tbody.insertRow();
        row.innerHTML = `
            <td style="padding: 15px; color: white; border-bottom: 1px solid #444;">${user.username}</td>
            <td style="padding: 15px; color: white; border-bottom: 1px solid #444;">${user.fullName}</td>
            <td style="padding: 15px; color: white; border-bottom: 1px solid #444;">${user.email || 'غير محدد'}</td>
            <td style="padding: 15px; color: white; border-bottom: 1px solid #444;">${getRoleText(user.role)}</td>
            <td style="padding: 15px; color: white; border-bottom: 1px solid #444;">${user.permissions.length} صلاحية</td>
            <td style="padding: 15px; border-bottom: 1px solid #444;">
                <span style="background: ${user.status === 'active' ? '#48bb78' : '#f56565'}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                    ${user.status === 'active' ? 'نشط' : 'غير نشط'}
                </span>
            </td>
            <td style="padding: 15px; text-align: center; border-bottom: 1px solid #444;">
                <button onclick="editUserData('${user.id}')" style="background: #f6e05e; color: black; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; margin-left: 5px;">
                    ✏️ تعديل
                </button>
                <button onclick="toggleUserStatus('${user.id}')" style="background: ${user.status === 'active' ? '#f56565' : '#48bb78'}; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer;">
                    ${user.status === 'active' ? '🚫 إيقاف' : '✅ تفعيل'}
                </button>
            </td>
        `;
    });
}

// Edit user data
function editUserData(userId) {
    const user = users.find(u => u.id === userId);
    if (!user) return;

    currentEditingUser = user;

    // Fill form with user data
    document.getElementById('username').value = user.username;
    document.getElementById('fullName').value = user.fullName;
    document.getElementById('email').value = user.email || '';
    document.getElementById('password').value = '';
    document.getElementById('userRole').value = user.role;

    // Set permissions
    document.querySelectorAll('input[name="permissions"]').forEach(checkbox => {
        checkbox.checked = user.permissions.includes(checkbox.value);
    });

    // Update buttons
    document.getElementById('editUserBtn').disabled = false;
    document.getElementById('deleteUserBtn').disabled = false;
    document.getElementById('addUserBtn').textContent = '💾 تحديث المستخدم';

    // Scroll to form
    document.getElementById('userForm').scrollIntoView({ behavior: 'smooth' });
}

// Toggle user status
function toggleUserStatus(userId) {
    const user = users.find(u => u.id === userId);
    if (!user) return;

    if (user.username === 'admin') {
        showNotification('لا يمكن تغيير حالة المدير الرئيسي', 'error');
        return;
    }

    user.status = user.status === 'active' ? 'inactive' : 'active';
    saveUsers();
    renderUsersTable();

    const statusText = user.status === 'active' ? 'تم تفعيل' : 'تم إيقاف';
    showNotification(`${statusText} المستخدم ${user.fullName}`, 'success');
}
    
    // Edit user
    editUserBtn.addEventListener('click', () => {
        if (!editingUser) return;
        
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        const permissions = getSelectedPermissions();
        
        if (!username) {
            alert('Username is required');
            return;
        }
        
        const updatedUser = {
            username,
            password: password || editingUser.password,
            permissions
        };
        
        if (updateUser(editingUser.username, updatedUser)) {
            renderUserList();
            document.getElementById('userForm').reset();
            editingUser = null;
            editUserBtn.disabled = true;
            deleteUserBtn.disabled = true;
            addUserBtn.textContent = 'Add User';
        } else {
            alert('Failed to update user');
        }
    });
    
    // Delete user
    deleteUserBtn.addEventListener('click', () => {
        if (!editingUser) return;
        
        if (confirm(`Are you sure you want to delete user ${editingUser.username}?`)) {
            const index = users.findIndex(u => u.username === editingUser.username);
            if (index !== -1) {
                users.splice(index, 1);
                localStorage.setItem('users', JSON.stringify(users));
                renderUserList();
                document.getElementById('userForm').reset();
                editingUser = null;
                editUserBtn.disabled = true;
                deleteUserBtn.disabled = true;
                addUserBtn.textContent = 'Add User';
            }
        }
    });
    
    // Render user list
    function renderUserList() {
        userTable.innerHTML = '';
        users.forEach(user => {
            const row = userTable.insertRow();
            row.innerHTML = `
                <td>${user.username}</td>
                <td>${Object.entries(user.permissions)
                    .filter(([_, value]) => value)
                    .map(([key]) => key)
                    .join(', ')}</td>
                <td>
                    <button class="edit-user" data-username="${user.username}">Edit</button>
                </td>
            `;
        });
        
        // Add event listeners to edit buttons
        document.querySelectorAll('.edit-user').forEach(button => {
            button.addEventListener('click', (e) => {
                const username = e.target.dataset.username;
                const user = users.find(u => u.username === username);
                
                if (user) {
                    editingUser = user;
                    document.getElementById('username').value = user.username;
                    document.getElementById('password').value = '';
                    
                    // Set permissions
                    document.getElementById('permCamera').checked = user.permissions.camera;
                    document.getElementById('permAccess').checked = user.permissions.access;
                    document.getElementById('permReports').checked = user.permissions.reports;
                    document.getElementById('permSettings').checked = user.permissions.settings;
                    
                    editUserBtn.disabled = false;
                    deleteUserBtn.disabled = false;
                    addUserBtn.textContent = 'Update';
                }
            });
        });
    }
    
    function getSelectedPermissions() {
        return {
            camera: document.getElementById('permCamera').checked,
            access: document.getElementById('permAccess').checked,
            reports: document.getElementById('permReports').checked,
            settings: document.getElementById('permSettings').checked
        };
    }
});

// Additional helper functions for new system
function getRoleText(role) {
    const roleMap = {
        admin: 'مدير النظام',
        operator: 'مشغل',
        viewer: 'مشاهد',
        security: 'أمان'
    };
    return roleMap[role] || role;
}

function showNotification(message, type = 'info') {
    const colors = {
        success: '#48bb78',
        error: '#f56565',
        info: '#4299e1',
        warning: '#ed8936'
    };

    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${colors[type]};
        color: white;
        padding: 15px 25px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 1001;
        font-weight: 500;
        max-width: 300px;
        animation: slideIn 0.3s ease;
    `;

    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

console.log('Advanced User Management System Loaded Successfully');