// TODO: Add database connection and CRUD operations here
document.addEventListener('DOMContentLoaded', () => {
    const currentUser = JSON.parse(localStorage.getItem('currentUser'));
    const currentUsernameEl = document.getElementById('currentUsername');
    const changePasswordBtn = document.getElementById('changePasswordBtn');
    const addUserBtn = document.getElementById('addUserBtn');
    const editUserBtn = document.getElementById('editUserBtn');
    const deleteUserBtn = document.getElementById('deleteUserBtn');
    const userTable = document.getElementById('userTable').getElementsByTagName('tbody')[0];
    
    let users = JSON.parse(localStorage.getItem('users')) || [];
    let editingUser = null;
    
    // Display current username
    if (currentUser) {
        currentUsernameEl.textContent = currentUser.username;
    }
    
    // Load user list
    renderUserList();
    
    // Change password
    changePasswordBtn.addEventListener('click', () => {
        const currentPassword = document.getElementById('currentPassword').value;
        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        
        if (!currentUser) {
            alert('No user logged in');
            return;
        }
        
        if (currentUser.password !== currentPassword) {
            alert('Current password is incorrect');
            return;
        }
        
        if (newPassword !== confirmPassword) {
            alert('New passwords do not match');
            return;
        }
        
        if (updateUser(currentUser.username, { password: newPassword })) {
            alert('Password changed successfully');
            document.getElementById('currentPassword').value = '';
            document.getElementById('newPassword').value = '';
            document.getElementById('confirmPassword').value = '';
        } else {
            alert('Failed to update password');
        }
    });
    
    // Add new user
    addUserBtn.addEventListener('click', () => {
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        const permissions = getSelectedPermissions();
        
        if (!username || !password) {
            alert('Username and password are required');
            return;
        }
        
        const newUser = {
            username,
            password,
            permissions
        };
        
        if (addUser(newUser)) {
            renderUserList();
            document.getElementById('userForm').reset();
        } else {
            alert('Username already exists');
        }
    });
    
    // Edit user
    editUserBtn.addEventListener('click', () => {
        if (!editingUser) return;
        
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        const permissions = getSelectedPermissions();
        
        if (!username) {
            alert('Username is required');
            return;
        }
        
        const updatedUser = {
            username,
            password: password || editingUser.password,
            permissions
        };
        
        if (updateUser(editingUser.username, updatedUser)) {
            renderUserList();
            document.getElementById('userForm').reset();
            editingUser = null;
            editUserBtn.disabled = true;
            deleteUserBtn.disabled = true;
            addUserBtn.textContent = 'Add User';
        } else {
            alert('Failed to update user');
        }
    });
    
    // Delete user
    deleteUserBtn.addEventListener('click', () => {
        if (!editingUser) return;
        
        if (confirm(`Are you sure you want to delete user ${editingUser.username}?`)) {
            const index = users.findIndex(u => u.username === editingUser.username);
            if (index !== -1) {
                users.splice(index, 1);
                localStorage.setItem('users', JSON.stringify(users));
                renderUserList();
                document.getElementById('userForm').reset();
                editingUser = null;
                editUserBtn.disabled = true;
                deleteUserBtn.disabled = true;
                addUserBtn.textContent = 'Add User';
            }
        }
    });
    
    // Render user list
    function renderUserList() {
        userTable.innerHTML = '';
        users.forEach(user => {
            const row = userTable.insertRow();
            row.innerHTML = `
                <td>${user.username}</td>
                <td>${Object.entries(user.permissions)
                    .filter(([_, value]) => value)
                    .map(([key]) => key)
                    .join(', ')}</td>
                <td>
                    <button class="edit-user" data-username="${user.username}">Edit</button>
                </td>
            `;
        });
        
        // Add event listeners to edit buttons
        document.querySelectorAll('.edit-user').forEach(button => {
            button.addEventListener('click', (e) => {
                const username = e.target.dataset.username;
                const user = users.find(u => u.username === username);
                
                if (user) {
                    editingUser = user;
                    document.getElementById('username').value = user.username;
                    document.getElementById('password').value = '';
                    
                    // Set permissions
                    document.getElementById('permCamera').checked = user.permissions.camera;
                    document.getElementById('permAccess').checked = user.permissions.access;
                    document.getElementById('permReports').checked = user.permissions.reports;
                    document.getElementById('permSettings').checked = user.permissions.settings;
                    
                    editUserBtn.disabled = false;
                    deleteUserBtn.disabled = false;
                    addUserBtn.textContent = 'Update';
                }
            });
        });
    }
    
    function getSelectedPermissions() {
        return {
            camera: document.getElementById('permCamera').checked,
            access: document.getElementById('permAccess').checked,
            reports: document.getElementById('permReports').checked,
            settings: document.getElementById('permSettings').checked
        };
    }
});