<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - Access Control</title>
    <link rel="stylesheet" href="style.css">
    <style>
        #loading-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #2c2c2c;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .loader {
            border: 16px solid #3a3a3a;
            border-top: 16px solid #1a365d;
            border-radius: 50%;
            width: 120px;
            height: 120px;
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .access-container {
            background: #3a3a3a;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .door-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .door-card {
            background: #4a4a4a;
            border-radius: 8px;
            padding: 20px;
            border: 2px solid #555555;
            transition: all 0.3s ease;
        }

        .door-card:hover {
            border-color: #1a365d;
            transform: translateY(-2px);
        }

        .door-status {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }

        .status-open { background-color: #48bb78; }
        .status-closed { background-color: #f56565; }
        .status-locked { background-color: #ed8936; }

        .door-controls {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .door-btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
        }

        .btn-open {
            background-color: #1a365d;
            color: white;
        }

        .btn-open:hover {
            background-color: #2c5282;
        }

        .btn-open:disabled {
            background-color: #666;
            cursor: not-allowed;
        }

        .language-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #1a365d;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            z-index: 100;
        }

        .alert-panel {
            background: #742a2a;
            border: 1px solid #e53e3e;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            display: none;
        }

        .alert-panel.show {
            display: block;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: #3a3a3a;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #1a365d;
        }

        .access-log {
            background: #3a3a3a;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }

        .log-entry {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #555555;
            color: white;
        }

        .log-entry:last-child {
            border-bottom: none;
        }

        .log-time {
            color: #a0a0a0;
            font-size: 12px;
        }

        /* Access Control Buttons */
        .access-buttons-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .access-button-card {
            background: linear-gradient(135deg, #3a3a3a 0%, #4a4a4a 100%);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid #555555;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            position: relative;
            overflow: hidden;
        }

        .access-button-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(26, 54, 93, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .access-button-card:hover::before {
            left: 100%;
        }

        .access-button-card:hover {
            transform: translateY(-5px);
            border-color: #1a365d;
            box-shadow: 0 10px 25px rgba(26, 54, 93, 0.4);
        }

        .access-button-icon {
            font-size: 48px;
            margin-bottom: 15px;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
        }

        .access-button-title {
            color: white;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .access-button-subtitle {
            color: #1a365d;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .access-button-description {
            color: #a0a0a0;
            font-size: 14px;
            line-height: 1.5;
        }

        /* Quick Stats */
        .quick-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .stat-item {
            background: #3a3a3a;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border: 2px solid #555555;
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #1a365d;
            margin-bottom: 8px;
        }

        .stat-label {
            color: white;
            font-size: 14px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .access-buttons-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .access-button-card {
                padding: 25px;
            }

            .access-button-icon {
                font-size: 40px;
            }

            .access-button-title {
                font-size: 18px;
            }
        }

        /* Right Sidebar */
        .right-sidebar {
            position: fixed;
            top: 0;
            right: -350px;
            width: 350px;
            height: 100vh;
            background: linear-gradient(180deg, #1a365d 0%, #2c5282 100%);
            box-shadow: -5px 0 20px rgba(0,0,0,0.3);
            transition: right 0.3s ease;
            z-index: 1000;
            overflow-y: auto;
        }

        .right-sidebar:hover,
        .right-sidebar.show {
            right: 0;
        }

        .sidebar-content {
            padding: 20px;
        }

        .sidebar-section {
            margin-bottom: 30px;
        }

        .section-title {
            color: white;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid rgba(255,255,255,0.2);
        }

        .sidebar-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            margin-bottom: 8px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .sidebar-item:hover {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.3);
            transform: translateX(-5px);
        }

        .item-icon {
            font-size: 20px;
            margin-left: 12px;
            width: 30px;
            text-align: center;
        }

        .item-text {
            color: white;
            font-size: 14px;
            font-weight: 500;
        }

        .user-info {
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            margin-top: 20px;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .user-details {
            flex: 1;
        }

        .user-name {
            color: white;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .user-role {
            color: rgba(255,255,255,0.8);
            font-size: 12px;
        }

        /* Sidebar Trigger Area */
        .sidebar-trigger {
            position: fixed;
            top: 0;
            right: 0;
            width: 20px;
            height: 100vh;
            z-index: 999;
            background: transparent;
        }
    </style>
</head>
<body>
    <div id="loading-container">
        <div class="loader"></div>
    </div>

    <button class="language-toggle" onclick="toggleLanguage()" id="langToggle">English</button>

    <script>
        window.addEventListener('load', function() {
            document.getElementById('loading-container').style.display = 'none';
        });
    </script>

    <!-- Banking Header -->
    <header class="bank-header">
        <div class="bank-logo">EG BANK</div>
        <nav class="bank-nav">
            <ul>
                <li><a href="index.html" style="color: white;" data-en="Dashboard" data-ar="لوحة التحكم">لوحة التحكم</a></li>
                <li><a href="cameras.html" style="color: white;" data-en="Security" data-ar="الأمان">الأمان</a></li>
                <li><a href="access.html" class="active" style="color: white;" data-en="Access Control" data-ar="التحكم في الوصول">التحكم في الوصول</a></li>
                <li><a href="reports.html" style="color: white;" data-en="Reports" data-ar="التقارير">التقارير</a></li>
                <li><a href="settings.html" style="color: white;" data-en="Settings" data-ar="الإعدادات">الإعدادات</a></li>
                <li><a href="gps.html" style="color: white;" data-en="GPS Tracking" data-ar="تتبع GPS">تتبع GPS</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main Content Area -->
    <div class="bank-container">
        <!-- Sidebar -->
        <aside class="bank-sidebar">
            <h3 style="color: white;">عبد الفتاح محمود</h3>
            <ul>
                <li><a href="index.html" style="color: white;" data-en="Camera Surveillance" data-ar="مراقبة الكاميرات">مراقبة الكاميرات</a></li>
                <li><a href="cameras.html" style="color: white;" data-en="IP Camera Management" data-ar="إدارة كاميرات IP">إدارة كاميرات IP</a></li>
                <li class="active" style="color: white;" data-en="Access Control" data-ar="التحكم في الوصول">التحكم في الوصول</li>
                <li><a href="reports.html" style="color: white;" data-en="Reports" data-ar="التقارير">التقارير</a></li>
                <li><a href="user_management.html" style="color: white;" data-en="User Management" data-ar="إدارة المستخدمين">إدارة المستخدمين</a></li>
            </ul>
        </aside>

        <!-- Access Control Section -->
        <main class="bank-main">
            <h1 style="color: white; text-align: center; margin-bottom: 40px;">التحكم في الوصول - Access Control</h1>

            <!-- Access Control Buttons Grid -->
            <div class="access-buttons-grid">
                <div class="access-button-card" onclick="openPage('door_control.html')">
                    <div class="access-button-icon">🚪</div>
                    <div class="access-button-title">التحكم في الأبواب</div>
                    <div class="access-button-subtitle">Door Control System</div>
                    <div class="access-button-description">فتح وإغلاق الأبواب عن بُعد ومراقبة حالتها</div>
                </div>

                <div class="access-button-card" onclick="openPage('user_permissions.html')">
                    <div class="access-button-icon">👤</div>
                    <div class="access-button-title">صلاحيات المستخدمين</div>
                    <div class="access-button-subtitle">User Permissions</div>
                    <div class="access-button-description">إدارة صلاحيات الوصول للمستخدمين والموظفين</div>
                </div>

                <div class="access-button-card" onclick="openPage('access_logs.html')">
                    <div class="access-button-icon">📋</div>
                    <div class="access-button-title">سجلات الوصول</div>
                    <div class="access-button-subtitle">Access Logs</div>
                    <div class="access-button-description">عرض وتحليل سجلات دخول وخروج المستخدمين</div>
                </div>

                <div class="access-button-card" onclick="openPage('security_alerts.html')">
                    <div class="access-button-icon">🚨</div>
                    <div class="access-button-title">التنبيهات الأمنية</div>
                    <div class="access-button-subtitle">Security Alerts</div>
                    <div class="access-button-description">مراقبة التنبيهات الأمنية والأحداث المشبوهة</div>
                </div>

                <div class="access-button-card" onclick="openPage('system_settings.html')">
                    <div class="access-button-icon">⚙️</div>
                    <div class="access-button-title">إعدادات النظام</div>
                    <div class="access-button-subtitle">System Settings</div>
                    <div class="access-button-description">تكوين إعدادات نظام التحكم في الوصول</div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="quick-stats">
                <div class="stat-item">
                    <div class="stat-number">8</div>
                    <div class="stat-label">إجمالي الأبواب</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">156</div>
                    <div class="stat-label">المستخدمين النشطين</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">24</div>
                    <div class="stat-label">العمليات اليوم</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">2</div>
                    <div class="stat-label">التنبيهات الجديدة</div>
                </div>
            </div>
        </main>
    </div>

    <!-- Banking Footer -->
    <footer class="bank-footer">
        <p data-en="© 2024 EG BANK - Secure Access Control" data-ar="© 2024 البنك المصري - التحكم الآمن في الوصول">© 2024 البنك المصري - التحكم الآمن في الوصول</p>
    </footer>

    <!-- Advanced Right Sidebar Toolbar -->
    <div id="rightSidebar" class="right-sidebar">
        <div class="sidebar-content">
            <!-- Control Panel Section -->
            <div class="sidebar-section">
                <div class="section-title">Control Panel</div>
                <div class="sidebar-item" onclick="openControlPanel()">
                    <div class="item-icon">🏠</div>
                    <div class="item-text">لوحة التحكم</div>
                </div>
                <div class="sidebar-item" onclick="openIPCameraConfig()">
                    <div class="item-icon">📹</div>
                    <div class="item-text">IP Camera Configuration</div>
                </div>
                <div class="sidebar-item" onclick="openAccessControl()">
                    <div class="item-icon">🚪</div>
                    <div class="item-text">Access Control</div>
                </div>
                <div class="sidebar-item" onclick="openReports()">
                    <div class="item-icon">📊</div>
                    <div class="item-text">Reports</div>
                </div>
                <div class="sidebar-item" onclick="openSettings()">
                    <div class="item-icon">⚙️</div>
                    <div class="item-text">Settings</div>
                </div>
                <div class="sidebar-item" onclick="openGPS()">
                    <div class="item-icon">📍</div>
                    <div class="item-text">Track Mobile ATMs</div>
                </div>
            </div>

            <!-- User Section -->
            <div class="sidebar-section">
                <div class="section-title">Abdel Fattah Mahmoud</div>
                <div class="sidebar-item" onclick="openCameraMonitoring()">
                    <div class="item-icon">📺</div>
                    <div class="item-text">Camera Monitoring</div>
                </div>
                <div class="sidebar-item" onclick="openIPCameraManagement()">
                    <div class="item-icon">📷</div>
                    <div class="item-text">IP Camera Management</div>
                </div>
                <div class="sidebar-item" onclick="openAccessControlUser()">
                    <div class="item-icon">🔐</div>
                    <div class="item-text">Access Control</div>
                </div>
                <div class="sidebar-item" onclick="openReportsUser()">
                    <div class="item-icon">📈</div>
                    <div class="item-text">Reports</div>
                </div>
                <div class="sidebar-item" onclick="openUserManagement()">
                    <div class="item-icon">👥</div>
                    <div class="item-text">User Management</div>
                </div>
            </div>

            <!-- User Info -->
            <div class="user-info">
                <div class="user-avatar">👤</div>
                <div class="user-details">
                    <div class="user-name">عبد الفتاح محمود</div>
                    <div class="user-role">مدير النظام</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar Trigger Area -->
    <div class="sidebar-trigger" onmouseenter="showSidebar()" onmouseleave="hideSidebar()"></div>

    <!-- Floating Toolbar -->
    <div class="floating-toolbar" style="display: none;">
        <div class="toolbar-header">
            لوحة التحكم الرئيسية
        </div>

        <div class="toolbar-section">
            <div class="toolbar-section-title">Control Panel</div>
            <a href="index.html" class="toolbar-item">
                <div class="toolbar-item-icon">🏠</div>
                <div class="toolbar-item-text">لوحة التحكم</div>
            </a>
            <a href="cameras.html" class="toolbar-item">
                <div class="toolbar-item-icon">🔒</div>
                <div class="toolbar-item-text">الأمان</div>
            </a>
            <a href="access.html" class="toolbar-item active">
                <div class="toolbar-item-icon">🚪</div>
                <div class="toolbar-item-text">التحكم في الوصول</div>
            </a>
            <a href="reports.html" class="toolbar-item">
                <div class="toolbar-item-icon">📊</div>
                <div class="toolbar-item-text">التقارير</div>
            </a>
            <a href="settings.html" class="toolbar-item">
                <div class="toolbar-item-icon">⚙️</div>
                <div class="toolbar-item-text">الإعدادات</div>
            </a>
            <a href="gps.html" class="toolbar-item">
                <div class="toolbar-item-icon">📍</div>
                <div class="toolbar-item-text">تتبع GPS</div>
            </a>
        </div>

        <div class="toolbar-section">
            <div class="toolbar-section-title">Abdel Fattah Mahmoud</div>
            <a href="index.html" class="toolbar-item">
                <div class="toolbar-item-icon">📹</div>
                <div class="toolbar-item-text">مراقبة الكاميرات</div>
            </a>
            <a href="cameras.html" class="toolbar-item">
                <div class="toolbar-item-icon">📷</div>
                <div class="toolbar-item-text">إدارة كاميرات IP</div>
            </a>
            <a href="access.html" class="toolbar-item active">
                <div class="toolbar-item-icon">🔐</div>
                <div class="toolbar-item-text">التحكم في الوصول</div>
            </a>
            <a href="reports.html" class="toolbar-item">
                <div class="toolbar-item-icon">📈</div>
                <div class="toolbar-item-text">التقارير</div>
            </a>
            <a href="user_management.html" class="toolbar-item">
                <div class="toolbar-item-icon">👥</div>
                <div class="toolbar-item-text">إدارة المستخدمين</div>
            </a>
        </div>

        <div class="toolbar-user">
            <div class="toolbar-user-name">عبد الفتاح محمود</div>
            <div class="toolbar-user-role">مدير النظام</div>
        </div>
    </div>

    <script src="access.js"></script>

    <script>
        // Right Sidebar Control
        let sidebarTimeout;

        function showSidebar() {
            clearTimeout(sidebarTimeout);
            document.getElementById('rightSidebar').classList.add('show');
        }

        function hideSidebar() {
            sidebarTimeout = setTimeout(() => {
                document.getElementById('rightSidebar').classList.remove('show');
            }, 300);
        }

        // Add mouse events to sidebar itself
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = document.getElementById('rightSidebar');
            sidebar.addEventListener('mouseenter', showSidebar);
            sidebar.addEventListener('mouseleave', hideSidebar);
        });

        // Navigation Functions
        function openControlPanel() {
            window.location.href = 'index.html';
        }

        function openIPCameraConfig() {
            window.location.href = 'ip_camera_config.html';
        }

        function openAccessControl() {
            window.location.href = 'access.html';
        }

        function openReports() {
            window.location.href = 'reports.html';
        }

        function openSettings() {
            window.location.href = 'settings.html';
        }

        function openGPS() {
            window.location.href = 'gps.html';
        }

        function openCameraMonitoring() {
            window.location.href = 'cameras.html';
        }

        function openIPCameraManagement() {
            window.location.href = 'ip_management.html';
        }

        function openAccessControlUser() {
            window.location.href = 'access_control_user.html';
        }

        function openReportsUser() {
            window.location.href = 'reports_user.html';
        }

        function openUserManagement() {
            window.location.href = 'user_management.html';
        }

        console.log('Right Sidebar System Loaded Successfully');
    </script>
</body>
</html>