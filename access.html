<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - Access Control</title>
    <link rel="stylesheet" href="style.css">
    <style>
        #loading-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #2c2c2c;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .loader {
            border: 16px solid #3a3a3a;
            border-top: 16px solid #1a365d;
            border-radius: 50%;
            width: 120px;
            height: 120px;
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .access-container {
            background: #3a3a3a;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .door-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .door-card {
            background: #4a4a4a;
            border-radius: 8px;
            padding: 20px;
            border: 2px solid #555555;
            transition: all 0.3s ease;
        }

        .door-card:hover {
            border-color: #1a365d;
            transform: translateY(-2px);
        }

        .door-status {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }

        .status-open { background-color: #48bb78; }
        .status-closed { background-color: #f56565; }
        .status-locked { background-color: #ed8936; }

        .door-controls {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .door-btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
        }

        .btn-open {
            background-color: #1a365d;
            color: white;
        }

        .btn-open:hover {
            background-color: #2c5282;
        }

        .btn-open:disabled {
            background-color: #666;
            cursor: not-allowed;
        }

        .language-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #1a365d;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            z-index: 100;
        }

        .alert-panel {
            background: #742a2a;
            border: 1px solid #e53e3e;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            display: none;
        }

        .alert-panel.show {
            display: block;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: #3a3a3a;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #1a365d;
        }

        .access-log {
            background: #3a3a3a;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }

        .log-entry {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #555555;
            color: white;
        }

        .log-entry:last-child {
            border-bottom: none;
        }

        .log-time {
            color: #a0a0a0;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div id="loading-container">
        <div class="loader"></div>
    </div>

    <button class="language-toggle" onclick="toggleLanguage()" id="langToggle">English</button>

    <script>
        window.addEventListener('load', function() {
            document.getElementById('loading-container').style.display = 'none';
        });
    </script>

    <!-- Banking Header -->
    <header class="bank-header">
        <div class="bank-logo">EG BANK</div>
        <nav class="bank-nav">
            <ul>
                <li><a href="index.html" style="color: white;" data-en="Dashboard" data-ar="لوحة التحكم">لوحة التحكم</a></li>
                <li><a href="cameras.html" style="color: white;" data-en="Security" data-ar="الأمان">الأمان</a></li>
                <li><a href="access.html" class="active" style="color: white;" data-en="Access Control" data-ar="التحكم في الوصول">التحكم في الوصول</a></li>
                <li><a href="reports.html" style="color: white;" data-en="Reports" data-ar="التقارير">التقارير</a></li>
                <li><a href="settings.html" style="color: white;" data-en="Settings" data-ar="الإعدادات">الإعدادات</a></li>
                <li><a href="gps.html" style="color: white;" data-en="GPS Tracking" data-ar="تتبع GPS">تتبع GPS</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main Content Area -->
    <div class="bank-container">
        <!-- Sidebar -->
        <aside class="bank-sidebar">
            <h3 style="color: white;">عبد الفتاح محمود</h3>
            <ul>
                <li><a href="index.html" style="color: white;" data-en="Camera Surveillance" data-ar="مراقبة الكاميرات">مراقبة الكاميرات</a></li>
                <li><a href="cameras.html" style="color: white;" data-en="IP Camera Management" data-ar="إدارة كاميرات IP">إدارة كاميرات IP</a></li>
                <li class="active" style="color: white;" data-en="Access Control" data-ar="التحكم في الوصول">التحكم في الوصول</li>
                <li><a href="reports.html" style="color: white;" data-en="Reports" data-ar="التقارير">التقارير</a></li>
                <li><a href="user_management.html" style="color: white;" data-en="User Management" data-ar="إدارة المستخدمين">إدارة المستخدمين</a></li>
            </ul>
        </aside>

        <!-- Access Control Section -->
        <main class="bank-main">
            <h1 style="color: white;" data-en="Access Control" data-ar="التحكم في الوصول">التحكم في الوصول</h1>

            <!-- Alert Panel -->
            <div id="alertPanel" class="alert-panel">
                <h3 style="color: #e53e3e;" data-en="Security Alert!" data-ar="تنبيه أمني!">تنبيه أمني!</h3>
                <p id="alertMessage" style="color: white;"></p>
            </div>

            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalDoors">8</div>
                    <div style="color: white;" data-en="Total Doors" data-ar="إجمالي الأبواب">إجمالي الأبواب</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="openDoors">3</div>
                    <div style="color: white;" data-en="Open Doors" data-ar="أبواب مفتوحة">أبواب مفتوحة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="closedDoors">4</div>
                    <div style="color: white;" data-en="Closed Doors" data-ar="أبواب مغلقة">أبواب مغلقة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="lockedDoors">1</div>
                    <div style="color: white;" data-en="Locked Doors" data-ar="أبواب مقفلة">أبواب مقفلة</div>
                </div>
            </div>

            <!-- Door Control Panel -->
            <div class="access-container">
                <h2 style="color: white; margin-bottom: 20px;" data-en="Door Control Panel" data-ar="لوحة التحكم في الأبواب">لوحة التحكم في الأبواب</h2>

                <div class="door-grid" id="doorGrid">
                    <!-- Doors will be populated by JavaScript -->
                </div>
            </div>

            <!-- Access Log -->
            <div class="access-log">
                <h3 style="color: white; margin-bottom: 15px;" data-en="Recent Access Log" data-ar="سجل الوصول الحديث">سجل الوصول الحديث</h3>
                <div id="accessLog">
                    <!-- Log entries will be populated by JavaScript -->
                </div>
            </div>
        </main>
    </div>

    <!-- Banking Footer -->
    <footer class="bank-footer">
        <p data-en="© 2024 EG BANK - Secure Access Control" data-ar="© 2024 البنك المصري - التحكم الآمن في الوصول">© 2024 البنك المصري - التحكم الآمن في الوصول</p>
    </footer>

    <!-- Floating Toolbar -->
    <div class="floating-toolbar">
        <div class="toolbar-header">
            لوحة التحكم الرئيسية
        </div>

        <div class="toolbar-section">
            <div class="toolbar-section-title">Control Panel</div>
            <a href="index.html" class="toolbar-item">
                <div class="toolbar-item-icon">🏠</div>
                <div class="toolbar-item-text">لوحة التحكم</div>
            </a>
            <a href="cameras.html" class="toolbar-item">
                <div class="toolbar-item-icon">🔒</div>
                <div class="toolbar-item-text">الأمان</div>
            </a>
            <a href="access.html" class="toolbar-item active">
                <div class="toolbar-item-icon">🚪</div>
                <div class="toolbar-item-text">التحكم في الوصول</div>
            </a>
            <a href="reports.html" class="toolbar-item">
                <div class="toolbar-item-icon">📊</div>
                <div class="toolbar-item-text">التقارير</div>
            </a>
            <a href="settings.html" class="toolbar-item">
                <div class="toolbar-item-icon">⚙️</div>
                <div class="toolbar-item-text">الإعدادات</div>
            </a>
            <a href="gps.html" class="toolbar-item">
                <div class="toolbar-item-icon">📍</div>
                <div class="toolbar-item-text">تتبع GPS</div>
            </a>
        </div>

        <div class="toolbar-section">
            <div class="toolbar-section-title">Abdel Fattah Mahmoud</div>
            <a href="index.html" class="toolbar-item">
                <div class="toolbar-item-icon">📹</div>
                <div class="toolbar-item-text">مراقبة الكاميرات</div>
            </a>
            <a href="cameras.html" class="toolbar-item">
                <div class="toolbar-item-icon">📷</div>
                <div class="toolbar-item-text">إدارة كاميرات IP</div>
            </a>
            <a href="access.html" class="toolbar-item active">
                <div class="toolbar-item-icon">🔐</div>
                <div class="toolbar-item-text">التحكم في الوصول</div>
            </a>
            <a href="reports.html" class="toolbar-item">
                <div class="toolbar-item-icon">📈</div>
                <div class="toolbar-item-text">التقارير</div>
            </a>
            <a href="user_management.html" class="toolbar-item">
                <div class="toolbar-item-icon">👥</div>
                <div class="toolbar-item-text">إدارة المستخدمين</div>
            </a>
        </div>

        <div class="toolbar-user">
            <div class="toolbar-user-name">عبد الفتاح محمود</div>
            <div class="toolbar-user-role">مدير النظام</div>
        </div>
    </div>

    <script src="access.js"></script>
</body>
</html>