<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - الصفحة الرئيسية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            overflow-x: hidden;
            min-height: 100vh;
        }
        
        /* Animated Background */
        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: 
                radial-gradient(circle at 20% 50%, rgba(26, 54, 93, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(44, 82, 130, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(49, 130, 206, 0.2) 0%, transparent 50%);
            animation: bgShift 20s ease-in-out infinite;
        }
        
        @keyframes bgShift {
            0%, 100% { opacity: 0.7; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.1); }
        }
        
        /* Header */
        .main-header {
            background: rgba(26, 54, 93, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 30px;
        }
        
        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #1a365d, #2c5282);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            box-shadow: 0 8px 25px rgba(26, 54, 93, 0.4);
            animation: logoFloat 3s ease-in-out infinite;
        }
        
        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }
        
        .logo-text {
            font-family: 'Orbitron', monospace;
            font-size: 32px;
            font-weight: 900;
            background: linear-gradient(45deg, #1a365d, #2c5282, #3182ce);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .user-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .user-info {
            text-align: left;
        }
        
        .user-name {
            font-size: 18px;
            font-weight: 600;
            color: white;
        }
        
        .user-role {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
        }
        
        .logout-btn {
            background: linear-gradient(135deg, #e53e3e, #c53030);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .logout-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(229, 62, 62, 0.4);
        }
        
        /* Main Container */
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 30px;
        }
        
        /* Welcome Section */
        .welcome-section {
            text-align: center;
            margin-bottom: 50px;
        }
        
        .welcome-title {
            font-size: 48px;
            font-weight: 900;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #1a365d, #2c5282, #3182ce, #4299e1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: titleGlow 3s ease-in-out infinite;
        }
        
        @keyframes titleGlow {
            0%, 100% { filter: brightness(1); }
            50% { filter: brightness(1.3); }
        }
        
        .welcome-subtitle {
            font-size: 20px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 30px;
        }
        
        .current-time {
            font-family: 'Orbitron', monospace;
            font-size: 24px;
            color: #3182ce;
            font-weight: 700;
            text-shadow: 0 0 10px rgba(49, 130, 206, 0.5);
        }
        
        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 50px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }
        
        .stat-card:hover::before {
            left: 100%;
        }
        
        .stat-card:hover {
            transform: translateY(-10px);
            border-color: #1a365d;
            box-shadow: 0 20px 40px rgba(26, 54, 93, 0.3);
        }
        
        .stat-icon {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }
        
        .stat-value {
            font-size: 36px;
            font-weight: 900;
            color: #3182ce;
            margin-bottom: 10px;
            font-family: 'Orbitron', monospace;
        }
        
        .stat-label {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 600;
        }
        
        /* Quick Actions */
        .quick-actions {
            margin-bottom: 50px;
        }
        
        .section-title {
            font-size: 32px;
            font-weight: 700;
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }
        
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
        }
        
        .action-card {
            background: linear-gradient(135deg, rgba(26, 54, 93, 0.2), rgba(44, 82, 130, 0.2));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            cursor: pointer;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }
        
        .action-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.05), transparent);
            transform: translateX(-100%);
            transition: transform 0.6s;
        }
        
        .action-card:hover::after {
            transform: translateX(100%);
        }
        
        .action-card:hover {
            transform: translateY(-8px) scale(1.02);
            border-color: #2c5282;
            box-shadow: 0 25px 50px rgba(26, 54, 93, 0.4);
        }
        
        .action-icon {
            font-size: 64px;
            margin-bottom: 20px;
            display: block;
            text-align: center;
        }
        
        .action-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 15px;
            text-align: center;
            color: white;
        }
        
        .action-description {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
            line-height: 1.6;
        }
        
        /* System Status */
        .system-status {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .status-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }
        
        .status-title {
            font-size: 24px;
            font-weight: 700;
            color: white;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #48bb78;
            box-shadow: 0 0 15px #48bb78;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .status-text {
            color: #48bb78;
            font-weight: 600;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }
        
        .status-name {
            color: rgba(255, 255, 255, 0.8);
        }
        
        .status-value {
            color: #3182ce;
            font-weight: 600;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .header-content {
                padding: 0 20px;
                flex-direction: column;
                gap: 20px;
            }
            
            .main-container {
                padding: 20px 15px;
            }
            
            .welcome-title {
                font-size: 32px;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .actions-grid {
                grid-template-columns: 1fr;
            }
            
            .status-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Animated Background -->
    <div class="animated-bg"></div>
    
    <!-- Main Header -->
    <header class="main-header">
        <div class="header-content">
            <div class="logo-section">
                <div class="logo-icon">🏦</div>
                <div class="logo-text">EG BANK</div>
            </div>
            
            <div class="user-section">
                <div class="user-info">
                    <div class="user-name" id="currentUserName">المستخدم</div>
                    <div class="user-role" id="currentUserRole">مدير النظام</div>
                </div>
                <button class="logout-btn" onclick="logout()">
                    🚪 تسجيل الخروج
                </button>
            </div>
        </div>
    </header>
    
    <!-- Main Container -->
    <div class="main-container">
        <!-- Welcome Section -->
        <div class="welcome-section">
            <h1 class="welcome-title">مرحباً بك في نظام الأمان المتقدم</h1>
            <p class="welcome-subtitle">إدارة شاملة لأنظمة الأمان والمراقبة للبنك المصري</p>
            <div class="current-time" id="currentTime">
                <!-- Time will be updated by JavaScript -->
            </div>
        </div>
        
        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card">
                <span class="stat-icon">📹</span>
                <div class="stat-value" id="activeCameras">24</div>
                <div class="stat-label">كاميرات نشطة</div>
            </div>
            
            <div class="stat-card">
                <span class="stat-icon">🚪</span>
                <div class="stat-value" id="accessPoints">12</div>
                <div class="stat-label">نقاط الوصول</div>
            </div>
            
            <div class="stat-card">
                <span class="stat-icon">🏧</span>
                <div class="stat-value" id="atmUnits">8</div>
                <div class="stat-label">أجهزة الصراف</div>
            </div>
            
            <div class="stat-card">
                <span class="stat-icon">👥</span>
                <div class="stat-value" id="activeUsers">156</div>
                <div class="stat-label">المستخدمين النشطين</div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="quick-actions">
            <h2 class="section-title">الإجراءات السريعة</h2>
            <div class="actions-grid">
                <div class="action-card" onclick="navigateToSystem('cameras')">
                    <span class="action-icon">📹</span>
                    <h3 class="action-title">نظام المراقبة</h3>
                    <p class="action-description">مراقبة مباشرة لجميع الكاميرات وإدارة التسجيلات</p>
                </div>
                
                <div class="action-card" onclick="navigateToSystem('access')">
                    <span class="action-icon">🔐</span>
                    <h3 class="action-title">التحكم في الوصول</h3>
                    <p class="action-description">إدارة الأبواب والصلاحيات ومراقبة الدخول والخروج</p>
                </div>
                
                <div class="action-card" onclick="navigateToSystem('gps')">
                    <span class="action-icon">🛰️</span>
                    <h3 class="action-title">تتبع الصرافات</h3>
                    <p class="action-description">مراقبة مواقع أجهزة الصراف المتنقلة في الوقت الفعلي</p>
                </div>
                
                <div class="action-card" onclick="navigateToSystem('reports')">
                    <span class="action-icon">📊</span>
                    <h3 class="action-title">التقارير والتحليلات</h3>
                    <p class="action-description">تقارير شاملة وتحليلات متقدمة للأنشطة الأمنية</p>
                </div>
                
                <div class="action-card" onclick="navigateToSystem('settings')">
                    <span class="action-icon">⚙️</span>
                    <h3 class="action-title">الإعدادات</h3>
                    <p class="action-description">إدارة المستخدمين وإعدادات النظام والنسخ الاحتياطي</p>
                </div>
                
                <div class="action-card" onclick="navigateToSystem('recordings')">
                    <span class="action-icon">💾</span>
                    <h3 class="action-title">إدارة التسجيلات</h3>
                    <p class="action-description">عرض وإدارة جميع تسجيلات المراقبة المحفوظة</p>
                </div>
            </div>
        </div>
        
        <!-- System Status -->
        <div class="system-status">
            <div class="status-header">
                <h3 class="status-title">حالة النظام</h3>
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span class="status-text">جميع الأنظمة تعمل بشكل طبيعي</span>
                </div>
            </div>
            
            <div class="status-grid">
                <div class="status-item">
                    <span class="status-name">خادم قاعدة البيانات</span>
                    <span class="status-value">متصل</span>
                </div>
                
                <div class="status-item">
                    <span class="status-name">شبكة الكاميرات</span>
                    <span class="status-value">نشطة</span>
                </div>
                
                <div class="status-item">
                    <span class="status-name">نظام التنبيهات</span>
                    <span class="status-value">يعمل</span>
                </div>
                
                <div class="status-item">
                    <span class="status-name">النسخ الاحتياطي</span>
                    <span class="status-value">محدث</span>
                </div>
                
                <div class="status-item">
                    <span class="status-name">مساحة التخزين</span>
                    <span class="status-value">78%</span>
                </div>
                
                <div class="status-item">
                    <span class="status-name">آخر تحديث</span>
                    <span class="status-value" id="lastUpdate">الآن</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="database_manager.js"></script>
    <script src="access_logger.js"></script>
    <script src="navigation.js"></script>
    
    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            try {
                checkAuthentication();
                loadUserInfo();
                updateTime();
                updateStatistics();
                logPageAccess();

                // Update time every second
                setInterval(updateTime, 1000);

                // Update statistics every 30 seconds
                setInterval(updateStatistics, 30000);

                console.log('Home page initialized successfully');
            } catch (error) {
                console.error('Error initializing home page:', error);
                // Fallback initialization
                basicInitialization();
            }
        });
        
        // Basic initialization fallback
        function basicInitialization() {
            console.log('Running basic initialization...');

            // Set default user info
            document.getElementById('currentUserName').textContent = localStorage.getItem('username') || 'المستخدم';
            document.getElementById('currentUserRole').textContent = 'مدير النظام';

            // Set current time
            const now = new Date();
            document.getElementById('currentTime').textContent = now.toLocaleString('ar-EG');

            // Set default statistics
            document.getElementById('activeCameras').textContent = '24';
            document.getElementById('accessPoints').textContent = '12';
            document.getElementById('atmUnits').textContent = '8';
            document.getElementById('activeUsers').textContent = '156';

            // Update time every second
            setInterval(() => {
                const now = new Date();
                document.getElementById('currentTime').textContent = now.toLocaleString('ar-EG');
            }, 1000);
        }

        // Check if user is authenticated
        function checkAuthentication() {
            try {
                const isLoggedIn = localStorage.getItem('isLoggedIn');
                if (!isLoggedIn || isLoggedIn !== 'true') {
                    console.log('User not authenticated, redirecting to login...');
                    window.location.href = 'login.html';
                    return false;
                }
                return true;
            } catch (error) {
                console.error('Authentication check failed:', error);
                return true; // Allow access if check fails
            }
        }
        
        // Load user information
        function loadUserInfo() {
            try {
                const username = localStorage.getItem('username') || 'المستخدم';
                const users = JSON.parse(localStorage.getItem('systemUsers') || '[]');
                const currentUser = users.find(u => u.username === username);

                const userNameElement = document.getElementById('currentUserName');
                const userRoleElement = document.getElementById('currentUserRole');

                if (userNameElement) {
                    userNameElement.textContent = currentUser?.fullName || username;
                }

                if (userRoleElement) {
                    userRoleElement.textContent = getRoleText(currentUser?.role || 'user');
                }

                console.log('User info loaded:', username);
            } catch (error) {
                console.error('Error loading user info:', error);
                // Set default values
                const userNameElement = document.getElementById('currentUserName');
                const userRoleElement = document.getElementById('currentUserRole');

                if (userNameElement) userNameElement.textContent = 'المستخدم';
                if (userRoleElement) userRoleElement.textContent = 'مدير النظام';
            }
        }
        
        // Get role text in Arabic
        function getRoleText(role) {
            const roleMap = {
                admin: 'مدير النظام',
                operator: 'مشغل',
                viewer: 'مشاهد',
                security: 'أمان'
            };
            return roleMap[role] || 'مستخدم';
        }
        
        // Update current time
        function updateTime() {
            try {
                const now = new Date();
                const timeString = now.toLocaleString('ar-EG', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });

                const currentTimeElement = document.getElementById('currentTime');
                const lastUpdateElement = document.getElementById('lastUpdate');

                if (currentTimeElement) {
                    currentTimeElement.textContent = timeString;
                }

                if (lastUpdateElement) {
                    lastUpdateElement.textContent = now.toLocaleTimeString('ar-EG');
                }
            } catch (error) {
                console.error('Error updating time:', error);
                // Fallback to simple time display
                const now = new Date();
                const currentTimeElement = document.getElementById('currentTime');
                if (currentTimeElement) {
                    currentTimeElement.textContent = now.toLocaleString();
                }
            }
        }
        
        // Update statistics
        function updateStatistics() {
            try {
                // Simulate real-time data updates
                const stats = {
                    activeCameras: Math.floor(Math.random() * 3) + 22, // 22-24
                    accessPoints: 12,
                    atmUnits: Math.floor(Math.random() * 2) + 7, // 7-8
                    activeUsers: Math.floor(Math.random() * 10) + 150 // 150-159
                };

                // Animate counter updates
                animateCounter('activeCameras', stats.activeCameras);
                animateCounter('accessPoints', stats.accessPoints);
                animateCounter('atmUnits', stats.atmUnits);
                animateCounter('activeUsers', stats.activeUsers);

                console.log('Statistics updated:', stats);
            } catch (error) {
                console.error('Error updating statistics:', error);
                // Set default values
                setStaticValue('activeCameras', 24);
                setStaticValue('accessPoints', 12);
                setStaticValue('atmUnits', 8);
                setStaticValue('activeUsers', 156);
            }
        }

        // Set static value helper
        function setStaticValue(elementId, value) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = value;
            }
        }
        
        // Animate counter
        function animateCounter(elementId, targetValue) {
            try {
                const element = document.getElementById(elementId);
                if (!element) {
                    console.warn('Element not found:', elementId);
                    return;
                }

                const currentValue = parseInt(element.textContent) || 0;

                if (currentValue === targetValue) {
                    return; // No change needed
                }

                const increment = targetValue > currentValue ? 1 : -1;
                const duration = 1000; // 1 second
                const steps = Math.abs(targetValue - currentValue);
                const stepDuration = steps > 0 ? duration / steps : 0;

                let current = currentValue;
                const timer = setInterval(() => {
                    current += increment;
                    element.textContent = current;

                    if (current === targetValue) {
                        clearInterval(timer);
                    }
                }, stepDuration);
            } catch (error) {
                console.error('Error animating counter:', error);
                // Fallback to direct value setting
                const element = document.getElementById(elementId);
                if (element) {
                    element.textContent = targetValue;
                }
            }
        }
        
        // Navigate to system
        function navigateToSystem(system) {
            try {
                console.log('Navigating to system:', system);

                // Log navigation action
                if (window.logAction) {
                    window.logAction('navigation', `الانتقال إلى نظام: ${system}`, {
                        targetSystem: system,
                        fromPage: 'home'
                    });
                }

                const systemUrls = {
                    cameras: 'cameras.html',
                    access: 'access.html',
                    gps: 'gps.html',
                    reports: 'reports.html',
                    settings: 'settings.html',
                    recordings: 'recordings_management.html'
                };

                if (systemUrls[system]) {
                    showTransition();
                    setTimeout(() => {
                        window.location.href = systemUrls[system];
                    }, 800);
                } else {
                    console.error('Unknown system:', system);
                    alert('النظام المطلوب غير متاح حالياً');
                }
            } catch (error) {
                console.error('Navigation error:', error);
                // Direct navigation fallback
                const systemUrls = {
                    cameras: 'cameras.html',
                    access: 'access.html',
                    gps: 'gps.html',
                    reports: 'reports.html',
                    settings: 'settings.html',
                    recordings: 'recordings_management.html'
                };

                if (systemUrls[system]) {
                    window.location.href = systemUrls[system];
                }
            }
        }
        
        // Show transition effect
        function showTransition() {
            const transition = document.createElement('div');
            transition.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(45deg, #1a365d, #2c5282);
                z-index: 9999;
                opacity: 0;
                transition: opacity 0.5s ease;
            `;
            
            document.body.appendChild(transition);
            
            setTimeout(() => {
                transition.style.opacity = '0.8';
            }, 10);
        }
        
        // Logout function
        async function logout() {
            try {
                if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                    console.log('User logging out...');

                    // Log logout action
                    if (window.accessLogger) {
                        try {
                            await window.accessLogger.endSession();
                        } catch (error) {
                            console.error('Error ending session:', error);
                        }
                    }

                    // Clear session data
                    localStorage.removeItem('isLoggedIn');
                    localStorage.removeItem('username');
                    localStorage.removeItem('loginTime');

                    // Show logout message
                    alert('تم تسجيل الخروج بنجاح');

                    // Redirect to login
                    window.location.href = 'login.html';
                }
            } catch (error) {
                console.error('Logout error:', error);
                // Force logout even if there's an error
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('username');
                localStorage.removeItem('loginTime');
                window.location.href = 'login.html';
            }
        }
        
        // Log page access
        function logPageAccess() {
            try {
                if (window.logAction) {
                    window.logAction('page_access', 'دخول إلى الصفحة الرئيسية', {
                        page: 'home',
                        timestamp: new Date().toISOString()
                    });
                }
                console.log('Page access logged');
            } catch (error) {
                console.error('Error logging page access:', error);
            }
        }
        
        // Advanced notification system
        function showAdvancedNotification(title, message, type = 'info', duration = 5000) {
            const notification = document.createElement('div');
            notification.className = `advanced-notification ${type}`;

            const icons = {
                success: '✅',
                error: '❌',
                warning: '⚠️',
                info: 'ℹ️',
                security: '🔒'
            };

            notification.innerHTML = `
                <div class="notification-icon">${icons[type] || icons.info}</div>
                <div class="notification-content">
                    <div class="notification-title">${title}</div>
                    <div class="notification-message">${message}</div>
                </div>
                <button class="notification-close" onclick="this.parentElement.remove()">×</button>
            `;

            // Add styles if not already added
            if (!document.getElementById('notification-styles')) {
                const styles = document.createElement('style');
                styles.id = 'notification-styles';
                styles.textContent = `
                    .advanced-notification {
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: rgba(26, 54, 93, 0.95);
                        backdrop-filter: blur(20px);
                        border: 1px solid rgba(255, 255, 255, 0.1);
                        border-radius: 12px;
                        padding: 20px;
                        max-width: 400px;
                        z-index: 10000;
                        display: flex;
                        align-items: flex-start;
                        gap: 15px;
                        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                        animation: slideInRight 0.5s ease;
                    }

                    .advanced-notification.success {
                        border-left: 4px solid #48bb78;
                    }

                    .advanced-notification.error {
                        border-left: 4px solid #f56565;
                    }

                    .advanced-notification.warning {
                        border-left: 4px solid #ed8936;
                    }

                    .advanced-notification.info {
                        border-left: 4px solid #4299e1;
                    }

                    .advanced-notification.security {
                        border-left: 4px solid #9f7aea;
                    }

                    .notification-icon {
                        font-size: 24px;
                        flex-shrink: 0;
                    }

                    .notification-content {
                        flex: 1;
                        color: white;
                    }

                    .notification-title {
                        font-weight: 600;
                        margin-bottom: 5px;
                        font-size: 16px;
                    }

                    .notification-message {
                        font-size: 14px;
                        color: rgba(255, 255, 255, 0.8);
                        line-height: 1.4;
                    }

                    .notification-close {
                        background: none;
                        border: none;
                        color: rgba(255, 255, 255, 0.6);
                        font-size: 20px;
                        cursor: pointer;
                        padding: 0;
                        width: 24px;
                        height: 24px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border-radius: 50%;
                        transition: all 0.3s ease;
                    }

                    .notification-close:hover {
                        background: rgba(255, 255, 255, 0.1);
                        color: white;
                    }

                    @keyframes slideInRight {
                        from {
                            transform: translateX(100%);
                            opacity: 0;
                        }
                        to {
                            transform: translateX(0);
                            opacity: 1;
                        }
                    }
                `;
                document.head.appendChild(styles);
            }

            document.body.appendChild(notification);

            // Auto remove after duration
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.animation = 'slideInRight 0.5s ease reverse';
                    setTimeout(() => {
                        if (notification.parentElement) {
                            notification.remove();
                        }
                    }, 500);
                }
            }, duration);
        }

        // Show welcome notification
        setTimeout(() => {
            const username = localStorage.getItem('username') || 'المستخدم';
            showAdvancedNotification(
                'مرحباً بك!',
                `أهلاً وسهلاً ${username}، تم تسجيل دخولك بنجاح إلى نظام الأمان المتقدم`,
                'success',
                4000
            );
        }, 1000);

        // Simulate real-time security alerts
        function simulateSecurityAlerts() {
            const alerts = [
                {
                    title: 'تنبيه أمني',
                    message: 'تم اكتشاف حركة غير عادية في الكاميرا رقم 3',
                    type: 'security'
                },
                {
                    title: 'تحديث النظام',
                    message: 'تم تحديث قاعدة البيانات بنجاح',
                    type: 'info'
                },
                {
                    title: 'نسخة احتياطية',
                    message: 'تم إنشاء النسخة الاحتياطية اليومية',
                    type: 'success'
                }
            ];

            // Show random alert every 30-60 seconds
            const randomDelay = Math.random() * 30000 + 30000; // 30-60 seconds
            setTimeout(() => {
                const randomAlert = alerts[Math.floor(Math.random() * alerts.length)];
                showAdvancedNotification(randomAlert.title, randomAlert.message, randomAlert.type);
                simulateSecurityAlerts(); // Schedule next alert
            }, randomDelay);
        }

        // Start security alerts simulation
        setTimeout(simulateSecurityAlerts, 10000); // Start after 10 seconds

        console.log('EG BANK Advanced Home Page Loaded Successfully');
    </script>
</body>
</html>
