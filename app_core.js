// EG BANK Security System - Core Application
// نظام الأمان المتكامل للبنك المصري

class EGBankApp {
    constructor() {
        this.isInitialized = false;
        this.currentUser = null;
        this.systemStatus = {
            cameras: 'active',
            access: 'active',
            gps: 'active',
            database: 'connected',
            alerts: 'enabled'
        };
        this.init();
    }

    // Initialize the application
    async init() {
        try {
            console.log('🏦 EG BANK Security System - Initializing...');
            
            // Initialize core systems
            this.initializeDatabase();
            this.initializeAuth();
            this.initializeLogging();
            this.setupGlobalHandlers();
            
            this.isInitialized = true;
            console.log('✅ EG BANK System initialized successfully');
            
            // Dispatch ready event
            document.dispatchEvent(new CustomEvent('egbankReady'));
            
        } catch (error) {
            console.error('❌ Failed to initialize EG BANK System:', error);
            this.handleInitError(error);
        }
    }

    // Initialize database system
    initializeDatabase() {
        // Force re-initialization to update passwords
        this.setupDefaultData();
        localStorage.setItem('egbank_initialized', 'true');
        localStorage.setItem('password_updated', 'SHeh#1981');

        // Load system users
        this.loadSystemUsers();
        console.log('📊 Database system initialized with updated passwords');
    }

    // Setup default system data
    setupDefaultData() {
        // Clear old data to ensure password update
        localStorage.removeItem('systemUsers');

        const defaultUsers = [
            {
                id: 'admin001',
                username: 'admin',
                password: 'SHeh#1981',
                fullName: 'عبد الفتاح محمود',
                email: '<EMAIL>',
                role: 'admin',
                permissions: ['cameras', 'attendance', 'gps', 'reports', 'users'],
                status: 'active',
                createdAt: new Date().toISOString(),
                lastLogin: null
            },
            {
                id: 'user001',
                username: 'operator',
                password: 'SHeh#1981',
                fullName: 'أحمد محمد',
                email: '<EMAIL>',
                role: 'operator',
                permissions: ['cameras', 'attendance', 'gps', 'reports'],
                status: 'active',
                createdAt: new Date().toISOString(),
                lastLogin: null
            }
        ];

        const defaultCameras = [
            {
                id: 'cam001',
                name: 'كاميرا المدخل الرئيسي',
                location: 'المدخل الرئيسي',
                ip: '*************',
                status: 'active',
                recording: true,
                quality: 'HD'
            },
            {
                id: 'cam002',
                name: 'كاميرا الخزينة',
                location: 'منطقة الخزينة',
                ip: '*************',
                status: 'active',
                recording: true,
                quality: '4K'
            },
            {
                id: 'cam003',
                name: 'كاميرا صالة العملاء',
                location: 'صالة العملاء',
                ip: '*************',
                status: 'active',
                recording: true,
                quality: 'HD'
            },
            {
                id: 'cam004',
                name: 'كاميرا الموقف',
                location: 'موقف السيارات',
                ip: '*************',
                status: 'active',
                recording: true,
                quality: 'HD'
            }
        ];

        const defaultAccessPoints = [
            {
                id: 'door001',
                name: 'الباب الرئيسي',
                location: 'المدخل الرئيسي',
                status: 'locked',
                accessLevel: 'public'
            },
            {
                id: 'door002',
                name: 'باب الخزينة',
                location: 'منطقة الخزينة',
                status: 'locked',
                accessLevel: 'restricted'
            },
            {
                id: 'door003',
                name: 'باب المكاتب',
                location: 'منطقة المكاتب',
                status: 'unlocked',
                accessLevel: 'staff'
            }
        ];

        const defaultATMs = [
            {
                id: 'atm001',
                name: 'صراف المدخل',
                location: 'أمام البنك',
                coordinates: { lat: 30.0444, lng: 31.2357 },
                status: 'online',
                cash: 85
            },
            {
                id: 'atm002',
                name: 'صراف التجمع',
                location: 'التجمع الخامس',
                coordinates: { lat: 30.0131, lng: 31.4286 },
                status: 'online',
                cash: 92
            }
        ];

        // Save default data
        localStorage.setItem('systemUsers', JSON.stringify(defaultUsers));
        localStorage.setItem('systemCameras', JSON.stringify(defaultCameras));
        localStorage.setItem('systemAccessPoints', JSON.stringify(defaultAccessPoints));
        localStorage.setItem('systemATMs', JSON.stringify(defaultATMs));
        localStorage.setItem('securityRecordings', JSON.stringify([]));
        localStorage.setItem('accessLogs', JSON.stringify([]));
        localStorage.setItem('securityAlerts', JSON.stringify([]));

        console.log('📝 Default system data created with password: SHeh#1981');
        console.log('👥 Users created:', defaultUsers.map(u => `${u.username}:${u.password}`));
    }

    // Load system users
    loadSystemUsers() {
        try {
            const users = JSON.parse(localStorage.getItem('systemUsers') || '[]');
            this.systemUsers = users;
            console.log(`👥 Loaded ${users.length} system users`);
        } catch (error) {
            console.error('Error loading system users:', error);
            this.systemUsers = [];
        }
    }

    // Initialize authentication system
    initializeAuth() {
        this.currentUser = this.getCurrentUser();
        console.log('🔐 Authentication system initialized');
    }

    // Get current logged in user
    getCurrentUser() {
        const isLoggedIn = localStorage.getItem('isLoggedIn');
        const username = localStorage.getItem('username');
        
        if (isLoggedIn === 'true' && username) {
            const user = this.systemUsers.find(u => u.username === username);
            return user || null;
        }
        
        return null;
    }

    // Initialize logging system
    initializeLogging() {
        this.setupErrorHandling();
        console.log('📋 Logging system initialized');
    }

    // Setup global error handling
    setupErrorHandling() {
        window.addEventListener('error', (event) => {
            this.logError('JavaScript Error', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error?.stack
            });
        });

        window.addEventListener('unhandledrejection', (event) => {
            this.logError('Unhandled Promise Rejection', {
                reason: event.reason
            });
        });
    }

    // Setup global event handlers
    setupGlobalHandlers() {
        // Page visibility change
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.logAction('page_hidden', 'إخفاء الصفحة');
            } else {
                this.logAction('page_visible', 'إظهار الصفحة');
            }
        });

        // Before page unload
        window.addEventListener('beforeunload', () => {
            this.logAction('page_unload', 'مغادرة الصفحة');
        });

        console.log('🎛️ Global handlers setup complete');
    }

    // Handle initialization errors
    handleInitError(error) {
        console.error('Initialization failed:', error);
        
        // Show user-friendly error message
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #e53e3e;
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            z-index: 10000;
            font-family: 'Cairo', sans-serif;
        `;
        errorDiv.innerHTML = `
            <h3>خطأ في تحميل النظام</h3>
            <p>حدث خطأ أثناء تحميل نظام الأمان</p>
            <button onclick="location.reload()" style="background: white; color: #e53e3e; border: none; padding: 10px 20px; border-radius: 5px; margin-top: 10px; cursor: pointer;">
                إعادة تحميل
            </button>
        `;
        document.body.appendChild(errorDiv);
    }

    // Authentication methods
    async login(username, password) {
        try {
            console.log('🔐 Login attempt:', username, 'with password:', password);
            console.log('👥 Available users:', this.systemUsers.map(u => `${u.username}:${u.password}`));

            const user = this.systemUsers.find(u =>
                u.username === username &&
                u.password === password &&
                u.status === 'active'
            );

            if (user) {
                // Update last login
                user.lastLogin = new Date().toISOString();
                this.saveUsers();

                // Set session
                localStorage.setItem('isLoggedIn', 'true');
                localStorage.setItem('username', username);
                localStorage.setItem('loginTime', new Date().toISOString());

                this.currentUser = user;
                this.logAction('login_success', 'تسجيل دخول ناجح', { username });

                return { success: true, user };
            } else {
                this.logSecurityEvent('failed_login', 'medium', 'محاولة تسجيل دخول فاشلة', { username });
                return { success: false, message: 'اسم المستخدم أو كلمة المرور غير صحيحة' };
            }
        } catch (error) {
            console.error('Login error:', error);
            return { success: false, message: 'حدث خطأ أثناء تسجيل الدخول' };
        }
    }

    logout() {
        try {
            this.logAction('logout', 'تسجيل خروج المستخدم');
            
            // Clear session
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('username');
            localStorage.removeItem('loginTime');
            
            this.currentUser = null;
            
            // Redirect to login
            window.location.href = 'login.html';
        } catch (error) {
            console.error('Logout error:', error);
            // Force logout
            localStorage.clear();
            window.location.href = 'login.html';
        }
    }

    // Check if user is authenticated
    isAuthenticated() {
        return this.currentUser !== null && localStorage.getItem('isLoggedIn') === 'true';
    }

    // Check if user has permission
    hasPermission(permission) {
        if (!this.currentUser) return false;
        return this.currentUser.permissions.includes(permission) || this.currentUser.role === 'admin';
    }

    // Data management methods
    saveUsers() {
        localStorage.setItem('systemUsers', JSON.stringify(this.systemUsers));
    }

    // Logging methods
    logAction(action, description, details = {}) {
        try {
            const logEntry = {
                id: 'log_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                userId: this.currentUser?.username || 'anonymous',
                action,
                description,
                details,
                timestamp: new Date().toISOString(),
                page: window.location.pathname,
                userAgent: navigator.userAgent
            };

            const logs = JSON.parse(localStorage.getItem('accessLogs') || '[]');
            logs.push(logEntry);
            
            // Keep only last 1000 logs
            if (logs.length > 1000) {
                logs.splice(0, logs.length - 1000);
            }
            
            localStorage.setItem('accessLogs', JSON.stringify(logs));
            console.log('📝 Action logged:', action);
        } catch (error) {
            console.error('Error logging action:', error);
        }
    }

    logSecurityEvent(eventType, severity, description, details = {}) {
        try {
            const securityLog = {
                id: 'security_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                userId: this.currentUser?.username || 'system',
                eventType,
                severity,
                description,
                details,
                timestamp: new Date().toISOString(),
                resolved: false
            };

            const alerts = JSON.parse(localStorage.getItem('securityAlerts') || '[]');
            alerts.push(securityLog);
            localStorage.setItem('securityAlerts', JSON.stringify(alerts));

            console.log('🚨 Security event logged:', eventType, severity);
        } catch (error) {
            console.error('Error logging security event:', error);
        }
    }

    logError(type, details) {
        this.logSecurityEvent('system_error', 'low', type, details);
    }

    // Utility methods
    showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `egbank-notification ${type}`;
        
        const colors = {
            success: '#48bb78',
            error: '#f56565',
            warning: '#ed8936',
            info: '#4299e1'
        };

        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${colors[type] || colors.info};
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 10001;
            font-family: 'Cairo', sans-serif;
            font-weight: 500;
            max-width: 300px;
            animation: slideInRight 0.3s ease;
        `;

        notification.textContent = message;
        document.body.appendChild(notification);

        // Add animation styles
        if (!document.getElementById('egbank-notification-styles')) {
            const style = document.createElement('style');
            style.id = 'egbank-notification-styles';
            style.textContent = `
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            `;
            document.head.appendChild(style);
        }

        setTimeout(() => {
            notification.style.animation = 'slideInRight 0.3s ease reverse';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }, duration);
    }

    // Navigation helper
    navigateTo(page) {
        this.logAction('navigation', `التنقل إلى: ${page}`);
        window.location.href = page;
    }

    // Get system statistics
    getSystemStats() {
        const cameras = JSON.parse(localStorage.getItem('systemCameras') || '[]');
        const accessPoints = JSON.parse(localStorage.getItem('systemAccessPoints') || '[]');
        const atms = JSON.parse(localStorage.getItem('systemATMs') || '[]');
        const users = JSON.parse(localStorage.getItem('systemUsers') || '[]');

        return {
            activeCameras: cameras.filter(c => c.status === 'active').length,
            totalCameras: cameras.length,
            accessPoints: accessPoints.length,
            atms: atms.filter(a => a.status === 'online').length,
            totalATMs: atms.length,
            activeUsers: users.filter(u => u.status === 'active').length,
            totalUsers: users.length
        };
    }
}

// Create global app instance
window.EGBankApp = new EGBankApp();

// Global helper functions
window.egbank = {
    login: (username, password) => window.EGBankApp.login(username, password),
    logout: () => window.EGBankApp.logout(),
    isAuthenticated: () => window.EGBankApp.isAuthenticated(),
    hasPermission: (permission) => window.EGBankApp.hasPermission(permission),
    logAction: (action, description, details) => window.EGBankApp.logAction(action, description, details),
    showNotification: (message, type, duration) => window.EGBankApp.showNotification(message, type, duration),
    navigateTo: (page) => window.EGBankApp.navigateTo(page),
    getStats: () => window.EGBankApp.getSystemStats(),
    getCurrentUser: () => window.EGBankApp.currentUser
};

console.log('🏦 EG BANK Core Application Loaded');
