<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - User Management</title>
    <link rel="stylesheet" href="style.css">
    <style>
        #loading-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #121212;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .loader {
            border: 16px solid #301934; /* Dark purple */
            border-top: 16px solid #4B0082; /* Darker purple */
            border-radius: 50%;
            width: 120px;
            height: 120px;
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .user-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .user-table th, .user-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #444;
            color: white;
        }
        .user-table th {
            background-color: #2d3748;
        }
        .user-table tbody tr:nth-child(even) {
            background-color: #333;
        }
    </style>
    <script src="auth.js"></script>
</head>
<body>
    <div id="loading-container">
        <div class="loader"></div>
    </div>
    <script>
        window.addEventListener('load', function() {
            document.getElementById('loading-container').style.display = 'none';
        });
    </script>
    <!-- Banking Header -->
    <header class="bank-header">
        <div class="bank-logo">EG BANK</div>
        <nav class="bank-nav">
            <ul>
                <li><a href="index.html" style="color: white;">Dashboard</a></li>
                <li><a href="cameras.html" style="color: white;">Security</a></li>
                <li><a href="access.html" style="color: white;">Access Control</a></li>
                <li><a href="reports.html" style="color: white;">Reports</a></li>
                <li><a href="settings.html" class="active" style="color: white;">Settings</a></li>
                <li><a href="gps.html" style="color: white;">GPS Tracking</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main Content Area -->
    <div class="bank-container">
        <!-- Sidebar -->
        <aside class="bank-sidebar">
            <h3 style="color: white;">Abdel Fattah Mahmoud</h3>
            <ul>
                <li><a href="index.html" style="color: white;">Camera Surveillance</a></li>
                <li><a href="cameras.html" style="color: white;">IP Camera Management</a></li>
                <li><a href="access.html" style="color: white;">Access Control</a></li>
                <li><a href="reports.html" style="color: white;">Reports</a></li>
                <li class="active" style="color: white;">User Management</li>
            </ul>
        </aside>

        <!-- Settings Section -->
        <main class="bank-main">
            <h1 style="color: white;">Team</h1>
            <p style="color: white;">Managing the Team Members</p>

            <table class="user-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Age</th>
                        <th>Phone Number</th>
                        <th>Email</th>
                        <th>Access Level</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>Jon Snow</td>
                        <td>35</td>
                        <td>(665)121-5454</td>
                        <td><EMAIL></td>
                        <td>admin</td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>Cersei Lannister</td>
                        <td>42</td>
                        <td>(421)314-2288</td>
                        <td><EMAIL></td>
                        <td>manager</td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>Jaime Lannister</td>
                        <td>45</td>
                        <td>(422)982-6739</td>
                        <td><EMAIL></td>
                        <td>user</td>
                    </tr>
                </tbody>
            </table>
        </main>
    </div>

    <!-- Banking Footer -->
    <footer class="bank-footer">
    </footer>

    <script src="settings.js"></script>
</body>
</html>