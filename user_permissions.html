<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - User Permissions</title>
    <link rel="stylesheet" href="style.css">
    <style>
        #loading-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #2c2c2c;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .loader {
            border: 16px solid #3a3a3a;
            border-top: 16px solid #1a365d;
            border-radius: 50%;
            width: 120px;
            height: 120px;
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .permissions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .user-permission-card {
            background: #3a3a3a;
            border-radius: 8px;
            padding: 20px;
            border: 2px solid #555555;
        }
        
        .user-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .user-name {
            color: white;
            font-size: 18px;
            font-weight: bold;
        }
        
        .user-role {
            background: #1a365d;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
        }
        
        .permissions-list {
            margin-bottom: 15px;
        }
        
        .permission-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #555555;
        }
        
        .permission-item:last-child {
            border-bottom: none;
        }
        
        .permission-name {
            color: #a0a0a0;
            font-size: 14px;
        }
        
        .permission-toggle {
            width: 40px;
            height: 20px;
            background: #555555;
            border-radius: 10px;
            position: relative;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .permission-toggle.active {
            background: #1a365d;
        }
        
        .permission-toggle::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            background: white;
            border-radius: 50%;
            top: 2px;
            left: 2px;
            transition: transform 0.3s;
        }
        
        .permission-toggle.active::after {
            transform: translateX(20px);
        }
    </style>
</head>
<body>
    <div id="loading-container">
        <div class="loader"></div>
    </div>
    
    <script>
        window.addEventListener('load', function() {
            document.getElementById('loading-container').style.display = 'none';
        });
    </script>
    
    <!-- Banking Header -->
    <header class="bank-header">
        <div class="bank-logo">EG BANK</div>
        <nav class="bank-nav">
            <ul>
                <li><a href="index.html" style="color: white;">لوحة التحكم</a></li>
                <li><a href="cameras.html" style="color: white;">الأمان</a></li>
                <li><a href="access.html" class="active" style="color: white;">التحكم في الوصول</a></li>
                <li><a href="reports.html" style="color: white;">التقارير</a></li>
                <li><a href="settings.html" style="color: white;">الإعدادات</a></li>
                <li><a href="gps.html" style="color: white;">تتبع GPS</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main Content Area -->
    <div class="bank-container">
        <!-- Sidebar -->
        <aside class="bank-sidebar">
            <h3 style="color: white;">عبد الفتاح محمود</h3>
            <ul>
                <li><a href="index.html" style="color: white;">مراقبة الكاميرات</a></li>
                <li><a href="cameras.html" style="color: white;">إدارة كاميرات IP</a></li>
                <li class="active" style="color: white;">صلاحيات المستخدمين</li>
                <li><a href="reports.html" style="color: white;">التقارير</a></li>
                <li><a href="user_management.html" style="color: white;">إدارة المستخدمين</a></li>
            </ul>
        </aside>

        <!-- User Permissions Section -->
        <main class="bank-main">
            <div style="display: flex; align-items: center; margin-bottom: 30px;">
                <button onclick="window.history.back()" style="background: #1a365d; color: white; border: none; padding: 10px 15px; border-radius: 4px; margin-left: 15px; cursor: pointer;">← رجوع</button>
                <h1 style="color: white; margin: 0;">إدارة صلاحيات المستخدمين</h1>
            </div>
            
            <!-- Search and Filter -->
            <div class="search-container">
                <div class="search-bar">
                    <input type="text" id="searchInput" class="search-input" placeholder="البحث عن مستخدم...">
                    <button class="search-btn" onclick="searchUsers()">بحث</button>
                    <button class="search-btn" onclick="addNewUser()">إضافة مستخدم جديد</button>
                </div>
            </div>
            
            <!-- Users Permissions Grid -->
            <div class="permissions-grid" id="permissionsGrid">
                <!-- User permission cards will be populated by JavaScript -->
            </div>
        </main>
    </div>

    <!-- Banking Footer -->
    <footer class="bank-footer">
        <p>© 2024 البنك المصري - إدارة صلاحيات المستخدمين</p>
    </footer>

    <!-- Floating Toolbar -->
    <div class="floating-toolbar">
        <div class="toolbar-header">
            لوحة التحكم الرئيسية
        </div>
        
        <div class="toolbar-section">
            <div class="toolbar-section-title">Control Panel</div>
            <a href="index.html" class="toolbar-item">
                <div class="toolbar-item-icon">🏠</div>
                <div class="toolbar-item-text">لوحة التحكم</div>
            </a>
            <a href="cameras.html" class="toolbar-item">
                <div class="toolbar-item-icon">🔒</div>
                <div class="toolbar-item-text">الأمان</div>
            </a>
            <a href="access.html" class="toolbar-item active">
                <div class="toolbar-item-icon">🚪</div>
                <div class="toolbar-item-text">التحكم في الوصول</div>
            </a>
            <a href="reports.html" class="toolbar-item">
                <div class="toolbar-item-icon">📊</div>
                <div class="toolbar-item-text">التقارير</div>
            </a>
            <a href="settings.html" class="toolbar-item">
                <div class="toolbar-item-icon">⚙️</div>
                <div class="toolbar-item-text">الإعدادات</div>
            </a>
            <a href="gps.html" class="toolbar-item">
                <div class="toolbar-item-icon">📍</div>
                <div class="toolbar-item-text">تتبع GPS</div>
            </a>
        </div>
        
        <div class="toolbar-section">
            <div class="toolbar-section-title">Abdel Fattah Mahmoud</div>
            <a href="index.html" class="toolbar-item">
                <div class="toolbar-item-icon">📹</div>
                <div class="toolbar-item-text">مراقبة الكاميرات</div>
            </a>
            <a href="cameras.html" class="toolbar-item">
                <div class="toolbar-item-icon">📷</div>
                <div class="toolbar-item-text">إدارة كاميرات IP</div>
            </a>
            <a href="access.html" class="toolbar-item active">
                <div class="toolbar-item-icon">🔐</div>
                <div class="toolbar-item-text">التحكم في الوصول</div>
            </a>
            <a href="reports.html" class="toolbar-item">
                <div class="toolbar-item-icon">📈</div>
                <div class="toolbar-item-text">التقارير</div>
            </a>
            <a href="user_management.html" class="toolbar-item">
                <div class="toolbar-item-icon">👥</div>
                <div class="toolbar-item-text">إدارة المستخدمين</div>
            </a>
        </div>
        
        <div class="toolbar-user">
            <div class="toolbar-user-name">عبد الفتاح محمود</div>
            <div class="toolbar-user-role">مدير النظام</div>
        </div>
    </div>

    <script src="user_permissions.js"></script>
</body>
</html>
