<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - IP Camera Management</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            background-color: #2c2c2c;
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            background: #3a3a3a;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            color: white;
            font-size: 32px;
            margin: 0;
        }
        
        .management-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
        }
        
        .camera-management-card {
            background: #4a4a4a;
            border-radius: 12px;
            padding: 25px;
            border: 2px solid #555555;
            transition: all 0.3s ease;
        }
        
        .camera-management-card:hover {
            border-color: #1a365d;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .camera-title {
            color: white;
            font-size: 18px;
            font-weight: bold;
        }
        
        .camera-id {
            background: #1a365d;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
        }
        
        .camera-details {
            margin-bottom: 20px;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #555555;
        }
        
        .detail-label {
            color: #a0a0a0;
            font-size: 14px;
        }
        
        .detail-value {
            color: white;
            font-size: 14px;
            font-weight: 500;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .status-online {
            background: #48bb78;
        }
        
        .status-offline {
            background: #f56565;
        }
        
        .status-maintenance {
            background: #ed8936;
        }
        
        .camera-actions {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }
        
        .action-btn {
            padding: 10px 15px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-configure {
            background: #1a365d;
            color: white;
        }
        
        .btn-configure:hover {
            background: #2c5282;
        }
        
        .btn-test {
            background: #38a169;
            color: white;
        }
        
        .btn-test:hover {
            background: #2f855a;
        }
        
        .btn-restart {
            background: #ed8936;
            color: white;
        }
        
        .btn-restart:hover {
            background: #dd6b20;
        }
        
        .btn-remove {
            background: #e53e3e;
            color: white;
        }
        
        .btn-remove:hover {
            background: #c53030;
        }
        
        .add-camera-card {
            background: linear-gradient(135deg, #1a365d 0%, #2c5282 100%);
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px dashed rgba(255,255,255,0.3);
        }
        
        .add-camera-card:hover {
            border-color: rgba(255,255,255,0.6);
            transform: translateY(-5px);
        }
        
        .add-icon {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }
        
        .add-text {
            color: white;
            font-size: 18px;
            font-weight: bold;
        }
        
        .back-btn {
            background: #1a365d;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: #2c5282;
        }
        
        @media (max-width: 768px) {
            .management-grid {
                grid-template-columns: 1fr;
            }
            
            .camera-actions {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <button class="back-btn" onclick="window.history.back()">← رجوع</button>
        
        <div class="header">
            <h1>🔧 IP Camera Management</h1>
            <p style="color: #a0a0a0; margin: 10px 0 0 0;">إدارة وتكوين كاميرات IP</p>
        </div>
        
        <div class="management-grid" id="managementGrid">
            <!-- Add Camera Card -->
            <div class="add-camera-card" onclick="addNewCamera()">
                <span class="add-icon">➕</span>
                <div class="add-text">إضافة كاميرا جديدة</div>
            </div>
            
            <!-- Camera management cards will be populated by JavaScript -->
        </div>
    </div>
    
    <script>
        // Sample camera management data
        const managedCameras = [
            {
                id: 'CAM001',
                name: 'كاميرا المدخل الرئيسي',
                ip: '*************',
                port: 554,
                username: 'admin',
                status: 'online',
                model: 'Hikvision DS-2CD2043G0-I',
                resolution: '4MP',
                fps: 25,
                lastPing: '2024-01-15 14:30:25'
            },
            {
                id: 'CAM002',
                name: 'كاميرا الخزينة',
                ip: '*************',
                port: 554,
                username: 'admin',
                status: 'online',
                model: 'Dahua IPC-HFW4431R-Z',
                resolution: '4MP',
                fps: 30,
                lastPing: '2024-01-15 14:30:20'
            },
            {
                id: 'CAM003',
                name: 'كاميرا صالة العملاء',
                ip: '*************',
                port: 554,
                username: 'admin',
                status: 'maintenance',
                model: 'Axis M3045-V',
                resolution: '2MP',
                fps: 25,
                lastPing: '2024-01-15 14:25:10'
            },
            {
                id: 'CAM004',
                name: 'كاميرا المكاتب',
                ip: '*************',
                port: 554,
                username: 'admin',
                status: 'offline',
                model: 'Hikvision DS-2CD2143G0-I',
                resolution: '4MP',
                fps: 25,
                lastPing: '2024-01-15 13:45:30'
            },
            {
                id: 'CAM005',
                name: 'كاميرا الموقف',
                ip: '*************',
                port: 554,
                username: 'admin',
                status: 'online',
                model: 'Dahua IPC-HFW2431S-S-S2',
                resolution: '4MP',
                fps: 20,
                lastPing: '2024-01-15 14:30:15'
            },
            {
                id: 'CAM006',
                name: 'كاميرا الممر الخلفي',
                ip: '*************',
                port: 554,
                username: 'admin',
                status: 'online',
                model: 'Hikvision DS-2CD2043G0-I',
                resolution: '4MP',
                fps: 25,
                lastPing: '2024-01-15 14:30:18'
            }
        ];
        
        // Initialize management interface
        function initializeManagement() {
            const grid = document.getElementById('managementGrid');
            
            // Keep the add camera card and add managed cameras after it
            managedCameras.forEach(camera => {
                const cameraCard = createManagementCard(camera);
                grid.appendChild(cameraCard);
            });
        }
        
        // Create management card
        function createManagementCard(camera) {
            const card = document.createElement('div');
            card.className = 'camera-management-card';
            
            const statusText = {
                online: 'متصل',
                offline: 'غير متصل',
                maintenance: 'صيانة'
            };
            
            card.innerHTML = `
                <div class="card-header">
                    <div class="camera-title">${camera.name}</div>
                    <div class="camera-id">${camera.id}</div>
                </div>
                
                <div class="camera-details">
                    <div class="detail-row">
                        <span class="detail-label">الحالة:</span>
                        <span class="detail-value">
                            <span class="status-indicator status-${camera.status}"></span>
                            ${statusText[camera.status]}
                        </span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">عنوان IP:</span>
                        <span class="detail-value">${camera.ip}:${camera.port}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">الموديل:</span>
                        <span class="detail-value">${camera.model}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">الدقة:</span>
                        <span class="detail-value">${camera.resolution} @ ${camera.fps}fps</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">آخر اتصال:</span>
                        <span class="detail-value">${camera.lastPing}</span>
                    </div>
                </div>
                
                <div class="camera-actions">
                    <button class="action-btn btn-configure" onclick="configureCamera('${camera.id}')">تكوين</button>
                    <button class="action-btn btn-test" onclick="testCamera('${camera.id}')">اختبار</button>
                    <button class="action-btn btn-restart" onclick="restartCamera('${camera.id}')">إعادة تشغيل</button>
                    <button class="action-btn btn-remove" onclick="removeCamera('${camera.id}')">حذف</button>
                </div>
            `;
            
            return card;
        }
        
        // Management functions
        function addNewCamera() {
            alert('فتح نافذة إضافة كاميرا IP جديدة');
        }
        
        function configureCamera(id) {
            alert(`فتح إعدادات الكاميرا ${id}`);
        }
        
        function testCamera(id) {
            alert(`اختبار الاتصال بالكاميرا ${id}`);
        }
        
        function restartCamera(id) {
            if (confirm(`هل أنت متأكد من إعادة تشغيل الكاميرا ${id}؟`)) {
                alert(`تم إعادة تشغيل الكاميرا ${id}`);
            }
        }
        
        function removeCamera(id) {
            if (confirm(`هل أنت متأكد من حذف الكاميرا ${id}؟`)) {
                alert(`تم حذف الكاميرا ${id}`);
            }
        }
        
        // Initialize on load
        document.addEventListener('DOMContentLoaded', initializeManagement);
        
        console.log('IP Camera Management System Loaded Successfully');
    </script>
</body>
</html>
