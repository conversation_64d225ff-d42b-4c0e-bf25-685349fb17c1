<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - نظام إنذار الحريق</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(26, 54, 93, 0.9);
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(45deg, #1a365d, #2c5282);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .logo-text {
            font-size: 28px;
            font-weight: 900;
            color: white;
        }
        
        .nav-buttons {
            display: flex;
            gap: 15px;
        }
        
        .nav-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
        }
        
        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .page-title {
            text-align: center;
            font-size: 36px;
            font-weight: 900;
            margin-bottom: 10px;
            color: #f56565;
            text-shadow: 0 0 20px rgba(245, 101, 101, 0.5);
        }
        
        .page-subtitle {
            text-align: center;
            font-size: 18px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 40px;
        }
        
        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .panel {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(20px);
        }
        
        .panel-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 20px;
            color: white;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-panel {
            text-align: center;
            background: linear-gradient(135deg, rgba(245, 101, 101, 0.1), rgba(229, 62, 62, 0.1));
            border: 2px solid rgba(245, 101, 101, 0.3);
        }
        
        .system-status {
            font-size: 48px;
            font-weight: 900;
            margin: 20px 0;
            text-shadow: 0 0 15px rgba(72, 187, 120, 0.5);
        }
        
        .status-normal {
            color: #48bb78;
        }
        
        .status-warning {
            color: #ed8936;
            animation: pulse 2s infinite;
        }
        
        .status-danger {
            color: #f56565;
            animation: blink 1s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }
        
        .sensors-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .sensor-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .sensor-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }
        
        .sensor-icon {
            font-size: 32px;
            margin-bottom: 10px;
            display: block;
        }
        
        .sensor-value {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .sensor-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .sensor-normal {
            border-color: rgba(72, 187, 120, 0.3);
        }
        
        .sensor-warning {
            border-color: rgba(237, 137, 54, 0.5);
            background: rgba(237, 137, 54, 0.1);
        }
        
        .sensor-danger {
            border-color: rgba(245, 101, 101, 0.5);
            background: rgba(245, 101, 101, 0.1);
            animation: shake 0.5s infinite;
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        
        .control-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .control-btn {
            background: linear-gradient(135deg, #1a365d, #2c5282);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(26, 54, 93, 0.4);
        }
        
        .test-btn {
            background: linear-gradient(135deg, #ed8936, #dd6b20);
        }
        
        .reset-btn {
            background: linear-gradient(135deg, #f56565, #e53e3e);
        }
        
        .settings-btn {
            background: linear-gradient(135deg, #9f7aea, #805ad5);
        }
        
        .alarm-panel {
            background: linear-gradient(135deg, rgba(245, 101, 101, 0.1), rgba(229, 62, 62, 0.1));
            border: 2px solid rgba(245, 101, 101, 0.3);
            text-align: center;
        }
        
        .alarm-indicator {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            margin: 20px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            font-weight: 900;
            transition: all 0.3s ease;
        }
        
        .alarm-off {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
        }
        
        .alarm-on {
            background: linear-gradient(135deg, #f56565, #e53e3e);
            color: white;
            animation: alarmBlink 0.5s infinite;
        }
        
        @keyframes alarmBlink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }
        
        .events-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: rgba(255, 255, 255, 0.02);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .events-table th,
        .events-table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 13px;
        }
        
        .events-table th {
            background: linear-gradient(135deg, #1a365d, #2c5282);
            color: white;
            font-weight: 600;
        }
        
        .events-table td {
            color: rgba(255, 255, 255, 0.9);
        }
        
        .event-normal {
            color: #48bb78;
        }
        
        .event-warning {
            color: #ed8936;
        }
        
        .event-danger {
            color: #f56565;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
        }
        
        .modal-content {
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            margin: 5% auto;
            padding: 30px;
            border-radius: 20px;
            width: 90%;
            max-width: 600px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .modal-title {
            font-size: 24px;
            font-weight: 700;
            color: #f56565;
        }
        
        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 12px;
            color: white;
            font-size: 14px;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #f56565;
            box-shadow: 0 0 0 2px rgba(245, 101, 101, 0.2);
        }
        
        .save-btn {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .save-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
        }
        
        .notification-panel {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .notification-toggle {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .toggle-switch {
            position: relative;
            width: 50px;
            height: 25px;
            background: #ccc;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .toggle-switch.active {
            background: #48bb78;
        }
        
        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 21px;
            height: 21px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
        }
        
        .toggle-switch.active .toggle-slider {
            transform: translateX(25px);
        }
        
        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
            }
            
            .sensors-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .control-buttons {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">🏦</div>
                <div class="logo-text">EG BANK</div>
            </div>
            
            <div class="nav-buttons">
                <a href="home_simple.html" class="nav-btn">🏠 الرئيسية</a>
                <a href="cameras.html" class="nav-btn">📹 المراقبة</a>
                <a href="security_tasks.html" class="nav-btn">🔧 المهام</a>
                <a href="smartvoice_attendance.html" class="nav-btn">🎤 الحضور</a>
            </div>
        </div>
    </header>
    
    <div class="container">
        <h1 class="page-title">🔥 نظام إنذار الحريق</h1>
        <p class="page-subtitle">نظام متقدم لاكتشاف الحريق والإنذار المبكر</p>
        
        <div class="main-grid">
            <!-- System Status Panel -->
            <div class="panel status-panel">
                <h2 class="panel-title">🛡️ حالة النظام</h2>
                
                <div class="system-status status-normal" id="systemStatus">
                    آمن
                </div>
                
                <div id="statusMessage">
                    جميع الأنظمة تعمل بشكل طبيعي
                </div>
                
                <div style="margin-top: 20px;">
                    <div style="font-size: 14px; color: rgba(255,255,255,0.7);">آخر فحص:</div>
                    <div style="font-size: 16px; font-weight: 600;" id="lastCheck">--:--:--</div>
                </div>
            </div>
            
            <!-- Alarm Panel -->
            <div class="panel alarm-panel">
                <h2 class="panel-title">🚨 حالة الإنذار</h2>
                
                <div class="alarm-indicator alarm-off" id="alarmIndicator">
                    🔕
                </div>
                
                <div id="alarmStatus">الإنذار متوقف</div>
                
                <div style="margin-top: 20px;">
                    <button class="control-btn test-btn" onclick="testAlarm()">
                        🔔 اختبار الإنذار
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Sensors Grid -->
        <div class="panel">
            <h2 class="panel-title">📡 حالة الحساسات</h2>
            
            <div class="sensors-grid">
                <div class="sensor-card sensor-normal" id="smokeSensor">
                    <span class="sensor-icon">💨</span>
                    <div class="sensor-value" id="smokeValue">طبيعي</div>
                    <div class="sensor-label">حساس الدخان</div>
                </div>
                
                <div class="sensor-card sensor-normal" id="tempSensor">
                    <span class="sensor-icon">🌡️</span>
                    <div class="sensor-value" id="tempValue">25°C</div>
                    <div class="sensor-label">حساس الحرارة</div>
                </div>
                
                <div class="sensor-card sensor-normal" id="gasSensor">
                    <span class="sensor-icon">⚗️</span>
                    <div class="sensor-value" id="gasValue">طبيعي</div>
                    <div class="sensor-label">حساس الغازات</div>
                </div>
                
                <div class="sensor-card sensor-normal" id="flameSensor">
                    <span class="sensor-icon">🔥</span>
                    <div class="sensor-value" id="flameValue">لا يوجد</div>
                    <div class="sensor-label">حساس اللهب</div>
                </div>
            </div>
        </div>
        
        <!-- Control Panel -->
        <div class="panel">
            <h2 class="panel-title">🎛️ لوحة التحكم</h2>
            
            <div class="control-buttons">
                <button class="control-btn" onclick="startSystem()">
                    ▶️ تشغيل النظام
                </button>
                <button class="control-btn" onclick="stopSystem()">
                    ⏹️ إيقاف النظام
                </button>
                <button class="control-btn test-btn" onclick="testSystem()">
                    🧪 اختبار شامل
                </button>
                <button class="control-btn reset-btn" onclick="resetAlarm()">
                    🔄 إعادة تعيين
                </button>
                <button class="control-btn settings-btn" onclick="openSettings()">
                    ⚙️ الإعدادات
                </button>
                <button class="control-btn" onclick="exportReport()">
                    📊 تصدير التقرير
                </button>
            </div>
        </div>
        
        <!-- Notifications Panel -->
        <div class="panel">
            <h2 class="panel-title">📱 إعدادات التنبيهات</h2>
            
            <div class="notification-panel">
                <div class="notification-toggle">
                    <div class="toggle-switch" id="smsToggle" onclick="toggleNotification('sms')">
                        <div class="toggle-slider"></div>
                    </div>
                    <span>📱 تنبيهات SMS</span>
                </div>
                
                <div class="notification-toggle">
                    <div class="toggle-switch" id="emailToggle" onclick="toggleNotification('email')">
                        <div class="toggle-slider"></div>
                    </div>
                    <span>📧 تنبيهات الإيميل</span>
                </div>
                
                <div class="notification-toggle">
                    <div class="toggle-switch active" id="soundToggle" onclick="toggleNotification('sound')">
                        <div class="toggle-slider"></div>
                    </div>
                    <span>🔊 الإنذار الصوتي</span>
                </div>
                
                <div class="notification-toggle">
                    <div class="toggle-switch active" id="lightToggle" onclick="toggleNotification('light')">
                        <div class="toggle-slider"></div>
                    </div>
                    <span>💡 الإنذار الضوئي</span>
                </div>
            </div>
        </div>
        
        <!-- Events Log -->
        <div class="panel">
            <h2 class="panel-title">📋 سجل الأحداث</h2>
            
            <div style="overflow-x: auto;">
                <table class="events-table">
                    <thead>
                        <tr>
                            <th>الوقت</th>
                            <th>نوع الحدث</th>
                            <th>الحساس</th>
                            <th>القيمة</th>
                            <th>الحالة</th>
                            <th>الإجراء</th>
                        </tr>
                    </thead>
                    <tbody id="eventsTableBody">
                        <!-- Events will be populated here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Settings Modal -->
    <div id="settingsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">⚙️ إعدادات النظام</h3>
                <button class="close-btn" onclick="closeSettings()">&times;</button>
            </div>
            
            <form id="settingsForm">
                <div class="form-group">
                    <label class="form-label">حد إنذار الحرارة (°C):</label>
                    <input type="number" class="form-input" id="tempThreshold" value="60" min="30" max="100">
                </div>
                
                <div class="form-group">
                    <label class="form-label">حد إنذار الدخان (%):</label>
                    <input type="number" class="form-input" id="smokeThreshold" value="30" min="10" max="100">
                </div>
                
                <div class="form-group">
                    <label class="form-label">رقم الهاتف للتنبيهات:</label>
                    <input type="tel" class="form-input" id="phoneNumber" placeholder="+20123456789">
                </div>
                
                <div class="form-group">
                    <label class="form-label">البريد الإلكتروني للتنبيهات:</label>
                    <input type="email" class="form-input" id="emailAddress" placeholder="<EMAIL>">
                </div>
                
                <div class="form-group">
                    <label class="form-label">فترة الفحص (ثانية):</label>
                    <input type="number" class="form-input" id="checkInterval" value="5" min="1" max="60">
                </div>
                
                <button type="submit" class="save-btn">💾 حفظ الإعدادات</button>
            </form>
        </div>
    </div>
    
    <script>
        // Fire Alarm System
        class FireAlarmSystem {
            constructor() {
                this.isRunning = false;
                this.alarmActive = false;
                this.sensors = {
                    smoke: { value: 0, threshold: 30, status: 'normal' },
                    temperature: { value: 25, threshold: 60, status: 'normal' },
                    gas: { value: 0, threshold: 25, status: 'normal' },
                    flame: { value: false, threshold: true, status: 'normal' }
                };
                this.notifications = {
                    sms: false,
                    email: false,
                    sound: true,
                    light: true
                };
                this.events = [];
                this.checkInterval = 5000; // 5 seconds
                this.intervalId = null;
                this.alarmSound = null;
                this.init();
            }

            init() {
                console.log('🔥 Initializing Fire Alarm System...');

                this.loadSettings();
                this.loadEvents();
                this.updateDisplay();
                this.setupEventListeners();
                this.startSystem();

                console.log('✅ Fire Alarm System initialized successfully');
            }

            loadSettings() {
                try {
                    const savedSettings = localStorage.getItem('fire_alarm_settings');
                    if (savedSettings) {
                        const settings = JSON.parse(savedSettings);
                        this.sensors.temperature.threshold = settings.tempThreshold || 60;
                        this.sensors.smoke.threshold = settings.smokeThreshold || 30;
                        this.checkInterval = (settings.checkInterval || 5) * 1000;
                        this.notifications = { ...this.notifications, ...settings.notifications };
                    }
                } catch (error) {
                    console.error('Error loading settings:', error);
                }
            }

            saveSettings() {
                try {
                    const settings = {
                        tempThreshold: this.sensors.temperature.threshold,
                        smokeThreshold: this.sensors.smoke.threshold,
                        checkInterval: this.checkInterval / 1000,
                        notifications: this.notifications,
                        phoneNumber: document.getElementById('phoneNumber').value,
                        emailAddress: document.getElementById('emailAddress').value
                    };
                    localStorage.setItem('fire_alarm_settings', JSON.stringify(settings));
                    console.log('💾 Settings saved');
                } catch (error) {
                    console.error('Error saving settings:', error);
                }
            }

            loadEvents() {
                try {
                    const savedEvents = localStorage.getItem('fire_alarm_events');
                    if (savedEvents) {
                        this.events = JSON.parse(savedEvents);
                    } else {
                        // Create sample events
                        this.events = [
                            {
                                time: new Date().toLocaleString('ar-EG'),
                                type: 'تشغيل النظام',
                                sensor: 'النظام',
                                value: 'تشغيل',
                                status: 'normal',
                                action: 'بدء المراقبة'
                            }
                        ];
                        this.saveEvents();
                    }
                } catch (error) {
                    console.error('Error loading events:', error);
                    this.events = [];
                }
            }

            saveEvents() {
                try {
                    localStorage.setItem('fire_alarm_events', JSON.stringify(this.events));
                } catch (error) {
                    console.error('Error saving events:', error);
                }
            }

            setupEventListeners() {
                // Settings form
                document.getElementById('settingsForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.updateSettings();
                });

                // Update notification toggles
                Object.keys(this.notifications).forEach(type => {
                    const toggle = document.getElementById(type + 'Toggle');
                    if (toggle && this.notifications[type]) {
                        toggle.classList.add('active');
                    }
                });
            }

            startSystem() {
                if (this.isRunning) return;

                this.isRunning = true;
                this.intervalId = setInterval(() => {
                    this.simulateSensorReadings();
                    this.checkAlarmConditions();
                    this.updateDisplay();
                }, this.checkInterval);

                this.logEvent('تشغيل النظام', 'النظام', 'تشغيل', 'normal', 'بدء المراقبة');
                console.log('▶️ Fire alarm system started');
            }

            stopSystem() {
                if (!this.isRunning) return;

                this.isRunning = false;
                if (this.intervalId) {
                    clearInterval(this.intervalId);
                    this.intervalId = null;
                }

                this.resetAlarm();
                this.logEvent('إيقاف النظام', 'النظام', 'إيقاف', 'normal', 'توقف المراقبة');
                console.log('⏹️ Fire alarm system stopped');
            }

            simulateSensorReadings() {
                // Simulate realistic sensor readings
                const baseTemp = 25;
                const tempVariation = Math.random() * 10 - 5; // ±5°C variation
                this.sensors.temperature.value = Math.round(baseTemp + tempVariation);

                // Occasionally simulate high readings for testing
                if (Math.random() < 0.02) { // 2% chance
                    this.sensors.temperature.value = Math.round(60 + Math.random() * 20);
                }

                // Smoke sensor (0-100%)
                this.sensors.smoke.value = Math.round(Math.random() * 15); // Normal: 0-15%
                if (Math.random() < 0.01) { // 1% chance
                    this.sensors.smoke.value = Math.round(30 + Math.random() * 50);
                }

                // Gas sensor (0-100%)
                this.sensors.gas.value = Math.round(Math.random() * 10); // Normal: 0-10%
                if (Math.random() < 0.01) { // 1% chance
                    this.sensors.gas.value = Math.round(25 + Math.random() * 30);
                }

                // Flame sensor (boolean)
                this.sensors.flame.value = Math.random() < 0.005; // 0.5% chance

                // Update sensor statuses
                this.updateSensorStatuses();
            }

            updateSensorStatuses() {
                // Temperature
                if (this.sensors.temperature.value >= this.sensors.temperature.threshold) {
                    this.sensors.temperature.status = 'danger';
                } else if (this.sensors.temperature.value >= this.sensors.temperature.threshold * 0.8) {
                    this.sensors.temperature.status = 'warning';
                } else {
                    this.sensors.temperature.status = 'normal';
                }

                // Smoke
                if (this.sensors.smoke.value >= this.sensors.smoke.threshold) {
                    this.sensors.smoke.status = 'danger';
                } else if (this.sensors.smoke.value >= this.sensors.smoke.threshold * 0.7) {
                    this.sensors.smoke.status = 'warning';
                } else {
                    this.sensors.smoke.status = 'normal';
                }

                // Gas
                if (this.sensors.gas.value >= 25) {
                    this.sensors.gas.status = 'danger';
                } else if (this.sensors.gas.value >= 15) {
                    this.sensors.gas.status = 'warning';
                } else {
                    this.sensors.gas.status = 'normal';
                }

                // Flame
                if (this.sensors.flame.value) {
                    this.sensors.flame.status = 'danger';
                } else {
                    this.sensors.flame.status = 'normal';
                }
            }

            checkAlarmConditions() {
                const dangerSensors = Object.keys(this.sensors).filter(key =>
                    this.sensors[key].status === 'danger'
                );

                if (dangerSensors.length > 0 && !this.alarmActive) {
                    this.activateAlarm(dangerSensors);
                } else if (dangerSensors.length === 0 && this.alarmActive) {
                    this.deactivateAlarm();
                }
            }

            activateAlarm(triggeredSensors) {
                this.alarmActive = true;

                // Log alarm activation
                triggeredSensors.forEach(sensor => {
                    const sensorData = this.sensors[sensor];
                    const sensorName = this.getSensorName(sensor);
                    const value = sensor === 'flame' ? (sensorData.value ? 'مكتشف' : 'غير مكتشف') :
                                 sensor === 'temperature' ? sensorData.value + '°C' :
                                 sensorData.value + '%';

                    this.logEvent('إنذار حريق', sensorName, value, 'danger', 'تفعيل الإنذار');
                });

                // Activate alarms
                if (this.notifications.sound) {
                    this.playAlarmSound();
                }

                if (this.notifications.light) {
                    this.activateVisualAlarm();
                }

                // Send notifications
                if (this.notifications.sms) {
                    this.sendSMSAlert(triggeredSensors);
                }

                if (this.notifications.email) {
                    this.sendEmailAlert(triggeredSensors);
                }

                console.log('🚨 FIRE ALARM ACTIVATED!', triggeredSensors);
            }

            deactivateAlarm() {
                this.alarmActive = false;
                this.stopAlarmSound();
                this.logEvent('إيقاف الإنذار', 'النظام', 'طبيعي', 'normal', 'عودة للوضع الطبيعي');
                console.log('✅ Fire alarm deactivated - conditions normal');
            }

            playAlarmSound() {
                try {
                    // Create audio context for alarm sound
                    if (!this.alarmSound) {
                        this.alarmSound = setInterval(() => {
                            // Create beep sound using Web Audio API
                            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                            const oscillator = audioContext.createOscillator();
                            const gainNode = audioContext.createGain();

                            oscillator.connect(gainNode);
                            gainNode.connect(audioContext.destination);

                            oscillator.frequency.value = 800; // High pitch alarm
                            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

                            oscillator.start(audioContext.currentTime);
                            oscillator.stop(audioContext.currentTime + 0.5);
                        }, 1000);
                    }
                } catch (error) {
                    console.warn('Could not play alarm sound:', error);
                }
            }

            stopAlarmSound() {
                if (this.alarmSound) {
                    clearInterval(this.alarmSound);
                    this.alarmSound = null;
                }
            }

            activateVisualAlarm() {
                // Visual alarm is handled by CSS animations
                document.body.style.animation = 'none';
                setTimeout(() => {
                    document.body.style.animation = 'alarmBlink 0.5s infinite';
                }, 10);
            }

            sendSMSAlert(sensors) {
                const message = `🚨 إنذار حريق - EG BANK\nالحساسات المتأثرة: ${sensors.map(s => this.getSensorName(s)).join(', ')}\nالوقت: ${new Date().toLocaleString('ar-EG')}`;

                // Simulate SMS sending
                console.log('📱 SMS Alert sent:', message);
                this.logEvent('إرسال SMS', 'النظام', 'تنبيه', 'warning', 'تم إرسال رسالة نصية');
            }

            sendEmailAlert(sensors) {
                const subject = '🚨 إنذار حريق عاجل - EG BANK';
                const body = `تم اكتشاف حريق في النظام.\nالحساسات المتأثرة: ${sensors.map(s => this.getSensorName(s)).join(', ')}\nالوقت: ${new Date().toLocaleString('ar-EG')}`;

                // Simulate email sending
                console.log('📧 Email Alert sent:', { subject, body });
                this.logEvent('إرسال إيميل', 'النظام', 'تنبيه', 'warning', 'تم إرسال بريد إلكتروني');
            }

            getSensorName(sensor) {
                const names = {
                    smoke: 'حساس الدخان',
                    temperature: 'حساس الحرارة',
                    gas: 'حساس الغازات',
                    flame: 'حساس اللهب'
                };
                return names[sensor] || sensor;
            }

            updateDisplay() {
                // Update last check time
                document.getElementById('lastCheck').textContent = new Date().toLocaleTimeString('ar-EG');

                // Update system status
                const systemStatus = document.getElementById('systemStatus');
                const statusMessage = document.getElementById('statusMessage');

                if (!this.isRunning) {
                    systemStatus.textContent = 'متوقف';
                    systemStatus.className = 'system-status status-warning';
                    statusMessage.textContent = 'النظام متوقف';
                } else if (this.alarmActive) {
                    systemStatus.textContent = 'إنذار!';
                    systemStatus.className = 'system-status status-danger';
                    statusMessage.textContent = 'تم اكتشاف حريق - اتخذ الإجراءات اللازمة';
                } else {
                    const warningSensors = Object.keys(this.sensors).filter(key =>
                        this.sensors[key].status === 'warning'
                    );

                    if (warningSensors.length > 0) {
                        systemStatus.textContent = 'تحذير';
                        systemStatus.className = 'system-status status-warning';
                        statusMessage.textContent = 'قيم مرتفعة في بعض الحساسات';
                    } else {
                        systemStatus.textContent = 'آمن';
                        systemStatus.className = 'system-status status-normal';
                        statusMessage.textContent = 'جميع الأنظمة تعمل بشكل طبيعي';
                    }
                }

                // Update alarm indicator
                const alarmIndicator = document.getElementById('alarmIndicator');
                const alarmStatus = document.getElementById('alarmStatus');

                if (this.alarmActive) {
                    alarmIndicator.className = 'alarm-indicator alarm-on';
                    alarmIndicator.textContent = '🚨';
                    alarmStatus.textContent = 'الإنذار نشط!';
                } else {
                    alarmIndicator.className = 'alarm-indicator alarm-off';
                    alarmIndicator.textContent = '🔕';
                    alarmStatus.textContent = 'الإنذار متوقف';
                }

                // Update sensor displays
                this.updateSensorDisplay('smoke', this.sensors.smoke.value + '%');
                this.updateSensorDisplay('temp', this.sensors.temperature.value + '°C');
                this.updateSensorDisplay('gas', this.sensors.gas.value + '%');
                this.updateSensorDisplay('flame', this.sensors.flame.value ? 'مكتشف' : 'لا يوجد');

                // Update events table
                this.updateEventsTable();
            }

            updateSensorDisplay(sensorType, value) {
                const sensorMap = {
                    smoke: 'smoke',
                    temp: 'temperature',
                    gas: 'gas',
                    flame: 'flame'
                };

                const sensor = this.sensors[sensorMap[sensorType]];
                const sensorCard = document.getElementById(sensorType + 'Sensor');
                const valueElement = document.getElementById(sensorType + 'Value');

                if (sensorCard && valueElement) {
                    valueElement.textContent = value;

                    // Update card styling based on status
                    sensorCard.className = `sensor-card sensor-${sensor.status}`;
                }
            }

            updateEventsTable() {
                const tbody = document.getElementById('eventsTableBody');
                tbody.innerHTML = '';

                // Show last 10 events
                const recentEvents = this.events.slice(-10).reverse();

                recentEvents.forEach(event => {
                    const row = tbody.insertRow();
                    row.innerHTML = `
                        <td>${event.time}</td>
                        <td>${event.type}</td>
                        <td>${event.sensor}</td>
                        <td>${event.value}</td>
                        <td class="event-${event.status}">${this.getStatusText(event.status)}</td>
                        <td>${event.action}</td>
                    `;
                });
            }

            getStatusText(status) {
                const statusMap = {
                    normal: 'طبيعي',
                    warning: 'تحذير',
                    danger: 'خطر'
                };
                return statusMap[status] || status;
            }

            logEvent(type, sensor, value, status, action) {
                const event = {
                    time: new Date().toLocaleString('ar-EG'),
                    type,
                    sensor,
                    value,
                    status,
                    action
                };

                this.events.push(event);

                // Keep only last 100 events
                if (this.events.length > 100) {
                    this.events = this.events.slice(-100);
                }

                this.saveEvents();
            }

            testAlarm() {
                if (!this.isRunning) {
                    alert('يجب تشغيل النظام أولاً');
                    return;
                }

                // Simulate alarm for 5 seconds
                this.alarmActive = true;
                this.updateDisplay();

                if (this.notifications.sound) {
                    this.playAlarmSound();
                }

                this.logEvent('اختبار الإنذار', 'النظام', 'اختبار', 'warning', 'اختبار الإنذار');

                setTimeout(() => {
                    this.alarmActive = false;
                    this.stopAlarmSound();
                    this.updateDisplay();
                    this.logEvent('انتهاء الاختبار', 'النظام', 'طبيعي', 'normal', 'انتهاء اختبار الإنذار');
                }, 5000);

                alert('تم تشغيل اختبار الإنذار لمدة 5 ثوان');
            }

            testSystem() {
                if (!this.isRunning) {
                    alert('يجب تشغيل النظام أولاً');
                    return;
                }

                // Test all sensors
                this.logEvent('اختبار شامل', 'النظام', 'بدء', 'warning', 'اختبار جميع الحساسات');

                // Simulate high readings for testing
                const originalValues = { ...this.sensors };

                this.sensors.temperature.value = 70;
                this.sensors.smoke.value = 40;
                this.sensors.gas.value = 30;
                this.sensors.flame.value = true;

                this.updateSensorStatuses();
                this.updateDisplay();

                setTimeout(() => {
                    this.sensors = originalValues;
                    this.updateSensorStatuses();
                    this.updateDisplay();
                    this.logEvent('انتهاء الاختبار', 'النظام', 'طبيعي', 'normal', 'انتهاء الاختبار الشامل');
                }, 3000);

                alert('تم تشغيل الاختبار الشامل لمدة 3 ثوان');
            }

            resetAlarm() {
                this.alarmActive = false;
                this.stopAlarmSound();
                document.body.style.animation = 'none';

                // Reset all sensor statuses to normal
                Object.keys(this.sensors).forEach(key => {
                    if (key !== 'temperature') {
                        this.sensors[key].value = key === 'flame' ? false : 0;
                    } else {
                        this.sensors[key].value = 25;
                    }
                    this.sensors[key].status = 'normal';
                });

                this.updateDisplay();
                this.logEvent('إعادة تعيين', 'النظام', 'إعادة تعيين', 'normal', 'إعادة تعيين النظام');
                alert('تم إعادة تعيين النظام');
            }

            openSettings() {
                // Fill current settings
                document.getElementById('tempThreshold').value = this.sensors.temperature.threshold;
                document.getElementById('smokeThreshold').value = this.sensors.smoke.threshold;
                document.getElementById('checkInterval').value = this.checkInterval / 1000;

                document.getElementById('settingsModal').style.display = 'block';
            }

            closeSettings() {
                document.getElementById('settingsModal').style.display = 'none';
            }

            updateSettings() {
                this.sensors.temperature.threshold = parseInt(document.getElementById('tempThreshold').value);
                this.sensors.smoke.threshold = parseInt(document.getElementById('smokeThreshold').value);
                this.checkInterval = parseInt(document.getElementById('checkInterval').value) * 1000;

                this.saveSettings();
                this.closeSettings();

                // Restart system with new settings
                if (this.isRunning) {
                    this.stopSystem();
                    setTimeout(() => this.startSystem(), 1000);
                }

                alert('تم حفظ الإعدادات بنجاح');
            }

            toggleNotification(type) {
                this.notifications[type] = !this.notifications[type];
                const toggle = document.getElementById(type + 'Toggle');

                if (this.notifications[type]) {
                    toggle.classList.add('active');
                } else {
                    toggle.classList.remove('active');
                }

                this.saveSettings();
            }

            exportReport() {
                const report = {
                    systemStatus: this.isRunning ? 'يعمل' : 'متوقف',
                    alarmStatus: this.alarmActive ? 'نشط' : 'متوقف',
                    sensors: this.sensors,
                    events: this.events,
                    settings: {
                        tempThreshold: this.sensors.temperature.threshold,
                        smokeThreshold: this.sensors.smoke.threshold,
                        checkInterval: this.checkInterval / 1000,
                        notifications: this.notifications
                    },
                    exportTime: new Date().toISOString()
                };

                const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `fire_alarm_report_${new Date().toISOString().split('T')[0]}.json`;
                a.click();
                URL.revokeObjectURL(url);

                this.logEvent('تصدير التقرير', 'النظام', 'تقرير', 'normal', 'تم تصدير التقرير');
                alert('تم تصدير التقرير بنجاح');
            }
        }

        // Global functions
        function startSystem() {
            if (window.fireAlarmSystem) {
                window.fireAlarmSystem.startSystem();
            }
        }

        function stopSystem() {
            if (window.fireAlarmSystem) {
                window.fireAlarmSystem.stopSystem();
            }
        }

        function testAlarm() {
            if (window.fireAlarmSystem) {
                window.fireAlarmSystem.testAlarm();
            }
        }

        function testSystem() {
            if (window.fireAlarmSystem) {
                window.fireAlarmSystem.testSystem();
            }
        }

        function resetAlarm() {
            if (window.fireAlarmSystem) {
                window.fireAlarmSystem.resetAlarm();
            }
        }

        function openSettings() {
            if (window.fireAlarmSystem) {
                window.fireAlarmSystem.openSettings();
            }
        }

        function closeSettings() {
            if (window.fireAlarmSystem) {
                window.fireAlarmSystem.closeSettings();
            }
        }

        function toggleNotification(type) {
            if (window.fireAlarmSystem) {
                window.fireAlarmSystem.toggleNotification(type);
            }
        }

        function exportReport() {
            if (window.fireAlarmSystem) {
                window.fireAlarmSystem.exportReport();
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔥 Fire Alarm System loading...');
            window.fireAlarmSystem = new FireAlarmSystem();
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            const modal = document.getElementById('settingsModal');
            if (event.target === modal) {
                closeSettings();
            }
        });

        console.log('🔥 Fire Alarm System Script Loaded');
    </script>
</body>
</html>
