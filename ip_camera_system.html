<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EG BANK - IP Camera + NVR System</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            background-color: #2c2c2c;
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            background: #3a3a3a;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            color: white;
            font-size: 32px;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .control-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .control-card {
            background: linear-gradient(135deg, #1a365d 0%, #2c5282 100%);
            border-radius: 10px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .control-card:hover {
            transform: translateY(-5px);
            border-color: rgba(255,255,255,0.3);
            box-shadow: 0 10px 30px rgba(26, 54, 93, 0.4);
        }
        
        .control-card .icon {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }
        
        .control-card .title {
            color: white;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .control-card .description {
            color: rgba(255,255,255,0.8);
            font-size: 14px;
        }
        
        .camera-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .camera-feed {
            background: #4a4a4a;
            border-radius: 10px;
            padding: 15px;
            border: 2px solid #555555;
        }
        
        .camera-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .camera-name {
            color: white;
            font-weight: bold;
            font-size: 16px;
        }
        
        .camera-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-online {
            background: #48bb78;
            color: white;
        }
        
        .status-offline {
            background: #f56565;
            color: white;
        }
        
        .camera-display {
            width: 100%;
            height: 200px;
            background: #2c2c2c;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
        }
        
        .camera-placeholder {
            color: #a0a0a0;
            font-size: 14px;
            text-align: center;
        }
        
        .camera-controls {
            display: flex;
            gap: 8px;
        }
        
        .control-btn {
            flex: 1;
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-view {
            background: #1a365d;
            color: white;
        }
        
        .btn-view:hover {
            background: #2c5282;
        }
        
        .btn-record {
            background: #e53e3e;
            color: white;
        }
        
        .btn-record:hover {
            background: #c53030;
        }
        
        .btn-settings {
            background: #38a169;
            color: white;
        }
        
        .btn-settings:hover {
            background: #2f855a;
        }
        
        .nvr-panel {
            background: #4a4a4a;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
            border: 2px solid #555555;
        }
        
        .nvr-header {
            color: white;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .nvr-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .stat-item {
            background: #3a3a3a;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-value {
            color: #1a365d;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: white;
            font-size: 14px;
        }
        
        .back-btn {
            background: #1a365d;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: #2c5282;
        }
        
        @media (max-width: 768px) {
            .control-panel {
                grid-template-columns: 1fr;
            }
            
            .camera-grid {
                grid-template-columns: 1fr;
            }
            
            .nvr-stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <button class="back-btn" onclick="window.history.back()">← رجوع</button>
        
        <div class="header">
            <h1>📹 IP Camera + NVR System</h1>
            <p style="color: #a0a0a0; margin: 10px 0 0 0;">نظام كاميرات IP ومسجل الشبكة الرقمي</p>
        </div>
        
        <!-- Control Panel -->
        <div class="control-panel">
            <div class="control-card" onclick="addNewCamera()">
                <span class="icon">📷</span>
                <div class="title">إضافة كاميرا جديدة</div>
                <div class="description">إضافة كاميرا IP جديدة للنظام</div>
            </div>
            
            <div class="control-card" onclick="configureNVR()">
                <span class="icon">💾</span>
                <div class="title">إعداد NVR</div>
                <div class="description">تكوين مسجل الشبكة الرقمي</div>
            </div>
            
            <div class="control-card" onclick="viewRecordings()">
                <span class="icon">🎬</span>
                <div class="title">عرض التسجيلات</div>
                <div class="description">مراجعة التسجيلات المحفوظة</div>
            </div>
            
            <div class="control-card" onclick="systemSettings()">
                <span class="icon">⚙️</span>
                <div class="title">إعدادات النظام</div>
                <div class="description">تكوين إعدادات الكاميرات</div>
            </div>
        </div>
        
        <!-- NVR Status Panel -->
        <div class="nvr-panel">
            <div class="nvr-header">📊 حالة مسجل الشبكة الرقمي (NVR)</div>
            <div class="nvr-stats">
                <div class="stat-item">
                    <div class="stat-value" id="totalCameras">8</div>
                    <div class="stat-label">إجمالي الكاميرات</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="onlineCameras">6</div>
                    <div class="stat-label">كاميرات متصلة</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="recordingCameras">4</div>
                    <div class="stat-label">كاميرات تسجل</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="storageUsed">65%</div>
                    <div class="stat-label">مساحة التخزين</div>
                </div>
            </div>
        </div>
        
        <!-- Camera Grid -->
        <div class="camera-grid" id="cameraGrid">
            <!-- Cameras will be populated by JavaScript -->
        </div>
    </div>
    
    <script>
        // Sample camera data
        const cameras = [
            { id: 1, name: 'كاميرا المدخل الرئيسي', ip: '*************', status: 'online', recording: true },
            { id: 2, name: 'كاميرا الخزينة', ip: '*************', status: 'online', recording: true },
            { id: 3, name: 'كاميرا صالة العملاء', ip: '*************', status: 'online', recording: false },
            { id: 4, name: 'كاميرا المكاتب', ip: '*************', status: 'offline', recording: false },
            { id: 5, name: 'كاميرا الموقف', ip: '*************', status: 'online', recording: true },
            { id: 6, name: 'كاميرا الممر الخلفي', ip: '*************', status: 'online', recording: false },
            { id: 7, name: 'كاميرا السطح', ip: '*************', status: 'offline', recording: false },
            { id: 8, name: 'كاميرا المخزن', ip: '*************', status: 'online', recording: true }
        ];
        
        // Initialize cameras
        function initializeCameras() {
            const grid = document.getElementById('cameraGrid');
            grid.innerHTML = '';
            
            cameras.forEach(camera => {
                const cameraCard = createCameraCard(camera);
                grid.appendChild(cameraCard);
            });
            
            updateStats();
        }
        
        // Create camera card
        function createCameraCard(camera) {
            const card = document.createElement('div');
            card.className = 'camera-feed';
            
            card.innerHTML = `
                <div class="camera-header">
                    <div class="camera-name">${camera.name}</div>
                    <div class="camera-status status-${camera.status}">
                        ${camera.status === 'online' ? 'متصل' : 'غير متصل'}
                    </div>
                </div>
                
                <div class="camera-display">
                    ${camera.status === 'online' ? 
                        `<div style="width: 100%; height: 100%; background: linear-gradient(45deg, #1a365d, #2c5282); display: flex; align-items: center; justify-content: center; color: white; font-size: 16px;">📹 Live Feed</div>` :
                        `<div class="camera-placeholder">📵 الكاميرا غير متصلة</div>`
                    }
                </div>
                
                <div class="camera-controls">
                    <button class="control-btn btn-view" onclick="viewCamera(${camera.id})">عرض</button>
                    <button class="control-btn btn-record" onclick="toggleRecording(${camera.id})" 
                            style="background: ${camera.recording ? '#e53e3e' : '#38a169'}">
                        ${camera.recording ? 'إيقاف التسجيل' : 'بدء التسجيل'}
                    </button>
                    <button class="control-btn btn-settings" onclick="cameraSettings(${camera.id})">إعدادات</button>
                </div>
            `;
            
            return card;
        }
        
        // Update statistics
        function updateStats() {
            const total = cameras.length;
            const online = cameras.filter(c => c.status === 'online').length;
            const recording = cameras.filter(c => c.recording).length;
            
            document.getElementById('totalCameras').textContent = total;
            document.getElementById('onlineCameras').textContent = online;
            document.getElementById('recordingCameras').textContent = recording;
        }
        
        // Camera functions
        function addNewCamera() {
            alert('فتح نافذة إضافة كاميرا جديدة');
        }
        
        function configureNVR() {
            alert('فتح إعدادات مسجل الشبكة الرقمي');
        }
        
        function viewRecordings() {
            alert('فتح مكتبة التسجيلات');
        }
        
        function systemSettings() {
            alert('فتح إعدادات النظام');
        }
        
        function viewCamera(id) {
            alert(`عرض الكاميرا رقم ${id} في نافذة منفصلة`);
        }
        
        function toggleRecording(id) {
            const camera = cameras.find(c => c.id === id);
            if (camera) {
                camera.recording = !camera.recording;
                initializeCameras();
            }
        }
        
        function cameraSettings(id) {
            alert(`فتح إعدادات الكاميرا رقم ${id}`);
        }
        
        // Initialize on load
        document.addEventListener('DOMContentLoaded', initializeCameras);
        
        console.log('IP Camera + NVR System Loaded Successfully');
    </script>
</body>
</html>
