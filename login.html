<!DOCTYPE html>
<html>
<head>
    <title>EG BANK Login</title>
    <style>
        #loading-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #2c2c2c;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .loader {
            border: 16px solid #3a3a3a; /* Dark gray */
            border-top: 16px solid #1a365d; /* Dark blue */
            border-radius: 50%;
            width: 120px;
            height: 120px;
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 20% 50%, rgba(26, 54, 93, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(44, 82, 130, 0.1) 0%, transparent 50%);
            z-index: -1;
        }
        .login-container {
            padding: 30px;
            border: 2px solid #1a365d;
            border-radius: 12px;
            background-color: #3a3a3a;
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
            text-align: center;
            min-width: 350px;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            margin-bottom: 15px;
            color: white;
            background-color: #2c2c2c;
            border: 2px solid #555555;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        input[type="text"]:focus, input[type="password"]:focus {
            outline: none;
            border-color: #1a365d;
            box-shadow: 0 0 0 3px rgba(26, 54, 93, 0.3);
        }
        input[type="submit"] {
            width: 100%;
            background: linear-gradient(135deg, #1a365d 0%, #2c5282 100%);
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-top: 10px;
        }
        input[type="submit"]:hover {
            background: linear-gradient(135deg, #2c5282 0%, #3182ce 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(26, 54, 93, 0.4);
        }
    </style>
</head>
<body>
    <div id="loading-container">
        <div class="loader"></div>
    </div>
   <script>
        window.addEventListener('load', function() {
            document.getElementById('loading-container').style.display = 'none';
        });
    </script>
    <div class="login-container">
        <h1 style="color: white; margin-bottom: 30px; font-size: 28px; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">🏦 EG BANK Login</h1>
        <form id="loginForm">
            <label for="username" style="color: white;">Username:</label><br>
            <input type="text" id="username" name="username"><br><br>
            <label for="password" style="color: white;">Password:</label><br>
            <input type="password" id="password" name="password"><br><br>
            <input type="submit" value="Submit">
        </form>
    </div>
    <script src="login.js"></script>

    <script>
        // Handle login form submission
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            // Simple validation
            if (username && password) {
                // Show loading effect
                showLoginProgress();

                // Simulate login process
                setTimeout(() => {
                    // Store login status
                    localStorage.setItem('isLoggedIn', 'true');
                    localStorage.setItem('username', username);
                    localStorage.setItem('loginTime', new Date().toISOString());

                    // Redirect to main interface
                    window.location.href = 'modern_interface.html';
                }, 1500);
            } else {
                showError('يرجى إدخال اسم المستخدم وكلمة المرور');
            }
        });

        // Show login progress
        function showLoginProgress() {
            const submitBtn = document.querySelector('input[type="submit"]');
            submitBtn.value = 'جاري تسجيل الدخول...';
            submitBtn.disabled = true;
            submitBtn.style.background = 'linear-gradient(135deg, #666 0%, #888 100%)';
        }

        // Show error message
        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.style.cssText = `
                background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-top: 15px;
                text-align: center;
                font-weight: 500;
                animation: shake 0.5s ease-in-out;
            `;
            errorDiv.textContent = message;

            // Remove existing error
            const existingError = document.querySelector('.error-message');
            if (existingError) {
                existingError.remove();
            }

            errorDiv.className = 'error-message';
            document.querySelector('.login-container').appendChild(errorDiv);

            // Remove error after 3 seconds
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.remove();
                }
            }, 3000);
        }

        // Add shake animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-5px); }
                75% { transform: translateX(5px); }
            }
        `;
        document.head.appendChild(style);

        // Check if already logged in
        if (localStorage.getItem('isLoggedIn') === 'true') {
            window.location.href = 'modern_interface.html';
        }

        console.log('EG BANK Login System Loaded Successfully');
    </script>
</body>
</html>