<!DOCTYPE html>
<html>
<head>
    <title>EG BANK Login</title>
    <style>
        #loading-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #2c2c2c;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .loader {
            border: 16px solid #3a3a3a; /* Dark gray */
            border-top: 16px solid #1a365d; /* Dark blue */
            border-radius: 50%;
            width: 120px;
            height: 120px;
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 20% 50%, rgba(26, 54, 93, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(44, 82, 130, 0.1) 0%, transparent 50%);
            z-index: -1;
        }
        .login-container {
            padding: 30px;
            border: 2px solid #1a365d;
            border-radius: 12px;
            background-color: #3a3a3a;
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
            text-align: center;
            min-width: 350px;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            margin-bottom: 15px;
            color: white;
            background-color: #2c2c2c;
            border: 2px solid #555555;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        input[type="text"]:focus, input[type="password"]:focus {
            outline: none;
            border-color: #1a365d;
            box-shadow: 0 0 0 3px rgba(26, 54, 93, 0.3);
        }
        input[type="submit"] {
            width: 100%;
            background: linear-gradient(135deg, #1a365d 0%, #2c5282 100%);
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-top: 10px;
        }
        input[type="submit"]:hover {
            background: linear-gradient(135deg, #2c5282 0%, #3182ce 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(26, 54, 93, 0.4);
        }
    </style>
</head>
<body>
    <div id="loading-container">
        <div class="loader"></div>
    </div>
   <script>
        window.addEventListener('load', function() {
            document.getElementById('loading-container').style.display = 'none';
        });
    </script>
    <div class="login-container">
        <h1 style="color: white; margin-bottom: 30px; font-size: 28px; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">🏦 EG BANK Login</h1>
        <form id="loginForm">
            <label for="username" style="color: white;">Username:</label><br>
            <input type="text" id="username" name="username"><br><br>
            <label for="password" style="color: white;">Password:</label><br>
            <input type="password" id="password" name="password"><br><br>
            <input type="submit" value="Submit">
        </form>
    </div>
    <script src="app_core.js"></script>

    <script>
        // Wait for EG BANK system to be ready
        document.addEventListener('egbankReady', function() {
            console.log('EG BANK system ready, setting up login form');
            setupLoginForm();
        });

        // Setup login form
        function setupLoginForm() {
            const loginForm = document.getElementById('loginForm');
            if (!loginForm) {
                console.error('Login form not found');
                return;
            }

            loginForm.addEventListener('submit', async function(e) {
                e.preventDefault();

                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value.trim();

                if (!username || !password) {
                    showError('يرجى إدخال اسم المستخدم وكلمة المرور');
                    return;
                }

                // Show loading effect
                showLoginProgress();

                try {
                    // Use the integrated login system
                    const result = await window.egbank.login(username, password);

                    if (result.success) {
                        showSuccess('تم تسجيل الدخول بنجاح');

                        // Redirect after short delay
                        setTimeout(() => {
                            window.location.href = 'home_simple.html';
                        }, 1000);
                    } else {
                        showError(result.message || 'فشل تسجيل الدخول');
                        resetLoginForm();
                    }
                } catch (error) {
                    console.error('Login error:', error);
                    showError('حدث خطأ أثناء تسجيل الدخول');
                    resetLoginForm();
                }
            });
        }

        // Show login progress
        function showLoginProgress() {
            const submitBtn = document.querySelector('input[type="submit"]');
            if (submitBtn) {
                submitBtn.value = 'جاري تسجيل الدخول...';
                submitBtn.disabled = true;
                submitBtn.style.background = 'linear-gradient(135deg, #666 0%, #888 100%)';
            }
        }

        // Show error message
        function showError(message) {
            removeExistingMessages();

            const errorDiv = document.createElement('div');
            errorDiv.style.cssText = `
                background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-top: 15px;
                text-align: center;
                font-weight: 500;
                animation: shake 0.5s ease-in-out;
            `;
            errorDiv.textContent = message;
            errorDiv.className = 'login-message error-message';

            const container = document.querySelector('.login-container');
            if (container) {
                container.appendChild(errorDiv);
            }

            // Remove error after 4 seconds
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.remove();
                }
            }, 4000);
        }

        // Show success message
        function showSuccess(message) {
            removeExistingMessages();

            const successDiv = document.createElement('div');
            successDiv.style.cssText = `
                background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-top: 15px;
                text-align: center;
                font-weight: 500;
                animation: fadeIn 0.5s ease-in-out;
            `;
            successDiv.textContent = message;
            successDiv.className = 'login-message success-message';

            const container = document.querySelector('.login-container');
            if (container) {
                container.appendChild(successDiv);
            }
        }

        // Remove existing messages
        function removeExistingMessages() {
            const existingMessages = document.querySelectorAll('.login-message');
            existingMessages.forEach(msg => msg.remove());
        }

        // Reset login form
        function resetLoginForm() {
            const submitBtn = document.querySelector('input[type="submit"]');
            if (submitBtn) {
                submitBtn.value = 'تسجيل الدخول';
                submitBtn.disabled = false;
                submitBtn.style.background = 'linear-gradient(135deg, #1a365d 0%, #2c5282 100%)';
            }

            // Clear password field for security
            const passwordField = document.getElementById('password');
            if (passwordField) {
                passwordField.value = '';
            }
        }

        // Add animations
        function addAnimations() {
            if (!document.getElementById('login-animations')) {
                const style = document.createElement('style');
                style.id = 'login-animations';
                style.textContent = `
                    @keyframes shake {
                        0%, 100% { transform: translateX(0); }
                        25% { transform: translateX(-5px); }
                        75% { transform: translateX(5px); }
                    }
                    @keyframes fadeIn {
                        from { opacity: 0; transform: translateY(10px); }
                        to { opacity: 1; transform: translateY(0); }
                    }
                `;
                document.head.appendChild(style);
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Login page loading...');

            // Add animations
            addAnimations();

            // Check if already logged in
            if (window.egbank && window.egbank.isAuthenticated()) {
                console.log('User already authenticated, redirecting...');
                window.location.href = 'home_simple.html';
                return;
            }

            // If EG BANK system is already ready, setup immediately
            if (window.EGBankApp && window.EGBankApp.isInitialized) {
                setupLoginForm();
            }

            console.log('EG BANK Login System Loaded Successfully');
        });
    </script>
</body>
</html>