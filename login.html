<!DOCTYPE html>
<html>
<head>
    <title>Login</title>
    <style>
        #loading-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #2c2c2c;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .loader {
            border: 16px solid #3a3a3a; /* Dark gray */
            border-top: 16px solid #1a365d; /* Dark blue */
            border-radius: 50%;
            width: 120px;
            height: 120px;
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #2c2c2c;
            color: white;
        }
        .login-container {
            padding: 20px;
            border: 2px solid #555555;
            border-radius: 5px;
            background-color: #3a3a3a;
        }
        input[type="text"], input[type="password"] {
            margin-bottom: 10px;
            color: white;
            background-color: #2c2c2c;
            border: 1px solid #555555;
            padding: 8px;
            border-radius: 4px;
        }
        input[type="submit"] {
            background-color: #1a365d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
        }
        input[type="submit"]:hover {
            background-color: #2c5282;
        }
    </style>
</head>
<body>
    <div id="loading-container">
        <div class="loader"></div>
    </div>
   <script>
        window.addEventListener('load', function() {
            document.getElementById('loading-container').style.display = 'none';
        });
    </script>
    <div class="login-container">
        <h1 style="color: white;">Login</h1>
        <form id="loginForm">
            <label for="username" style="color: white;">Username:</label><br>
            <input type="text" id="username" name="username"><br><br>
            <label for="password" style="color: white;">Password:</label><br>
            <input type="password" id="password" name="password"><br><br>
            <input type="submit" value="Submit">
        </form>
    </div>
    <script src="login.js"></script>
</body>
</html>