// Advanced Local Database Manager
// نظام إدارة قاعدة البيانات المحلية المتقدم

class DatabaseManager {
    constructor() {
        this.dbName = 'EG_BANK_SECURITY_DB';
        this.version = 1;
        this.db = null;
        this.isInitialized = false;
        this.init();
    }

    // Initialize IndexedDB
    async init() {
        try {
            this.db = await this.openDatabase();
            this.isInitialized = true;
            console.log('Database initialized successfully');
        } catch (error) {
            console.error('Database initialization failed:', error);
            // Fallback to localStorage
            this.useFallback = true;
        }
    }

    // Open IndexedDB database
    openDatabase() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.version);

            request.onerror = () => reject(request.error);
            request.onsuccess = () => resolve(request.result);

            request.onupgradeneeded = (event) => {
                const db = event.target.result;

                // Create object stores
                this.createObjectStores(db);
            };
        });
    }

    // Create object stores (tables)
    createObjectStores(db) {
        // Users store
        if (!db.objectStoreNames.contains('users')) {
            const usersStore = db.createObjectStore('users', { keyPath: 'id' });
            usersStore.createIndex('username', 'username', { unique: true });
            usersStore.createIndex('email', 'email', { unique: false });
        }

        // Recordings store
        if (!db.objectStoreNames.contains('recordings')) {
            const recordingsStore = db.createObjectStore('recordings', { keyPath: 'id' });
            recordingsStore.createIndex('camera', 'camera', { unique: false });
            recordingsStore.createIndex('startTime', 'startTime', { unique: false });
            recordingsStore.createIndex('type', 'type', { unique: false });
        }

        // Access logs store
        if (!db.objectStoreNames.contains('accessLogs')) {
            const accessLogsStore = db.createObjectStore('accessLogs', { keyPath: 'id' });
            accessLogsStore.createIndex('userId', 'userId', { unique: false });
            accessLogsStore.createIndex('timestamp', 'timestamp', { unique: false });
            accessLogsStore.createIndex('action', 'action', { unique: false });
        }

        // Camera settings store
        if (!db.objectStoreNames.contains('cameraSettings')) {
            const cameraStore = db.createObjectStore('cameraSettings', { keyPath: 'id' });
            cameraStore.createIndex('name', 'name', { unique: false });
            cameraStore.createIndex('location', 'location', { unique: false });
        }

        // System settings store
        if (!db.objectStoreNames.contains('systemSettings')) {
            const settingsStore = db.createObjectStore('systemSettings', { keyPath: 'key' });
        }

        // Security alerts store
        if (!db.objectStoreNames.contains('securityAlerts')) {
            const alertsStore = db.createObjectStore('securityAlerts', { keyPath: 'id' });
            alertsStore.createIndex('timestamp', 'timestamp', { unique: false });
            alertsStore.createIndex('severity', 'severity', { unique: false });
            alertsStore.createIndex('type', 'type', { unique: false });
        }

        // GPS tracking store
        if (!db.objectStoreNames.contains('gpsTracking')) {
            const gpsStore = db.createObjectStore('gpsTracking', { keyPath: 'id' });
            gpsStore.createIndex('vehicleId', 'vehicleId', { unique: false });
            gpsStore.createIndex('timestamp', 'timestamp', { unique: false });
        }
    }

    // Generic CRUD operations
    async add(storeName, data) {
        if (this.useFallback) {
            return this.addToLocalStorage(storeName, data);
        }

        try {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.add(data);

            return new Promise((resolve, reject) => {
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
            });
        } catch (error) {
            console.error('Add operation failed:', error);
            return this.addToLocalStorage(storeName, data);
        }
    }

    async get(storeName, id) {
        if (this.useFallback) {
            return this.getFromLocalStorage(storeName, id);
        }

        try {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.get(id);

            return new Promise((resolve, reject) => {
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
            });
        } catch (error) {
            console.error('Get operation failed:', error);
            return this.getFromLocalStorage(storeName, id);
        }
    }

    async getAll(storeName) {
        if (this.useFallback) {
            return this.getAllFromLocalStorage(storeName);
        }

        try {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.getAll();

            return new Promise((resolve, reject) => {
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
            });
        } catch (error) {
            console.error('GetAll operation failed:', error);
            return this.getAllFromLocalStorage(storeName);
        }
    }

    async update(storeName, data) {
        if (this.useFallback) {
            return this.updateInLocalStorage(storeName, data);
        }

        try {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.put(data);

            return new Promise((resolve, reject) => {
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
            });
        } catch (error) {
            console.error('Update operation failed:', error);
            return this.updateInLocalStorage(storeName, data);
        }
    }

    async delete(storeName, id) {
        if (this.useFallback) {
            return this.deleteFromLocalStorage(storeName, id);
        }

        try {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.delete(id);

            return new Promise((resolve, reject) => {
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
            });
        } catch (error) {
            console.error('Delete operation failed:', error);
            return this.deleteFromLocalStorage(storeName, id);
        }
    }

    // Search with filters
    async search(storeName, filters = {}) {
        const allData = await this.getAll(storeName);
        
        return allData.filter(item => {
            return Object.keys(filters).every(key => {
                if (!filters[key]) return true;
                
                if (typeof filters[key] === 'string') {
                    return item[key] && item[key].toString().toLowerCase().includes(filters[key].toLowerCase());
                }
                
                return item[key] === filters[key];
            });
        });
    }

    // LocalStorage fallback methods
    addToLocalStorage(storeName, data) {
        try {
            const existing = JSON.parse(localStorage.getItem(storeName) || '[]');
            existing.push(data);
            localStorage.setItem(storeName, JSON.stringify(existing));
            return data.id;
        } catch (error) {
            console.error('LocalStorage add failed:', error);
            return null;
        }
    }

    getFromLocalStorage(storeName, id) {
        try {
            const data = JSON.parse(localStorage.getItem(storeName) || '[]');
            return data.find(item => item.id === id);
        } catch (error) {
            console.error('LocalStorage get failed:', error);
            return null;
        }
    }

    getAllFromLocalStorage(storeName) {
        try {
            return JSON.parse(localStorage.getItem(storeName) || '[]');
        } catch (error) {
            console.error('LocalStorage getAll failed:', error);
            return [];
        }
    }

    updateInLocalStorage(storeName, data) {
        try {
            const existing = JSON.parse(localStorage.getItem(storeName) || '[]');
            const index = existing.findIndex(item => item.id === data.id);
            
            if (index !== -1) {
                existing[index] = data;
            } else {
                existing.push(data);
            }
            
            localStorage.setItem(storeName, JSON.stringify(existing));
            return data.id;
        } catch (error) {
            console.error('LocalStorage update failed:', error);
            return null;
        }
    }

    deleteFromLocalStorage(storeName, id) {
        try {
            const existing = JSON.parse(localStorage.getItem(storeName) || '[]');
            const filtered = existing.filter(item => item.id !== id);
            localStorage.setItem(storeName, JSON.stringify(filtered));
            return true;
        } catch (error) {
            console.error('LocalStorage delete failed:', error);
            return false;
        }
    }

    // Backup and restore
    async backup() {
        const backup = {
            timestamp: new Date().toISOString(),
            version: this.version,
            data: {}
        };

        const stores = ['users', 'recordings', 'accessLogs', 'cameraSettings', 'systemSettings', 'securityAlerts', 'gpsTracking'];
        
        for (const store of stores) {
            backup.data[store] = await this.getAll(store);
        }

        return backup;
    }

    async restore(backupData) {
        try {
            for (const [storeName, data] of Object.entries(backupData.data)) {
                // Clear existing data
                const allItems = await this.getAll(storeName);
                for (const item of allItems) {
                    await this.delete(storeName, item.id);
                }

                // Restore data
                for (const item of data) {
                    await this.add(storeName, item);
                }
            }
            return true;
        } catch (error) {
            console.error('Restore failed:', error);
            return false;
        }
    }

    // Export data as JSON
    async exportData() {
        const backup = await this.backup();
        const dataStr = JSON.stringify(backup, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `eg_bank_backup_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        URL.revokeObjectURL(url);
    }

    // Import data from JSON
    async importData(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = async (e) => {
                try {
                    const backupData = JSON.parse(e.target.result);
                    const success = await this.restore(backupData);
                    resolve(success);
                } catch (error) {
                    reject(error);
                }
            };
            
            reader.onerror = () => reject(reader.error);
            reader.readAsText(file);
        });
    }

    // Get database statistics
    async getStatistics() {
        const stats = {
            users: 0,
            recordings: 0,
            accessLogs: 0,
            cameraSettings: 0,
            securityAlerts: 0,
            gpsTracking: 0,
            totalSize: 0
        };

        const stores = Object.keys(stats);
        
        for (const store of stores) {
            const data = await this.getAll(store);
            stats[store] = data.length;
        }

        // Calculate approximate size
        const allData = await this.backup();
        stats.totalSize = new Blob([JSON.stringify(allData)]).size;

        return stats;
    }

    // Clean old data
    async cleanOldData(days = 30) {
        const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
        let deletedCount = 0;

        // Clean old recordings
        const recordings = await this.getAll('recordings');
        for (const recording of recordings) {
            if (new Date(recording.startTime) < cutoffDate) {
                await this.delete('recordings', recording.id);
                deletedCount++;
            }
        }

        // Clean old access logs
        const accessLogs = await this.getAll('accessLogs');
        for (const log of accessLogs) {
            if (new Date(log.timestamp) < cutoffDate) {
                await this.delete('accessLogs', log.id);
                deletedCount++;
            }
        }

        // Clean old GPS tracking data
        const gpsData = await this.getAll('gpsTracking');
        for (const gps of gpsData) {
            if (new Date(gps.timestamp) < cutoffDate) {
                await this.delete('gpsTracking', gps.id);
                deletedCount++;
            }
        }

        return deletedCount;
    }
}

// Create global database instance
const dbManager = new DatabaseManager();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DatabaseManager;
}

console.log('Advanced Database Manager Loaded Successfully');
