// Access Control System JavaScript
// نظام التحكم في الوصول

// Current language state
let currentLanguage = 'ar';

// User permissions and authentication
const currentUser = {
    id: 1,
    name: 'عبد الفتاح محمود',
    permissions: ['door_1', 'door_2', 'door_3', 'door_4', 'door_5', 'door_6'], // User has access to these doors
    role: 'admin'
};

// Door data simulation (would normally come from EntraPass API)
let doors = [
    {
        id: 'door_1',
        name: { ar: 'الباب الرئيسي', en: 'Main Entrance' },
        location: { ar: 'المدخل الرئيسي', en: 'Main Lobby' },
        status: 'closed',
        lastAccess: '2024-01-15 14:30:25',
        accessCount: 45
    },
    {
        id: 'door_2',
        name: { ar: 'باب الخزينة', en: 'Vault Door' },
        location: { ar: 'منطقة الخزينة', en: 'Vault Area' },
        status: 'locked',
        lastAccess: '2024-01-15 09:15:10',
        accessCount: 12
    },
    {
        id: 'door_3',
        name: { ar: 'باب المكاتب', en: 'Office Door' },
        location: { ar: 'الطابق الثاني', en: 'Second Floor' },
        status: 'open',
        lastAccess: '2024-01-15 15:45:30',
        accessCount: 78
    },
    {
        id: 'door_4',
        name: { ar: 'باب الطوارئ', en: 'Emergency Exit' },
        location: { ar: 'المخرج الخلفي', en: 'Back Exit' },
        status: 'closed',
        lastAccess: '2024-01-15 08:20:15',
        accessCount: 5
    },
    {
        id: 'door_5',
        name: { ar: 'باب الأرشيف', en: 'Archive Door' },
        location: { ar: 'قسم الأرشيف', en: 'Archive Department' },
        status: 'open',
        lastAccess: '2024-01-15 16:10:45',
        accessCount: 23
    },
    {
        id: 'door_6',
        name: { ar: 'باب الاجتماعات', en: 'Meeting Room' },
        location: { ar: 'غرفة الاجتماعات', en: 'Conference Room' },
        status: 'open',
        lastAccess: '2024-01-15 13:25:20',
        accessCount: 34
    },
    {
        id: 'door_7',
        name: { ar: 'باب الخدمات', en: 'Service Door' },
        location: { ar: 'منطقة الخدمات', en: 'Service Area' },
        status: 'closed',
        lastAccess: '2024-01-15 11:40:30',
        accessCount: 18
    },
    {
        id: 'door_8',
        name: { ar: 'باب الحراسة', en: 'Security Door' },
        location: { ar: 'غرفة الحراسة', en: 'Security Room' },
        status: 'closed',
        lastAccess: '2024-01-15 12:55:10',
        accessCount: 67
    }
];

// Access log data
let accessLog = [
    {
        doorId: 'door_3',
        action: { ar: 'فتح الباب', en: 'Door Opened' },
        user: 'عبد الفتاح محمود',
        timestamp: '2024-01-15 15:45:30',
        status: 'success'
    },
    {
        doorId: 'door_5',
        action: { ar: 'فتح الباب', en: 'Door Opened' },
        user: 'أحمد محمد',
        timestamp: '2024-01-15 16:10:45',
        status: 'success'
    },
    {
        doorId: 'door_1',
        action: { ar: 'محاولة دخول غير مصرح', en: 'Unauthorized Access Attempt' },
        user: 'غير معروف',
        timestamp: '2024-01-15 14:30:25',
        status: 'failed'
    },
    {
        doorId: 'door_6',
        action: { ar: 'فتح الباب', en: 'Door Opened' },
        user: 'سارة أحمد',
        timestamp: '2024-01-15 13:25:20',
        status: 'success'
    },
    {
        doorId: 'door_8',
        action: { ar: 'إغلاق الباب', en: 'Door Closed' },
        user: 'محمد علي',
        timestamp: '2024-01-15 12:55:10',
        status: 'success'
    }
];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    renderDoors();
    renderAccessLog();
    updateStatistics();

    // Simulate real-time updates
    setInterval(simulateRealTimeUpdates, 30000); // Update every 30 seconds

    // Check for unauthorized access attempts
    setTimeout(simulateSecurityAlert, 10000); // Show alert after 10 seconds
});

// Initialize application
function initializeApp() {
    console.log('Access Control System Initialized');
    console.log('Current User:', currentUser.name);
    console.log('User Permissions:', currentUser.permissions);
}

// Render doors in the grid
function renderDoors() {
    const doorGrid = document.getElementById('doorGrid');
    doorGrid.innerHTML = '';

    doors.forEach(door => {
        const hasAccess = currentUser.permissions.includes(door.id);
        const doorCard = createDoorCard(door, hasAccess);
        doorGrid.appendChild(doorCard);
    });
}

// Create individual door card
function createDoorCard(door, hasAccess) {
    const card = document.createElement('div');
    card.className = 'door-card';

    const statusClass = `status-${door.status}`;
    const statusText = {
        ar: {
            open: 'مفتوح',
            closed: 'مغلق',
            locked: 'مقفل يدوياً'
        },
        en: {
            open: 'Open',
            closed: 'Closed',
            locked: 'Manually Locked'
        }
    };

    card.innerHTML = `
        <h3 style="color: white; margin-bottom: 10px;">${door.name[currentLanguage]}</h3>
        <p style="color: #a0a0a0; margin-bottom: 15px;">${door.location[currentLanguage]}</p>

        <div class="door-status">
            <span class="status-indicator ${statusClass}"></span>
            <span style="color: white;">${statusText[currentLanguage][door.status]}</span>
        </div>

        <div style="color: #a0a0a0; font-size: 12px; margin-bottom: 15px;">
            <div data-en="Last Access: ${door.lastAccess}" data-ar="آخر دخول: ${door.lastAccess}">
                ${currentLanguage === 'ar' ? 'آخر دخول: ' + door.lastAccess : 'Last Access: ' + door.lastAccess}
            </div>
            <div data-en="Access Count: ${door.accessCount}" data-ar="عدد مرات الدخول: ${door.accessCount}">
                ${currentLanguage === 'ar' ? 'عدد مرات الدخول: ' + door.accessCount : 'Access Count: ' + door.accessCount}
            </div>
        </div>

        <div class="door-controls">
            <button class="door-btn btn-open"
                    onclick="toggleDoor('${door.id}')"
                    ${!hasAccess || door.status === 'locked' ? 'disabled' : ''}
                    data-en="${door.status === 'open' ? 'Close Door' : 'Open Door'}"
                    data-ar="${door.status === 'open' ? 'إغلاق الباب' : 'فتح الباب'}">
                ${door.status === 'open' ?
                    (currentLanguage === 'ar' ? 'إغلاق الباب' : 'Close Door') :
                    (currentLanguage === 'ar' ? 'فتح الباب' : 'Open Door')}
            </button>
        </div>

        ${!hasAccess ? `<div style="color: #f56565; font-size: 12px; margin-top: 10px; text-align: center;">
            ${currentLanguage === 'ar' ? 'ليس لديك صلاحية للوصول' : 'No Access Permission'}
        </div>` : ''}
    `;

    return card;
}

// Toggle door status (open/close)
function toggleDoor(doorId) {
    const door = doors.find(d => d.id === doorId);
    if (!door) return;

    // Check user permissions
    if (!currentUser.permissions.includes(doorId)) {
        showAlert(
            currentLanguage === 'ar' ?
            'ليس لديك صلاحية للوصول إلى هذا الباب!' :
            'You do not have permission to access this door!'
        );
        return;
    }

    // Cannot toggle locked doors
    if (door.status === 'locked') {
        showAlert(
            currentLanguage === 'ar' ?
            'هذا الباب مقفل يدوياً ولا يمكن التحكم فيه عن بُعد!' :
            'This door is manually locked and cannot be controlled remotely!'
        );
        return;
    }

    // Simulate API call to EntraPass system
    simulateAPICall(doorId, door.status === 'open' ? 'close' : 'open')
        .then(response => {
            if (response.success) {
                // Update door status
                door.status = door.status === 'open' ? 'closed' : 'open';
                door.lastAccess = new Date().toLocaleString('sv-SE').replace('T', ' ').substring(0, 19);
                door.accessCount++;

                // Add to access log
                addToAccessLog(doorId, door.status === 'open' ? 'opened' : 'closed', 'success');

                // Re-render doors and update statistics
                renderDoors();
                updateStatistics();
                renderAccessLog();

                // Show success message
                showNotification(
                    currentLanguage === 'ar' ?
                    `تم ${door.status === 'open' ? 'فتح' : 'إغلاق'} ${door.name[currentLanguage]} بنجاح` :
                    `${door.name[currentLanguage]} ${door.status === 'open' ? 'opened' : 'closed'} successfully`
                );
            } else {
                throw new Error('API call failed');
            }
        })
        .catch(error => {
            console.error('Error toggling door:', error);
            showAlert(
                currentLanguage === 'ar' ?
                'فشل في التحكم بالباب. يرجى المحاولة مرة أخرى.' :
                'Failed to control door. Please try again.'
            );
        });
}

// Simulate API call to EntraPass system
function simulateAPICall(doorId, action) {
    return new Promise((resolve, reject) => {
        // Simulate network delay
        setTimeout(() => {
            // Simulate 95% success rate
            if (Math.random() > 0.05) {
                resolve({
                    success: true,
                    doorId: doorId,
                    action: action,
                    timestamp: new Date().toISOString()
                });
            } else {
                reject(new Error('Network error or door malfunction'));
            }
        }, 1000 + Math.random() * 2000); // 1-3 second delay
    });
}

// Add entry to access log
function addToAccessLog(doorId, action, status) {
    const door = doors.find(d => d.id === doorId);
    const logEntry = {
        doorId: doorId,
        action: {
            ar: action === 'opened' ? 'فتح الباب' : 'إغلاق الباب',
            en: action === 'opened' ? 'Door Opened' : 'Door Closed'
        },
        user: currentUser.name,
        timestamp: new Date().toLocaleString('sv-SE').replace('T', ' ').substring(0, 19),
        status: status
    };

    accessLog.unshift(logEntry); // Add to beginning of array

    // Keep only last 10 entries
    if (accessLog.length > 10) {
        accessLog = accessLog.slice(0, 10);
    }
}

// Render access log
function renderAccessLog() {
    const logContainer = document.getElementById('accessLog');
    logContainer.innerHTML = '';

    if (accessLog.length === 0) {
        logContainer.innerHTML = `<p style="color: #a0a0a0; text-align: center;">
            ${currentLanguage === 'ar' ? 'لا توجد سجلات وصول' : 'No access logs available'}
        </p>`;
        return;
    }

    accessLog.forEach(entry => {
        const door = doors.find(d => d.id === entry.doorId);
        const logElement = document.createElement('div');
        logElement.className = 'log-entry';

        logElement.innerHTML = `
            <div>
                <div style="font-weight: 500;">
                    ${door ? door.name[currentLanguage] : entry.doorId}
                </div>
                <div style="color: #a0a0a0; font-size: 12px;">
                    ${entry.action[currentLanguage]} - ${entry.user}
                </div>
            </div>
            <div class="log-time">
                ${entry.timestamp}
            </div>
        `;

        // Add status indicator
        if (entry.status === 'failed') {
            logElement.style.borderLeft = '3px solid #f56565';
        } else {
            logElement.style.borderLeft = '3px solid #48bb78';
        }

        logContainer.appendChild(logElement);
    });
}

// Update statistics
function updateStatistics() {
    const stats = {
        total: doors.length,
        open: doors.filter(d => d.status === 'open').length,
        closed: doors.filter(d => d.status === 'closed').length,
        locked: doors.filter(d => d.status === 'locked').length
    };

    document.getElementById('totalDoors').textContent = stats.total;
    document.getElementById('openDoors').textContent = stats.open;
    document.getElementById('closedDoors').textContent = stats.closed;
    document.getElementById('lockedDoors').textContent = stats.locked;
}

// Language toggle functionality
function toggleLanguage() {
    currentLanguage = currentLanguage === 'ar' ? 'en' : 'ar';

    // Update language toggle button
    const langToggle = document.getElementById('langToggle');
    langToggle.textContent = currentLanguage === 'ar' ? 'English' : 'العربية';

    // Update document direction
    document.documentElement.lang = currentLanguage;
    document.documentElement.dir = currentLanguage === 'ar' ? 'rtl' : 'ltr';

    // Update all elements with language attributes
    updateLanguageElements();

    // Re-render dynamic content
    renderDoors();
    renderAccessLog();
}

// Update elements with language attributes
function updateLanguageElements() {
    const elements = document.querySelectorAll('[data-ar][data-en]');
    elements.forEach(element => {
        if (element.hasAttribute(`data-${currentLanguage}`)) {
            element.textContent = element.getAttribute(`data-${currentLanguage}`);
        }
    });
}

// Show alert message
function showAlert(message) {
    const alertPanel = document.getElementById('alertPanel');
    const alertMessage = document.getElementById('alertMessage');

    alertMessage.textContent = message;
    alertPanel.classList.add('show');

    // Hide alert after 5 seconds
    setTimeout(() => {
        alertPanel.classList.remove('show');
    }, 5000);
}

// Show notification (success message)
function showNotification(message) {
    // Create notification element
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: #48bb78;
        color: white;
        padding: 15px 25px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 1001;
        font-weight: 500;
        animation: slideDown 0.3s ease;
    `;

    notification.textContent = message;
    document.body.appendChild(notification);

    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideUp 0.3s ease';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Simulate real-time updates
function simulateRealTimeUpdates() {
    // Randomly update door status
    const randomDoor = doors[Math.floor(Math.random() * doors.length)];

    if (Math.random() > 0.7) { // 30% chance of status change
        const oldStatus = randomDoor.status;

        if (randomDoor.status === 'open') {
            randomDoor.status = 'closed';
        } else if (randomDoor.status === 'closed' && Math.random() > 0.5) {
            randomDoor.status = 'open';
        }

        if (oldStatus !== randomDoor.status) {
            randomDoor.lastAccess = new Date().toLocaleString('sv-SE').replace('T', ' ').substring(0, 19);

            // Add to access log
            addToAccessLog(
                randomDoor.id,
                randomDoor.status === 'open' ? 'opened' : 'closed',
                'success'
            );

            // Update UI
            renderDoors();
            updateStatistics();
            renderAccessLog();

            console.log(`Real-time update: ${randomDoor.name.en} is now ${randomDoor.status}`);
        }
    }
}

// Simulate security alert
function simulateSecurityAlert() {
    const alertMessages = {
        ar: [
            'تم رصد محاولة دخول غير مصرح بها في الباب الرئيسي',
            'تنبيه: فتح باب الطوارئ بدون تصريح',
            'محاولة دخول متكررة فاشلة في باب الخزينة'
        ],
        en: [
            'Unauthorized access attempt detected at Main Entrance',
            'Alert: Emergency exit opened without authorization',
            'Multiple failed access attempts at Vault Door'
        ]
    };

    const randomAlert = alertMessages[currentLanguage][Math.floor(Math.random() * alertMessages[currentLanguage].length)];
    showAlert(randomAlert);

    // Add failed attempt to log
    addToAccessLog('door_1', 'unauthorized_attempt', 'failed');
    renderAccessLog();

    // Schedule next alert (random interval between 30-120 seconds)
    setTimeout(simulateSecurityAlert, (30 + Math.random() * 90) * 1000);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideDown {
        from { opacity: 0; transform: translateX(-50%) translateY(-20px); }
        to { opacity: 1; transform: translateX(-50%) translateY(0); }
    }

    @keyframes slideUp {
        from { opacity: 1; transform: translateX(-50%) translateY(0); }
        to { opacity: 0; transform: translateX(-50%) translateY(-20px); }
    }
`;
document.head.appendChild(style);

// Export functions for testing (if needed)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        toggleDoor,
        toggleLanguage,
        simulateAPICall,
        updateStatistics
    };
}