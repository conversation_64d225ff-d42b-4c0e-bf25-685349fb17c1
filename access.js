// Access Control Main Page JavaScript
// الصفحة الرئيسية لنظام التحكم في الوصول

// Function to open pages
function openPage(pageName) {
    // Add loading effect
    showLoadingEffect();

    // Simulate page loading delay
    setTimeout(() => {
        window.location.href = pageName;
    }, 500);
}

// Show loading effect
function showLoadingEffect() {
    const loadingOverlay = document.createElement('div');
    loadingOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(44, 44, 44, 0.9);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        backdrop-filter: blur(5px);
    `;

    const loader = document.createElement('div');
    loader.style.cssText = `
        border: 8px solid #555555;
        border-top: 8px solid #1a365d;
        border-radius: 50%;
        width: 60px;
        height: 60px;
        animation: spin 1s linear infinite;
    `;

    loadingOverlay.appendChild(loader);
    document.body.appendChild(loadingOverlay);
}

// Add CSS animation for loader
const style = document.createElement('style');
style.textContent = `
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    console.log('Access Control Main Page Loaded');

    // Add hover effects to cards
    const cards = document.querySelectorAll('.access-button-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Add click sound effect (optional)
    cards.forEach(card => {
        card.addEventListener('click', function() {
            // Create a subtle click feedback
            this.style.transform = 'translateY(-3px) scale(0.98)';
            setTimeout(() => {
                this.style.transform = 'translateY(-5px) scale(1.02)';
            }, 100);
        });
    });
});

console.log('Access Control Main Page JavaScript Loaded Successfully');