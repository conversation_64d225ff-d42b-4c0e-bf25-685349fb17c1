// User Permissions Management JavaScript
// إدارة صلاحيات المستخدمين

// Users data with permissions
let users = [
    {
        id: 1,
        name: 'عبد الفتاح محمود',
        role: 'مدير النظام',
        permissions: {
            door_1: true,
            door_2: true,
            door_3: true,
            door_4: true,
            door_5: true,
            door_6: true,
            door_7: true,
            door_8: true
        }
    },
    {
        id: 2,
        name: 'أحمد محمد علي',
        role: 'موظف أمن',
        permissions: {
            door_1: true,
            door_2: false,
            door_3: true,
            door_4: true,
            door_5: false,
            door_6: true,
            door_7: false,
            door_8: true
        }
    },
    {
        id: 3,
        name: 'سارة أحمد حسن',
        role: 'موظف إداري',
        permissions: {
            door_1: true,
            door_2: false,
            door_3: true,
            door_4: false,
            door_5: true,
            door_6: true,
            door_7: false,
            door_8: false
        }
    },
    {
        id: 4,
        name: 'محمد علي حسن',
        role: 'فني صيانة',
        permissions: {
            door_1: true,
            door_2: false,
            door_3: false,
            door_4: true,
            door_5: false,
            door_6: false,
            door_7: true,
            door_8: false
        }
    },
    {
        id: 5,
        name: 'فاطمة محمود',
        role: 'محاسب',
        permissions: {
            door_1: true,
            door_2: false,
            door_3: true,
            door_4: false,
            door_5: true,
            door_6: false,
            door_7: false,
            door_8: false
        }
    },
    {
        id: 6,
        name: 'خالد أحمد',
        role: 'مشرف عام',
        permissions: {
            door_1: true,
            door_2: true,
            door_3: true,
            door_4: true,
            door_5: true,
            door_6: true,
            door_7: false,
            door_8: true
        }
    }
];

// Door names mapping
const doorNames = {
    door_1: 'الباب الرئيسي',
    door_2: 'باب الخزينة',
    door_3: 'باب المكاتب',
    door_4: 'باب الطوارئ',
    door_5: 'باب الأرشيف',
    door_6: 'باب الاجتماعات',
    door_7: 'باب الخدمات',
    door_8: 'باب الحراسة'
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    renderUsers();
});

// Initialize application
function initializeApp() {
    console.log('User Permissions Management System Initialized');
    console.log('Total Users:', users.length);
}

// Render users permissions
function renderUsers() {
    const permissionsGrid = document.getElementById('permissionsGrid');
    permissionsGrid.innerHTML = '';
    
    users.forEach(user => {
        const userCard = createUserPermissionCard(user);
        permissionsGrid.appendChild(userCard);
    });
}

// Create user permission card
function createUserPermissionCard(user) {
    const card = document.createElement('div');
    card.className = 'user-permission-card';
    
    // Count active permissions
    const activePermissions = Object.values(user.permissions).filter(p => p).length;
    const totalPermissions = Object.keys(user.permissions).length;
    
    card.innerHTML = `
        <div class="user-header">
            <div class="user-name">${user.name}</div>
            <div class="user-role">${user.role}</div>
        </div>
        
        <div style="color: #a0a0a0; font-size: 14px; margin-bottom: 15px;">
            الصلاحيات النشطة: ${activePermissions}/${totalPermissions}
        </div>
        
        <div class="permissions-list">
            ${Object.entries(user.permissions).map(([doorId, hasPermission]) => `
                <div class="permission-item">
                    <div class="permission-name">${doorNames[doorId]}</div>
                    <div class="permission-toggle ${hasPermission ? 'active' : ''}" 
                         onclick="togglePermission(${user.id}, '${doorId}')"></div>
                </div>
            `).join('')}
        </div>
        
        <div style="display: flex; gap: 10px; margin-top: 15px;">
            <button class="action-btn btn-edit" onclick="editUser(${user.id})" style="flex: 1; padding: 8px; background: #1a365d; color: white; border: none; border-radius: 4px; cursor: pointer;">
                تعديل المستخدم
            </button>
            <button class="action-btn btn-delete" onclick="deleteUser(${user.id})" style="flex: 1; padding: 8px; background: #e53e3e; color: white; border: none; border-radius: 4px; cursor: pointer;">
                حذف المستخدم
            </button>
        </div>
    `;
    
    return card;
}

// Toggle permission for user
function togglePermission(userId, doorId) {
    const user = users.find(u => u.id === userId);
    if (!user) return;
    
    // Toggle permission
    user.permissions[doorId] = !user.permissions[doorId];
    
    // Re-render users
    renderUsers();
    
    // Show notification
    const doorName = doorNames[doorId];
    const action = user.permissions[doorId] ? 'منح' : 'إلغاء';
    showNotification(`تم ${action} صلاحية ${doorName} للمستخدم ${user.name}`);
    
    // Save to localStorage
    saveUsers();
}

// Search users
function searchUsers() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
    
    if (searchTerm === '') {
        renderUsers();
        return;
    }
    
    const filteredUsers = users.filter(user => 
        user.name.toLowerCase().includes(searchTerm) ||
        user.role.toLowerCase().includes(searchTerm)
    );
    
    const permissionsGrid = document.getElementById('permissionsGrid');
    permissionsGrid.innerHTML = '';
    
    if (filteredUsers.length === 0) {
        permissionsGrid.innerHTML = `
            <div style="text-align: center; color: #a0a0a0; padding: 40px; grid-column: 1 / -1;">
                <h3>لا توجد نتائج</h3>
                <p>لم يتم العثور على مستخدمين يطابقون البحث</p>
            </div>
        `;
        return;
    }
    
    filteredUsers.forEach(user => {
        const userCard = createUserPermissionCard(user);
        permissionsGrid.appendChild(userCard);
    });
}

// Add new user
function addNewUser() {
    const name = prompt('اسم المستخدم الجديد:');
    const role = prompt('دور المستخدم:');
    
    if (name && role) {
        const newUser = {
            id: users.length + 1,
            name: name,
            role: role,
            permissions: {
                door_1: false,
                door_2: false,
                door_3: false,
                door_4: false,
                door_5: false,
                door_6: false,
                door_7: false,
                door_8: false
            }
        };
        
        users.push(newUser);
        renderUsers();
        saveUsers();
        showNotification(`تم إضافة المستخدم ${name} بنجاح`);
    }
}

// Edit user
function editUser(userId) {
    const user = users.find(u => u.id === userId);
    if (!user) return;
    
    const newName = prompt('اسم المستخدم:', user.name);
    const newRole = prompt('دور المستخدم:', user.role);
    
    if (newName && newRole) {
        user.name = newName;
        user.role = newRole;
        renderUsers();
        saveUsers();
        showNotification(`تم تحديث بيانات المستخدم بنجاح`);
    }
}

// Delete user
function deleteUser(userId) {
    const user = users.find(u => u.id === userId);
    if (!user) return;
    
    if (confirm(`هل أنت متأكد من حذف المستخدم ${user.name}؟`)) {
        const index = users.findIndex(u => u.id === userId);
        if (index !== -1) {
            users.splice(index, 1);
            renderUsers();
            saveUsers();
            showNotification(`تم حذف المستخدم ${user.name} بنجاح`);
        }
    }
}

// Save users to localStorage
function saveUsers() {
    localStorage.setItem('users_permissions', JSON.stringify(users));
}

// Load users from localStorage
function loadUsers() {
    const saved = localStorage.getItem('users_permissions');
    if (saved) {
        users = JSON.parse(saved);
    }
}

// Show notification
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    const bgColor = type === 'success' ? '#48bb78' : '#f56565';
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: ${bgColor};
        color: white;
        padding: 15px 25px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 1001;
        font-weight: 500;
        animation: slideDown 0.3s ease;
    `;
    
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideUp 0.3s ease';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideDown {
        from { opacity: 0; transform: translateX(-50%) translateY(-20px); }
        to { opacity: 1; transform: translateX(-50%) translateY(0); }
    }
    
    @keyframes slideUp {
        from { opacity: 1; transform: translateX(-50%) translateY(0); }
        to { opacity: 0; transform: translateX(-50%) translateY(-20px); }
    }
`;
document.head.appendChild(style);

// Load users on startup
loadUsers();

console.log('User Permissions Management JavaScript Loaded Successfully');
